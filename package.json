{"name": "crm-panel", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@fullcalendar/core": "^6.1.4", "@fullcalendar/daygrid": "^6.1.4", "@fullcalendar/interaction": "^6.1.4", "@fullcalendar/list": "^6.1.4", "@fullcalendar/react": "^6.1.4", "@fullcalendar/timegrid": "^6.1.4", "@headlessui/react": "^1.7.4", "@hookform/resolvers": "^2.9.10", "@reduxjs/toolkit": "^1.9.0", "@rollup/plugin-replace": "^5.0.2", "@south-paw/react-vector-maps": "^3.2.0", "@svg-maps/world": "^1.0.1", "@tippyjs/react": "^4.2.6", "@vitejs/plugin-react-refresh": "^1.3.6", "apexcharts": "^3.36.3", "chart.js": "^4.2.0", "cleave.js": "^1.6.0", "d3-array": "^3.2.2", "date-fns": "^3.6.0", "dateformat": "^5.0.3", "dayjs": "^1.11.7", "formik": "^2.4.6", "framer-motion": "^10.12.12", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "leaflet": "^1.9.3", "localforage": "^1.10.0", "match-sorter": "^6.3.1", "quill": "^2.0.2", "quill-better-table": "^1.2.10", "quill-table-ui": "^1.0.7", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^4.0.0", "react-chartjs-2": "^5.2.0", "react-collapse": "^5.1.1", "react-datepicker": "^7.3.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-flatpickr": "^3.10.13", "react-hook-form": "^7.39.5", "react-leaflet": "^4.2.0", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-router-dom": "^6.4.3", "react-select": "^5.7.0", "react-switch": "^7.0.0", "react-table": "^7.8.0", "react-tailwindcss-datepicker": "^1.4.2", "react-toastify": "^9.1.1", "react-transition-group": "^4.4.5", "recharts": "^2.3.2", "sass": "^1.56.1", "simplebar-react": "^2.4.3", "sort-by": "^0.0.2", "suneditor": "^2.47.0", "suneditor-react": "^3.6.1", "sweetalert2": "^11.6.13", "swiper": "^8.4.5", "uuidv4": "^6.2.13", "xlsx": "^0.18.5", "yarn": "^1.22.19", "yup": "^0.32.11"}, "devDependencies": {"@faker-js/faker": "^8.0.2", "@iconify/react": "^5.0.1", "@types/react": "^18.0.24", "@types/react-dom": "^18.0.8", "@vitejs/plugin-react": "^2.2.0", "autoprefixer": "^10.4.13", "miragejs": "^0.1.47", "postcss": "^8.4.19", "tailwindcss": "^3.2.4", "vite": "^3.2.3"}}