/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/react-tailwindcss-datepicker/dist/index.esm.js",
  ],
  mode: "jit",
  darkMode: "class",
  theme: {
    container: {
      center: true,
      padding: {
        DEFAULT: "15px",
        sm: "15px",
        lg: "15px",
        xl: "0",
        "2xl": "0",
      },
      screens: {
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        "2xl": "1280px",
      },
    },
    extend: {
      colors: {
        primaryColor: '#0075BD',
        secondaryColor: '#47B9FF',
        primary: {
          50: "#F6F8FF",
          100: "#EDF0FF",
          200: "#D1DAFE",
          300: "#B4C2FD",
          400: "#8092FF",
          500: "#4669fa",
          600: "#3F5EDF",
          700: "#2A3F96",
          800: "#203071",
          900: "#151F49",
        },
        secondary: {
          50: "#F9FAFB",
          100: "#F4F5F7",
          200: "#E5E7EB",
          300: "#D2D6DC",
          400: "#9FA6B2",
          500: "#A0AEC0",
          600: "#475569",
          700: "#334155",
          800: "#1E293B",
          900: "#0F172A",
        },
        danger: {
          50: "#FFF7F7",
          100: "#FEEFEF",
          200: "#FCD6D7",
          300: "#FABBBD",
          400: "#F68B8D",
          500: "#F1595C",
          600: "#D75052",
          700: "#913638",
          800: "#6D292A",
          900: "#461A1B",
        },
        black: {
          50: "#F9FAFB",
          100: "#F4F5F7",
          200: "#E5E7EB",
          300: "#D2D6DC",
          400: "#9FA6B2",
          500: "#111112",
          600: "#475569",
          700: "#334155",
          800: "#1E293B",
          900: "#0F172A",
        },
        warning: {
          50: "#FFFAF8",
          100: "#FFF4F1",
          200: "#FEE4DA",
          300: "#FDD2C3",
          400: "#FCB298",
          500: "#FA916B",
          600: "#DF8260",
          700: "#965741",
          800: "#714231",
          900: "#492B20",
        },
        info: {
          50: "#F3FEFF",
          100: "#E7FEFF",
          200: "#C5FDFF",
          300: "#A3FCFF",
          400: "#5FF9FF",
          500: "#0CE7FA",
          600: "#00B8D4",
          700: "#007A8D",
          800: "#005E67",
          900: "#003F42",
        },
        success: {
          50: "#F3FEF8",
          100: "#E7FDF1",
          200: "#C5FBE3",
          300: "#A3F9D5",
          400: "#5FF5B1",
          500: "#50C793",
          600: "#3F9A7A",
          700: "#2E6D61",
          800: "#1F4B47",
          900: "#0F2A2E",
        },
        gray: {
          50: "#F9FAFB",
          100: "#F4F5F7",
          200: "#E5E7EB",
          300: "#D2D6DC",
          400: "#9FA6B2",
          500: "#68768A",
          600: "#475569",
          700: "#334155",
          800: "#1E293B",
          900: "#0F172A",
        },
        //dashboard color..
        downriver: {
          50: '#eff3ff',
          100: '#dbe3fe',
          200: '#bfcdfe',
          300: '#93abfd',
          400: '#6083fa',
          500: '#3b66f6',
          600: '#2552eb',
          700: '#1d48d8',
          800: '#1e3faf',
          900: '#1e378a',
          950: '#172554',
        },
        fuscousGray: {
          50: '#f6f6f6',
          100: '#e7e7e7',
          200: '#d1d1d1',
          300: '#b0b0b0',
          400: '#888888',
          500: '#6d6d6d',
          600: '#5d5d5d',
          700: '#4e4e4e',
          800: '#454545',
          900: '#3d3d3d',
          950: '#262626',
        },
        halfDutchWhite: {
          50: '#fff8e0',
          100: '#fff3c0',
          200: '#ffe485',
          300: '#ffcc3f',
          400: '#ffb10b',
          500: '#f49700',
          600: '#d37100',
          700: '#a84c00',
          800: '#8a3c09',
          900: '#75310e',
          950: '#451703',
        },
        chalky: {
          50: '#fdf9ed',
          100: '#faedcb',
          200: '#f4da91',
          300: '#efc35a',
          400: '#ebad34',
          500: '#e38d1d',
          600: '#c96b16',
          700: '#a74c16',
          800: '#883b18',
          900: '#703117',
          950: '#401808',
        },
        selago: {
          50: '#eef1ff',
          100: '#e0e5ff',
          200: '#c7cffe',
          300: '#a5affc',
          400: '#8185f8',
          500: '#6863f1',
          600: '#5846e5',
          700: '#4c38ca',
          800: '#3e30a3',
          900: '#362e81',
          950: '#211b4b',
        },
        portage: {
          50: '#edf5ff',
          100: '#dfebff',
          200: '#c5d9ff',
          300: '#a2bfff',
          400: '#819efc',
          500: '#5e78f6',
          600: '#4150ea',
          700: '#333dcf',
          800: '#2c36a7',
          900: '#2b3584',
          950: '#191c4d',
        },
        seashellPeach: {
          50: '#fff2e9',
          100: '#ffe7d5',
          200: '#fecbaa',
          300: '#fda674',
          400: '#fb763c',
          500: '#f95216',
          600: '#ea370c',
          700: '#c2260c',
          800: '#9a2012',
          900: '#7c1d12',
          950: '#430b07',
        },
        monaLisa: {
          50: '#fef3f2',
          100: '#ffe4e1',
          200: '#ffcec8',
          300: '#ff9f94',
          400: '#fd7b6c',
          500: '#f5513e',
          600: '#e23420',
          700: '#be2817',
          800: '#9d2517',
          900: '#82251a',
          950: '#470e08',
        },
        frostee: {
          50: '#effaf2',
          100: '#e2f5e6',
          200: '#b6e4c2',
          300: '#86cf9e',
          400: '#53b476',
          500: '#319859',
          600: '#227946',
          700: '#1b613a',
          800: '#184d2f',
          900: '#144028',
          950: '#0a2417',
        },
        celadon: {
          50: '#f3faf4',
          100: '#e3f5e7',
          200: '#c8ead0',
          300: '#97d6a6',
          400: '#6abe7f',
          500: '#45a25b',
          600: '#358448',
          700: '#2c693b',
          800: '#275432',
          900: '#22452c',
          950: '#0e2515',
        },









      },

      fontFamily: {
        inter: ["Inter", "sans-serif"],
        // poppins: ["Poppins", "sans-serif"],
      },
      boxShadow: {
        base: "0px 0px 1px rgba(40, 41, 61, 0.08), 0px 0.5px 2px rgba(96, 97, 112, 0.16)",
        base2:
          "0px 2px 4px rgba(40, 41, 61, 0.04), 0px 8px 16px rgba(96, 97, 112, 0.16)",
        base3: "16px 10px 40px rgba(15, 23, 42, 0.22)",
        deep: "-2px 0px 8px rgba(0, 0, 0, 0.16)",
        dropdown: "0px 4px 8px rgba(0, 0, 0, 0.08)",

        testi: "0px 4px 24px rgba(0, 0, 0, 0.06)",
        todo: "rgba(235 233 241, 0.6) 0px 3px 10px 0px",
      },
      keyframes: {
        zoom: {
          "0%, 100%": { transform: "scale(0.5)" },
          "50%": { transform: "scale(1)" },
        },
        tada: {
          "0%": { transform: "scale3d(1, 1, 1)" },
          "10%, 20%": {
            transform: "scale3d(1, 1, 0.95) rotate3d(0, 0, 1, -10deg)",
          },
          "30%, 50%, 70%, 90%": {
            transform: "scale3d(1, 1, 1) rotate3d(0, 0, 1, 10deg)",
          },
          "40%, 60%, 80%": {
            transform: "rotate3d(0, 0, 1, -10deg)",
          },
          "100%": { transform: "scale3d(1, 1, 1)" },
        },
      },
      animation: {
        "spin-slow": "spin 3s linear infinite",
        zoom: "zoom 1s ease-in-out infinite",
        tada: "tada 1.5s ease-in-out infinite",
      },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      const newUtilities = {
        '.no-spinner': {
          '-moz-appearance': 'textfield', /* Firefox */
          '-webkit-appearance': 'none', /* Chrome, Safari, Edge */
        },
        '.no-spinner::-webkit-outer-spin-button': {
          '-webkit-appearance': 'none',
          margin: 0,
        },
        '.no-spinner::-webkit-inner-spin-button': {
          '-webkit-appearance': 'none',
          margin: 0,
        },
      };

      addUtilities(newUtilities);
    },
  ],
};
