import * as Yup from "yup";


export const createValidationSchema = (menus,edit) => Yup.object({
    name: Yup.string().required("Name is required"),
    order: Yup.number()
        .required("Order is required")
        .min(0, "Order must be a positive number"),
    is_active: Yup.number().required("Please select if active or inactive"),
    role_ids: Yup.array().min(1, "At least one role must be selected"),
});
export const menuInitialValues = {
    name: "",
    description: "",
    icon: "",
    url: "",
    order: "",
    is_active: 1,
    role_ids: [],
};

export const isActiveOptions = [
    { label: "Active", value: 1 },
    { label: "Inactive", value: 0 },
];