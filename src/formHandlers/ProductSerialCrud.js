import * as Yup from "yup";

export const createProductSerialValidationSchema = () =>
  Yup.object({
    // product_id:
    //   Yup.string()
    //     .required("Name is required") || "",
    // description:
    //   Yup.string()
    //     .min(20, "Minimum 20 & maximum 200 character")
    //     .max(200, "Minimum 20 & maximum 200 character")
    //     .required("Description is required") || "",
    serial_number: Yup.string().required("Serial Number is required") || "",
    // is_active:
    //   Yup.number().required("Please select if active or inactive") || "",
    // sold:
    //   Yup.number().required("Please select if sold or not") || "",
  });

export const productSerialInitialValues = {
  // product_id: "",
  serial_number: "",
  description: "",
  // is_active: "",
  // sold: "",
};

export const isProductSerialActiveOptions = [
  { label: "Active", value: 1 },
  { label: "Inactive", value: 0 },
];

export const isSoldOptions = [
  { label: "Yes", value: 1 },
  { label: "No", value: 0 },
];