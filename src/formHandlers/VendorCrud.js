import * as Yup from "yup";

export const createValidationSchema = () =>
  Yup.object({
    name: Yup.string().required("Name is required") || "",
    number: Yup.string().required("Number is required") || "",
    company_name: Yup.string().required("Company Name is required") || "",
  });

export const vendorInitialValues = {
  image: "",
  name: "",
  company_name: "",
  description: "",
  email: "",
  number: "",
  website: "",
  address: "",
  is_active: 1,
};

export const isActiveOptions = [
  { label: "Active", value: 1 },
  { label: "Inactive", value: 0 },
];
