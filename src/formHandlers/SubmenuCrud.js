import * as Yup from 'yup';

export const isActiveOptions = [
    { label: "Active", value: 1 },
    { label: "Inactive", value: 0 },
];

export const submenuValidationSchema = Yup.object({
    name: Yup.string()
        .required('Submenu name is required')
        .min(3, 'Submenu name must be at least 3 characters')
        .max(50, 'Submenu name must be less than 50 characters'),

    // description: Yup.string()
    //     .required('Description is required')
    //     .min(5, 'Description must be at least 5 characters')
    //     .max(200, 'Description must be less than 200 characters'),

    // icon: Yup.string().required('Icon is required'),

    is_active: Yup.number()
        .required('Status is required')
        .oneOf([0, 1], 'Invalid status selection'),

    url: Yup.string()
        .required('URL is required'),

    order: Yup.number()
        .required('Order is required')
        .min(0, 'Order must be a positive number')
        .integer('Order must be an integer'),
    role_ids: Yup.array().min(1, "At least one role must be selected"),
});


export const subMenuInitialValues = {
    name: "",
    // description: "",
    role_ids: [],
    icon: "",
    url: "",
    order: '',
    is_active: 1,
    menu_id: '',
}