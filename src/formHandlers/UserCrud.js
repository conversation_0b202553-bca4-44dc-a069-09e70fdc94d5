import * as Yup from "yup";

export const createUserValidationSchema = () => {
  return Yup.object().shape({
    name: Yup.string()
      .required("Name is required")
      .min(3, "Name must be at least 3 characters"),
    email: Yup.string()
      .email("Invalid email format")
      .required("Email is required"),
    username: Yup.string()
      .required("Username is required"),
    is_active: Yup.string().required("Status is required"),

    // ...((mode === 'create') || (mode === 'edit') && {
    password: Yup.string()
      .required("Password is required")
      .min(8, "Password must be at least 8 characters"),
    password_confirmation: Yup.string()
      .oneOf([Yup.ref("password"), null], "Passwords must match")
      .required("Confirm Password is required"),
    // })
  });
};

export const userInitialValues = {
  name: "",
  email: "",
  username: "",
  password: "",
  password_confirmation: "",
  number: "",
  image: "",
  is_active: 1,
};

export const isUserActiveOptions = [
  { label: "Active", value: 1 },
  { label: "Inactive", value: 0 },
];
