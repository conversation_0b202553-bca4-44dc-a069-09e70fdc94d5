import * as Yup from "yup";

export const createValidationSchema = () =>
  Yup.object({
    name:
      Yup.string()
        .max(255, "Maximum 255 character")
        .required("Name is required") || "",

    phone:
      Yup.string()
        //==-----Comment not deleted-----
        // .matches(
        //   /^(?:\+880|880|0)1[3-9][0-9]{8}$/,
        //   "Enter a valid  Phone Number"
        // )
        .required("Phone Number is required"),

    customer_source: Yup.string().min(1, "Please select one").required("Customer Source is required") || "",

  });

export const clientInitialValues = {
  name: "",
  phone: "",
  email: "",
  address: "",
  image: "",
  company_name: "",
  customer_source: "Internal",
  source_details: "",
  is_active: 1,
};

export const isActiveOptions = [
  { label: "Active", value: 1 },
  { label: "Inactive", value: 0 },
];

export const customerSources = [
  { label: "Internal", value: "Internal" },
  { label: "External", value: "External" },
];
