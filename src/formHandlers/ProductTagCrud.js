import * as Yup from 'yup';

export const productTagInitialValues = {
    tags: [
        { name: '', description: '', is_active: true }
    ]
};

export const productTagValidationSchema = Yup.object().shape({
    tags: Yup.array().of(
        Yup.object().shape({
            name: Yup.string()
                .required('Name is required'),
            description: Yup.string()
                .required('Description is required'),
            is_active: Yup.mixed()
                .oneOf([0, 1], 'Invalid status') 
                .required('Status is required')
        })
    ).min(1, 'At least one tag is required') 
});

export const productTagActiveOptions = [
    { label: "Active", value: 1 },
    { label: "Inactive", value: 0 },
];