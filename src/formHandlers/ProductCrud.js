import * as Yup from "yup";

export const productValidationSchema = () =>
  Yup.object({
    // name:
    //   Yup.string()
    //     .min(3, "Minimum 3 & maximum 30 character")
    //     .max(30, "Minimum 3 & maximum 30 character")
    //     .required("Name is required") || "",
    // name: Yup.string().required("name is required") || "",
    // description: Yup.string().required("description is required") || "",
    // image: Yup.string().required("image is required") || "",
    // sku: Yup.string().required("sku is required") || "",
    // quantity: Yup.string().required("quantity is required") || 0,
    // category_id: Yup.string().required("category id is required"),
    // brand_id: Yup.string().required("brand id is required") || 0,
    // tags: Yup.string().required("tags is required") || "",
    // regular_price: Yup.string().required("regular price is required") || 0,
    // sale_price: Yup.string().required("sale price is required") || 0,
    // is_description_shown_in_invoices: Yup.string().required("is_description_shown_in_invoices is required") || 0,
    // has_related_products: Yup.string().required("has_related_products is required") || 0,
    // is_active: Yup.string().required("Please select active or deactive") || 0
  });

export const productInitialValues = {
  name: "",
  description: "",
  image: "",
  sku: "",
  // tags: "",
  regular_price: "",
  sale_price: "",
  category_id: "",
  brand_id: "",
  has_serials: "",
  is_description_shown_in_invoices: "",
  has_related_products: "",
  is_active: "",
};

export const isProductActiveOptions = [
  { label: "Active", value: 1 },
  { label: "Inactive", value: 0 },
];

export const isDescriptionShownInInvoices = [
  { label: "Yes", value: 1 },
  { label: "No", value: 0 },
];

export const hasRelatedProducts = [
  { label: "Yes", value: 1 },
  { label: "No", value: 0 },
];

export const hasSerials = [
  { label: "Yes", value: 1 },
  { label: "No", value: 0 },
];

export const isActiveOptions = [
  { label: "Active", value: 1 },
  { label: "Inactive", value: 0 },
];