import * as Yup from "yup";

export const createValidationSchema = () =>
  Yup.object({
    name: Yup.string()
      .max(255, "Maximum 255 characters")
      .required("Name is required"),
    is_active:
      Yup.number().required("Please select if active or inactive") || "",
  });
export const brandInitialValues = {
  name: "",
  description: "",
  image: "",
  is_active: 1,
};

export const isActiveOptions = [
  { label: "Active", value: 1 },
  { label: "Inactive", value: 0 },
];
