import { lazy, Suspense } from "react";
import { Navigate, Route, Routes } from "react-router-dom";
import PrivateOutlet from "./routes/PrivateOutlet";
import PublicOutlet from "./routes/PublicOutlet";
import InvoiceOutlet from "./routes/InvoiceOutlet";

const Dashboard = lazy(() => import("./pages/dashboard"));
const Login = lazy(() => import("./pages/auth/login"));
// const ChangePass = lazy(() => import("./pages/auth/change-password"));
const Error = lazy(() => import("./pages/404"));

import AuthLayout from "./layout/AuthLayout";
import Layout from "./layout/Layout";

import Loading from "@/components/Loading";
import User from "./pages/user/user";
import CreateMenu from "./pages/menu/createMenu";
import MenuList from "./pages/menu/menuList";
import EditMenu from "./pages/menu/EditMenu";

import CreateBrand from "./pages/brand/CreateBrand";
import EditBrand from "./pages/brand/EditBrand";
import BrandList from "./pages/brand/BrandList";

import SubMenuList from "./pages/menu/SubMenuList";
import EditSubMenu from "./pages/menu/EditSubMenu";
import VendorList from "./pages/vendor/VendorList";
import CreateVendor from "./pages/vendor/CreateVendor";
import EditVendor from "./pages/vendor/EditVendor";
import CategoryList from "./pages/category/CategoryList";
import CreateCategory from "./pages/category/CreateCategory";
import EditCategory from "./pages/category/EditCategory";
// Customer File Path start
import CustomerList from "./pages/customer/CustomerList";
import CreateCustomer from "./pages/customer/CreateCustomer";
import EditCustomer from "./pages/customer/EditCustomer";
import DetailsCustomer from "./pages/customer/DetailsCustomer";
// Customer File Path end
import ProductList from "./pages/product/ProductList";
import CreateProduct from "./pages/product/CreateProduct";
import EditProduct from "./pages/product/EditProduct";
import DetailsProduct from "./pages/product/DetailsProduct";
import ProtectedRoutes from "./routes/ProtectedRoutes";
import CreateUser from "./pages/user/CreateUser";
import EditUser from "./pages/user/EditUser";
import ProductSerialList from "./pages/productSerial/ProductSerialList";
import CreateProductSerial from "./pages/productSerial/CreateProductSerial";
import EditProductSerial from "./pages/productSerial/EditProductSerial";
import changePass from "./pages/auth/change-password";
import CreateProductTag from "./pages/productTag/CreateProductTag";
import ProductTagList from "./pages/productTag/ProductTagList";
import EditProductTag from "./pages/productTag/EditProductTag";
import StoreStock from "./pages/Stock/StoreStock";
import StockList from "./pages/Stock/StockList";
import EditStock from "./pages/Stock/EditStock";
import TagsList from "./pages/tags";
import Index from "./pages/tags";
import SupportTypeList from "./pages/AllSupport/supportType/index";
import DetailsSupportType from "./pages/AllSupport/supportType/DetailsSupport";
import CreateSupportType from "./pages/AllSupport/supportType/CreateSupport";
import EditSupportType from "./pages/AllSupport/supportType/EditSupport";
import StoreSale from "./pages/Sale/StoreSale";
import Inventories from "./pages/Sale/Inventories";
import SalesList from "./pages/Sale/SalesList";
import HomeContainer from "./pages/PromotionalSite/HomeContainer";

//support ticket
import IndexSupportTicket from "./pages/AllSupport/supportTicket/index";
import CreateSupportTicket from "./pages/AllSupport/supportTicket/create";
import DetailSupportTicket from "./pages/AllSupport/supportTicket/details";
import EditSupportTicket from "./pages/AllSupport/supportTicket/edit";
import SalesAccount from "./pages/Sale/SalesAccount/SalesAccount";
import RoleList from "./pages/user/Role/RoleList";
import AssignUser from "./pages/user/Role/AssignRole";
import CreateRole from "./pages/user/Role/CreateRole";
import SupportList from "./pages/UserPanel/Support/SupportList";
import Licensing from "./pages/Sale/Licenses/Licensing";

//License Type
import IndexLicenseType from "./pages/License/LicenseType/index";
import CreateLicenseType from "./pages/License/LicenseType/create";
import EditLicenseType from "./pages/License/LicenseType/edit";
import DetailsLicenseType from "./pages/License/LicenseType/details";

//License
import IndexLicense from "./pages/License/License/index";
import CreateLicense from "./pages/License/License/create";
import EditLicense from "./pages/License/License/edit";
import DetailsLicense from "./pages/License/License/details";

//Activity Logs File path
import IndexActivityLog from "./pages/ActivityLogs/index";

//Expense Head File path
import IndexExpenseHead from "./pages/AllSupport/expenseHead/index";
import CreateExpenseHead from "./pages/AllSupport/expenseHead/create";
import EditExpenseHead from "./pages/AllSupport/expenseHead/edit";
import DetailsExpenseHead from "./pages/AllSupport/expenseHead/details";
import SupportExpenses from "./pages/UserPanel/Support/SupportExpenses/SupportExpenses";
import CreateExpense from "./pages/UserPanel/Support/SupportExpenses/CreateExpense";

//Model File Path
import IndexModel from "./pages/model/index";
import CreateModel from "./pages/model/create";
import EditModel from "./pages/model/edit";

//Support Account File Path
import IndexSupportAccount from "./pages/AllSupport/supportTicket/Account/index";
import CreateSupportAccount from "./pages/AllSupport/supportTicket/Account/create";
import EditSupportAccount from "./pages/AllSupport/supportTicket/Account/edit";
import DetailsSupportAccount from "./pages/AllSupport/supportTicket/Account/details";

//Related Product File Path
import IndexRelatedProduct from "./pages/product/relatedProduct/index";
import CreateRelatedProduct from "./pages/product/relatedProduct/create";
import EditRelatedProduct from "./pages/product/relatedProduct/edit";
import DetailsRelatedProduct from "./pages/product/relatedProduct/details";

//AMC File Path
import IndexAMC from "./pages/warranty/amc/index";
import CreateAMC from "./pages/warranty/amc/create";
import DetailsAMC from "./pages/warranty/amc/details";
import EditAMC from "./pages/warranty/amc/edit";

//License File Path

//Product Serials File Path
import IndexSerial from "./pages/serials/index";

//Query Tickets File Path
import IndexQueryTicket from "./pages/queryTickets/index";

//User Profile File Path
import IndexUserProfile from "./pages/user/UserProfile/index";

//Support Ticket File Path

//User Panel File Path

//User Panel File Path

import SupportDetails from "./pages/UserPanel/Support/SupportDetails";
import ViewStock from "./pages/Stock/ViewStock";
import SeeInvoice from "./pages/Sale/SalesAccount/SeeInvoice";
import LotWiseStock from "./pages/Sale/LotWiseStock";
import ProductWiseSalesList from "./pages/Sale/ProductWiseSalesList";
import WarrantyList from "./pages/warranty/Warranty/WarrantyList";
import SeeChallan from "./pages/Sale/SalesAccount/SeeChallan";
import UserDashboard from "./pages/dashboard/UserDashboard";
import PaidInvoices from "./pages/Report/PaidInvoices";
import AdvancePaidInvoices from "./pages/Report/AdvancePaidInvoices";

function App() {
  return (
    <main className="App  relative">
      <Routes>
        <Route
          element={
            <InvoiceOutlet
              allowedRoles={["admin", "system-admin", "super-admin", "account"]}
            />
          }
        >
          <Route path="/see-invoice/:id" element={<SeeInvoice />} />
          <Route path="see-challan/:id" element={<SeeChallan />} />
        </Route>
        <Route element={<PublicOutlet />}>
          <Route path="/invoice/:invoiceNo" element={<SeeInvoice />} />
          <Route path="/" element={<HomeContainer />} />
          <Route path="/login" element={<AuthLayout />}>
            <Route path="/login" element={<Login />} />
          </Route>
        </Route>

        <Route element={<PrivateOutlet />}>
          <Route path="/*" element={<Layout />}>
            <Route
              path="dashboard"
              element={
                <ProtectedRoutes
                  element={Dashboard}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="user/dashboard"
              element={
                <ProtectedRoutes
                  element={UserDashboard}
                  allowedRoles={[
                    "user",
                  ]}
                />
              }
            />
            <Route
              path="create-menu"
              element={
                <ProtectedRoutes
                  element={CreateMenu}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="change-password"
              element={
                <ProtectedRoutes
                  element={changePass}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "user",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="menu-list"
              element={
                <ProtectedRoutes
                  element={MenuList}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-menu/:id"
              element={
                <ProtectedRoutes
                  element={EditMenu}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* submenu crud */}
            <Route
              path="submenu-list"
              element={
                <ProtectedRoutes
                  element={SubMenuList}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-submenu/:id"
              element={
                <ProtectedRoutes
                  element={EditSubMenu}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* brand crud routes */}
            <Route
              path="create-brand"
              element={
                <ProtectedRoutes
                  element={CreateBrand}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-brand/:id"
              element={
                <ProtectedRoutes
                  element={EditBrand}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="brand-list"
              element={
                <ProtectedRoutes
                  element={BrandList}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* vendor crud routes */}
            <Route
              path="create-vendor"
              element={
                <ProtectedRoutes
                  element={CreateVendor}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-vendor/:id"
              element={
                <ProtectedRoutes
                  element={EditVendor}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="vendor-list"
              element={
                <ProtectedRoutes
                  element={VendorList}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* category crud routes */}
            <Route
              path="create-category"
              element={
                <ProtectedRoutes
                  element={CreateCategory}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-category/:id"
              element={
                <ProtectedRoutes
                  element={EditCategory}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="category-list"
              element={
                <ProtectedRoutes
                  element={CategoryList}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* Customer crud routes start */}
            <Route
              path="create-customer"
              element={
                <ProtectedRoutes
                  element={CreateCustomer}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-customer/:id"
              element={
                <ProtectedRoutes
                  element={EditCustomer}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="details-customer/:id"
              element={
                <ProtectedRoutes
                  element={DetailsCustomer}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="customer-list"
              element={
                <ProtectedRoutes
                  element={CustomerList}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* Customer crud routes end */}
            {/* product crud routes */}
            <Route
              path="create-product"
              element={
                <ProtectedRoutes
                  element={CreateProduct}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="product-list"
              element={
                <ProtectedRoutes
                  element={ProductList}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-product/:id"
              element={
                <ProtectedRoutes
                  element={EditProduct}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="details-product/:id"
              element={
                <ProtectedRoutes
                  element={DetailsProduct}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* User crud */}
            <Route
              path="user-list"
              element={
                <ProtectedRoutes
                  element={User}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="user-create"
              element={
                <ProtectedRoutes
                  element={CreateUser}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-user/:id"
              element={
                <ProtectedRoutes
                  element={EditUser}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* Managing Role */}
            <Route
              path="role-list"
              element={
                <ProtectedRoutes
                  element={RoleList}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="assign-user"
              element={
                <ProtectedRoutes
                  element={AssignUser}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="create-role"
              element={
                <ProtectedRoutes
                  element={CreateRole}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* product serial crud */}
            <Route
              path="product-serials-list/:id"
              element={
                <ProtectedRoutes
                  element={ProductSerialList}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="product-serials-create"
              element={
                <ProtectedRoutes
                  element={CreateProductSerial}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-product-serials/:id"
              element={
                <ProtectedRoutes
                  element={EditProductSerial}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* Product Serial CRUD */}
            <Route
              path="create-product-tags"
              element={
                <ProtectedRoutes
                  element={Index}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* <Route
              path="create-product-tags"
              element={
                <ProtectedRoutes
                  element={CreateProductTag}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            /> */}
            <Route
              path="product-tag-list"
              element={
                <ProtectedRoutes
                  element={ProductTagList}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-product-tag/:id"
              element={
                <ProtectedRoutes
                  element={EditProductTag}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* Stock Crud  */}
            <Route
              path="store-stock"
              element={
                <ProtectedRoutes
                  element={StoreStock}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="stock-list"
              element={
                <ProtectedRoutes
                  element={StockList}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="view-stock/:id"
              element={
                <ProtectedRoutes
                  element={ViewStock}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-stock/:id"
              element={
                <ProtectedRoutes
                  element={EditStock}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* Support type crud start*/}
            <Route
              path="support-type"
              element={
                <ProtectedRoutes
                  element={SupportTypeList}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="details-support/:id"
              element={
                <ProtectedRoutes
                  element={DetailsSupportType}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="create-support-type"
              element={
                <ProtectedRoutes
                  element={CreateSupportType}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-support/:id"
              element={
                <ProtectedRoutes
                  element={EditSupportType}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* Support type crud end*/}
            {/* Support ticket crud start*/}
            <Route
              path="support-list"
              element={
                <ProtectedRoutes
                  element={IndexSupportTicket}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="support-ticket/:id"
              element={
                <ProtectedRoutes
                  element={IndexSupportTicket}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="create-support-ticket/:id"
              element={
                <ProtectedRoutes
                  element={CreateSupportTicket}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="details-support-ticket/:id"
              element={
                <ProtectedRoutes
                  element={DetailSupportTicket}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="edit-support-ticket/:id"
              element={
                <ProtectedRoutes
                  element={EditSupportTicket}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            {/* Support ticket crud end*/}
            {/* License Type CRUD Route Start */}
            <Route
              path="license-type-list"
              element={
                <ProtectedRoutes
                  element={IndexLicenseType}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="create-license-type"
              element={
                <ProtectedRoutes
                  element={CreateLicenseType}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-license-type/:id"
              element={
                <ProtectedRoutes
                  element={EditLicenseType}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="details-license-type/:id"
              element={
                <ProtectedRoutes
                  element={DetailsLicenseType}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* License Type CRUD Route End */}
            {/* License CRUD Route Start */}
            <Route
              path="license-list"
              element={
                <ProtectedRoutes
                  element={IndexLicense}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="create-license"
              element={
                <ProtectedRoutes
                  element={CreateLicense}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-license/:id"
              element={
                <ProtectedRoutes
                  element={EditLicense}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="details-license/:id"
              element={
                <ProtectedRoutes
                  element={DetailsLicense}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* License CRUD Route End */}
            {/* Activity Logs CRUD Route Start */}
            <Route
              path="activity-logs-list"
              element={
                <ProtectedRoutes
                  element={IndexActivityLog}
                  allowedRoles={["super-admin"]}
                />
              }
            />
            {/* Activity Logs CRUD Route end */}
            {/* ExpenseHead CRUD Route Start */}
            <Route
              path="expense-head-list"
              element={
                <ProtectedRoutes
                  element={IndexExpenseHead}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="create-expense-head"
              element={
                <ProtectedRoutes
                  element={CreateExpenseHead}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="edit-expense-head/:id"
              element={
                <ProtectedRoutes
                  element={EditExpenseHead}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="details-expense-head/:id"
              element={
                <ProtectedRoutes
                  element={DetailsExpenseHead}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            {/* ExpenseHead CRUD Route End  */}

            {/* AMC CRUD Route start */}
            <Route
              path="warranty-list"
              element={
                <ProtectedRoutes
                  element={WarrantyList}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="warranty-amc-list"
              element={
                <ProtectedRoutes
                  element={IndexAMC}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="create-warranty-amc"
              element={
                <ProtectedRoutes
                  element={CreateAMC}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="details-warranty-amc/:id"
              element={
                <ProtectedRoutes
                  element={DetailsAMC}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="edit-warranty-amc/:id"
              element={
                <ProtectedRoutes
                  element={EditAMC}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />

            {/* AMC CRUD Route end */}

            {/* Models CRUD Route Start */}
            <Route
              path="model-list"
              element={
                <ProtectedRoutes
                  element={IndexModel}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="create-model"
              element={
                <ProtectedRoutes
                  element={CreateModel}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-model/:id"
              element={
                <ProtectedRoutes
                  element={EditModel}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* Models CRUD Route End */}
            {/* Support Account CRUD Route start */}
            <Route
              path="support-account-list/:support_id"
              element={
                <ProtectedRoutes
                  element={IndexSupportAccount}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="create-support-account/:id"
              element={
                <ProtectedRoutes
                  element={CreateSupportAccount}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="edit-support-account/:id/:accountId"
              element={
                <ProtectedRoutes
                  element={EditSupportAccount}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="details-support-account/:id/:accountId"
              element={
                <ProtectedRoutes
                  element={DetailsSupportAccount}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            {/* Support Account CRUD Route End  */}
            {/* Related Product crud start*/}
            <Route
              path="related-product/:id"
              element={
                <ProtectedRoutes
                  element={IndexRelatedProduct}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="create-related-product/:id"
              element={
                <ProtectedRoutes
                  element={CreateRelatedProduct}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="details-related-product/:id"
              element={
                <ProtectedRoutes
                  element={DetailsRelatedProduct}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            <Route
              path="edit-related-product/:id"
              element={
                <ProtectedRoutes
                  element={EditRelatedProduct}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/*  Related Product crud end*/}

            {/* Query Ticket CRUD Route Start */}

            <Route
              path="query-tickets-list"
              element={
                <ProtectedRoutes
                  element={IndexQueryTicket}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />

            {/* Query Ticket CRUD Route End */}

            {/* User Profile CRUD Route start */}

            <Route
              path="user-profile"
              element={
                <ProtectedRoutes
                  element={IndexUserProfile}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                    "user",
                  ]}
                />
              }
            />

            {/* User Profile CRUD Route End */}

            {/* Sales Crud*/}
            <Route
              path="sales-list"
              element={
                <ProtectedRoutes
                  element={SalesList}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="product-wise-sales-list"
              element={
                <ProtectedRoutes
                  element={ProductWiseSalesList}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="store-sale"
              element={
                <ProtectedRoutes
                  element={StoreSale}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="inventory-list"
              element={
                <ProtectedRoutes
                  element={Inventories}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route
              path="lot-wise-stock"
              element={
                <ProtectedRoutes
                  element={LotWiseStock}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            {/* Task Crud */}
            <Route
              path="task-list"
              element={
                <ProtectedRoutes
                  element={SupportList}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "user",
                  ]}
                />
              }
            />
            <Route
              path="task-details/:id"
              element={
                <ProtectedRoutes
                  element={SupportDetails}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "user",
                  ]}
                />
              }
            />
            {/* Task Expenses */}
            <Route
              path="support-expenses/:id"
              element={
                <ProtectedRoutes
                  element={SupportExpenses}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "user",
                  ]}
                />
              }
            />
            <Route
              path="create-expenses/:id"
              element={
                <ProtectedRoutes
                  element={CreateExpense}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "user",
                  ]}
                />
              }
            />
            {/* Tag Crud */}
            <Route
              path="tag-list"
              element={
                <ProtectedRoutes
                  element={TagsList}
                  allowedRoles={["super-admin", "system-admin", "admin"]}
                />
              }
            />
            {/* Account Crud */}
            <Route
              path="sales-accounts/:id"
              element={
                <ProtectedRoutes
                  element={SalesAccount}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            {/* Product serial CRUD start */}
            <Route
              path="product-serial"
              element={
                <ProtectedRoutes
                  element={IndexSerial}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            {/* Product serial CRUD end */}
            {/* License Part */}
            <Route
              path="licensing/:id"
              element={
                <ProtectedRoutes
                  element={Licensing}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />

            {/* Report List  */}
            <Route
              path="paid-invoice"
              element={
                <ProtectedRoutes
                  element={PaidInvoices}
                  allowedRoles={[
                    "super-admin",
                    "system-admin",
                    "admin",
                    "account",
                  ]}
                />
              }
            />
            <Route path="advance-paid-invoice" element={<ProtectedRoutes element={AdvancePaidInvoices} allowedRoles={["super-admin", "system-admin", "admin", "account"]}/>}/>

            <Route path="*" element={<Navigate to="/404" />} />
          </Route>
        </Route>
        <Route
          path="/404"
          element={
            <Suspense fallback={<Loading />}>
              <Error />
            </Suspense>
          }
        />
      </Routes>
    </main>
  );
}

export default App;
