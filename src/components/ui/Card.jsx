import React from "react";
import useSkin from "@/hooks/useSkin";

const Card = ({
  children,
  title,
  secondTitle,
  subtitle,
  headerslot,
  className = "custom-class",
  bodyClass = "p-6",
  noborder,
  titleClass = "custom-class",
}) => {
  const [skin] = useSkin();

  return (
    <div
      className={`
          card rounded-md bg-white dark:bg-slate-800   ${skin === "bordered"
          ? " border border-slate-200 dark:border-slate-700"
          : "shadow-base" //shadow-base
        }
      ${className}
          `}
    >
      {(title || subtitle || secondTitle) && (
        <header className={`card-header ${noborder ? "no-border" : ""}`}>
          {/* Flexbox to align title and secondTitle with justify-between */}
          <div className="flex justify-between items-center w-full">
            {title && (
              <div className={`card-title ${titleClass}`}>
                {title}
              </div>
            )}
            {secondTitle && (
              <div className={`card-title ${titleClass}`}>
                {secondTitle}
              </div>
            )}
          </div>

          {/* Subtitle */}
          {subtitle && <div className="card-subtitle">{subtitle}</div>}
          
          {/* Header slot */}
          {headerslot && <div className="card-header-slot">{headerslot}</div>}
        </header>
      )}
      <main className={`card-body ${bodyClass}`}>{children}</main>
    </div>
  );
};

export default Card;
