import React, { useState } from "react";
import { useField } from "formik";
import { Icon } from "@iconify/react";

const QuantityInput = ({ name, min = 0, max = 100 }) => {
  const [field, , helpers] = useField(name);
  const [quantity, setQuantity] = useState(field.value || 0);

  const handleIncrease = () => {
    setQuantity((prev) => {
      const newValue = prev + 1;
      if (newValue <= max) {
        helpers.setValue(newValue); // Sync with Formik
        return newValue;
      }
      return prev;
    });
  };

  const handleDecrease = () => {
    setQuantity((prev) => {
      const newValue = prev - 1;
      if (newValue >= min) {
        helpers.setValue(newValue); // Sync with Formik
        return newValue;
      }
      return prev;
    });
  };

  const handleChange = (e) => {
    const value = Number(e.target.value);
    setQuantity(value);
    helpers.setValue(value); // Sync with Formik
  };

  return (
    <div className="flex items-center gap-2 w-32">
      <Icon
        className="text-3xl text-start cursor-pointer"
        onClick={handleDecrease}
        icon="ic:round-minus"
      />
      <input
        name={name}
        type="number"
        className="w-full text-center no-spinner focus:outline-none focus:border-transparent"
        value={quantity}
        onChange={handleChange}
      />
      <Icon
        className="text-3xl text-left cursor-pointer"
        onClick={handleIncrease}
        icon="ic:round-plus"
      />
    </div>
  );
};

export default QuantityInput;