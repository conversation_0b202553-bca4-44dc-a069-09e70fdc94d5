import React, { useState, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import uploadSvgImage from "@/assets/images/svg/upload.svg"; // Default upload icon
import excelPreviewImage from "@/components/ui/xcel.png"; // Custom image to show after Excel upload
import { Icon } from "@iconify/react";

const ExcelDropZone = ({ onDrop, height = "250px", width = "280px", error }) => {
    const [files, setFiles] = useState([]);

    // Clear the files when there's an error so the user can upload again
    useEffect(() => {
        if (error) {
            setFiles([]); // Reset the files state
        }
    }, [error]);

    const { getRootProps, getInputProps, isDragAccept } = useDropzone({
        accept: {
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [], // .xlsx files
            "application/vnd.ms-excel": [], // .xls files
        },
        onDrop: (acceptedFiles) => {
            const previewFile = Object.assign(acceptedFiles[0], {
                preview: URL.createObjectURL(acceptedFiles[0]),
            });
            setFiles([previewFile]);
            onDrop(acceptedFiles[0] || null); // Pass file to parent component
        },
    });

    const handleRemoveFile = () => {
        setFiles([]); // Clear the file
        onDrop(null); // Reset file in parent component
    };

    return (
        <div>
            <div className={`w-[${width}] h-[${height}] text-center border-dashed border border-secondary-500 rounded-md py-[0px] px-[0px] flex flex-col justify-center items-center`}>
                {files.length === 0 && (
                    <div {...getRootProps({ className: "dropzone" })}>
                        <input className="hidden" {...getInputProps()} />
                        <img src={uploadSvgImage} alt="Upload Icon" className="mx-auto" />
                        {isDragAccept ? (
                            <p className="text-sm text-slate-500 dark:text-slate-300">
                                Drop the Excel file here ...
                            </p>
                        ) : (
                            <p className="text-sm text-slate-500 dark:text-slate-300">
                                Drop Excel file here or click to upload.
                            </p>
                        )}
                    </div>
                )}

                {/* Error Message */}
                {error && (
                    <p className="text-red-500 text-sm mt-2">{error}</p>
                )}

                {/* Preview Image (if no error) */}
                {files.length > 0 && !error && (
                    <div className="relative">
                        <div className="h-[250px] w-[280px] rounded-md">
                            <img
                                src={excelPreviewImage} // Show this image as a preview
                                alt="Excel Preview"
                                className="object-cover h-full w-full block rounded-md"
                            />
                        </div>

                        {/* Remove File (Cross) Icon */}
                        <button
                            className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1"
                            onClick={handleRemoveFile}
                        >
                            <Icon icon="mdi:close" />
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ExcelDropZone;
