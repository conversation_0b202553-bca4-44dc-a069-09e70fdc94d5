// import React, { useState } from "react";
// import { useDropzone } from "react-dropzone";

// // image import
// import uploadSvgImage from "@/assets/images/svg/upload.svg";

// const DropZone = () => {
//   const [files, setFiles] = useState([]);
//   const { getRootProps, getInputProps, isDragAccept } = useDropzone({
//     accept: {
//       "image/*": [],
//     },
//     onDrop: (acceptedFiles) => {
//       setFiles(
//         acceptedFiles.map((file) =>
//           Object.assign(file, {
//             preview: URL.createObjectURL(file),
//           })
//         )
//       );
//     },
//   });
//   return (
//     <div>
//       <div className="w-full text-center border-dashed border border-secondary-500 rounded-md py-[52px] flex flex-col justify-center items-center">
//         {files.length === 0 && (
//           <div {...getRootProps({ className: "dropzone" })}>
//             <input className="hidden" {...getInputProps()} />
//             <img src={uploadSvgImage} alt="" className="mx-auto mb-4" />
//             {isDragAccept ? (
//               <p className="text-sm text-slate-500 dark:text-slate-300 ">
//                 Drop the files here ...
//               </p>
//             ) : (
//               <p className="text-sm text-slate-500 dark:text-slate-300 f">
//                 Drop files here or click to upload.
//               </p>
//             )}
//           </div>
//         )}
//         <div className="flex space-x-4">
//           {files.map((file, i) => (
//             <div key={i} className="mb-4 flex-none">
//               <div className="h-[300px] w-[300px] mx-auto mt-6 rounded-md">
//                 <img
//                   src={file.preview}
//                   className=" object-contain h-full w-full block rounded-md"
//                   onLoad={() => {
//                     URL.revokeObjectURL(file.preview);
//                   }}
//                 />
//               </div>
//             </div>
//           ))}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default DropZone;

// import React, { useState } from "react";
// import { useDropzone } from "react-dropzone";

// // image import
// import uploadSvgImage from "@/assets/images/svg/upload.svg";

// const DropZone = () => {
//   const [files, setFiles] = useState([]);
//   const { getRootProps, getInputProps, isDragAccept } = useDropzone({
//     accept: {
//       "image/*": [],
//     },
//     onDrop: (acceptedFiles) => {
//       setFiles(
//         acceptedFiles.map((file) =>
//           Object.assign(file, {
//             preview: URL.createObjectURL(file),
//           })
//         )
//       );
//     },
//   });

//   return (
//     <div>
//       <div className="w-[280px] h-[250px] text-center border-dashed border border-secondary-500 rounded-md py-[0px] px-[0px] flex flex-col justify-center items-center">
//         {files.length === 0 && (
//           <div {...getRootProps({ className: "dropzone" })}>
//             <input className="hidden" {...getInputProps()} />
//             <img src={uploadSvgImage} alt="" className="mx-auto" />
//             {isDragAccept ? (
//               <p className="text-sm text-slate-500 dark:text-slate-300 ">
//                 Drop the Thumbnail here ...
//               </p>
//             ) : (
//               <p className="text-sm text-slate-500 dark:text-slate-300">
//                 Drop Thumbnail here or click to upload.
//               </p>
//             )}
//           </div>
//         )}
//         <div className="flex space-x-">
//           {files.map((file, i) => (
//             <div key={i} className="mb- flex-none">
//               <div className="h-[250px] w-[280px] mt- rounded-md">
//                 <img
//                   src={file.preview}
//                   className="object-cover h-[250px] w-[280px] block rounded-md"
//                   onLoad={() => {
//                     URL.revokeObjectURL(file.preview);
//                   }}
//                   alt="Preview"
//                 />
//               </div>
//             </div>
//           ))}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default DropZone;

// import React, { useState } from "react";
// import { useDropzone } from "react-dropzone";
// import uploadSvgImage from "@/assets/images/svg/upload.svg";

// const DropZone = ({ onDrop }) => {
//   const [files, setFiles] = useState([]);
//   const { getRootProps, getInputProps, isDragAccept } = useDropzone({
//     accept: {
//       "image/*": [],
//     },
//     onDrop: (acceptedFiles) => {
//       setFiles(
//         acceptedFiles.map((file) =>
//           Object.assign(file, {
//             preview: URL.createObjectURL(file),
//           })
//         )
//       );
//       // Pass the selected file back to the form
//       onDrop(acceptedFiles[0] || null); // Pass null if no file is selected
//     },
//   });

//   return (
//     <div>
//       <div className="w-[280px] h-[250px] text-center border-dashed border border-secondary-500 rounded-md py-[0px] px-[0px] flex flex-col justify-center items-center">
//         {files.length === 0 && (
//           <div {...getRootProps({ className: "dropzone" })}>
//             <input className="hidden" {...getInputProps()} />
//             <img src={uploadSvgImage} alt="" className="mx-auto" />
//             {isDragAccept ? (
//               <p className="text-sm text-slate-500 dark:text-slate-300 ">
//                 Drop the Thumbnail here ...
//               </p>
//             ) : (
//               <p className="text-sm text-slate-500 dark:text-slate-300">
//                 Drop Thumbnail here or click to upload.
//               </p>
//             )}
//           </div>
//         )}
//         <div className="flex space-x-">
//           {files.map((file, i) => (
//             <div key={i} className="mb- flex-none">
//               <div className="h-[250px] w-[280px] mt- rounded-md">
//                 <img
//                   src={file.preview}
//                   className="object-cover h-[250px] w-[280px] block rounded-md"
//                   onLoad={() => {
//                     URL.revokeObjectURL(file.preview);
//                   }}
//                   alt="Preview"
//                 />
//               </div>
//             </div>
//           ))}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default DropZone;

import React, { useState } from "react";
import { useDropzone } from "react-dropzone";
import uploadSvgImage from "@/assets/images/svg/upload.svg";
import { Icon } from "@iconify/react";

const DropZone = ({ onDrop }) => {
  const [files, setFiles] = useState([]);
  const { getRootProps, getInputProps, isDragAccept } = useDropzone({
    accept: {
      // "image/*": [],
      "image/png": [],
      "image/jpeg": [],
      "image/jpg": [],
    },
    onDrop: (acceptedFiles) => {
      const previewFile = Object.assign(acceptedFiles[0], {
        preview: URL.createObjectURL(acceptedFiles[0]),
      });
      setFiles([previewFile]);
      onDrop(acceptedFiles[0] || null); // Pass null if no file is selected
    },
  });

  const handleRemoveImage = () => {
    setFiles([]); // Remove the current image
    onDrop(null); // Set image as null in the parent form
  };

  return (
    <div>
      {/* w-[280px] h-[250px] */}
      <div className="w-auto h-[250px] text-center border-dashed border border-secondary-500 rounded-md py-[0px] px-[0px] flex flex-col justify-center items-center">
        {files.length === 0 && (
          <div {...getRootProps({ className: "dropzone" })}>
            <input className="hidden" {...getInputProps()} />
            <img src={uploadSvgImage} alt="Upload Icon" className="mx-auto" />
            {isDragAccept ? (
              <p className="text-sm text-slate-500 dark:text-slate-300">
                Drop the Thumbnail here ...
              </p>
            ) : (
              <p className="text-sm text-slate-500 dark:text-slate-300">
                Drop Thumbnail here or click to upload.
              </p>
            )}
          </div>
        )}

        {files.length > 0 && (
          <div className="relative">
            <div className="h-[250px] w-[280px] rounded-md">
              <img
                src={files[0].preview}
                className="object-cover h-[250px] w-[280px] block rounded-md"
                alt="Preview"
              />
            </div>

            {/* Remove Image (Cross) Icon */}
            <button
              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1"
              onClick={handleRemoveImage}
            >
              <Icon icon="mdi:close" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DropZone;
