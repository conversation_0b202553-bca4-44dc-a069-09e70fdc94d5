import React from 'react';
import { useField } from 'formik';

const TextAreaField = ({ label, required, columns = 5, ...props }) => {
    const [field, meta] = useField(props);

    return (
        <div className="mb-4">
            <label
                htmlFor={props.id || props.name}
                className="block text-gray-700 text-base font-medium mb-2"
            >
                {label} {required && <span className="text-red-500">*</span>}
            </label>
            <textarea
                className={`form-control w-full border rounded-md py-2 px-3 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-300 ease-in-out ${meta.touched && meta.error ? 'border-red-500' : 'border-gray-300'
                    }`}
                rows={columns}
                {...field}
                {...props}
            />
            {meta.touched && meta.error && (
                <div className="text-red-500 text-xs mt-1">{meta.error}</div>
            )}
        </div>
    );
};

export default TextAreaField;
