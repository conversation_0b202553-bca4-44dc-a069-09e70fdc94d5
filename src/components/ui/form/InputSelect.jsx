import React from "react";
import Select from "react-select";
import { useField } from "formik";
import { Icon } from "@iconify/react";

const InputSelect = ({
  label,
  options = [],
  valueKey = "value",
  labelKey = "label",
  placeholder,
  onRoleSelect,
  className,
  isClearable,
  onlyLabel,
  required,
  isDisabled,
  ...props
}) => {
  const [field, , helpers] = useField(props);

  const handleChange = (selectedOption) => {
    if (selectedOption === null) {
      helpers.setValue("");
      if (typeof onRoleSelect === "function") {
        onRoleSelect("");
      }
    } else {
      if (onlyLabel) {
        helpers.setValue(selectedOption.label);
      } else {
        helpers.setValue(selectedOption.value);
      }
      if (typeof onRoleSelect === "function") {
        onRoleSelect(onlyLabel ? selectedOption.label : selectedOption.value);
      }
    }
  };

  const formattedOptions = options?.map((option) => ({
    value: option[valueKey],
    label: option[labelKey],
    image: option.image,
  }));

  const customStyles = {
    control: (base, state) => ({
      ...base,
      backgroundColor: isDisabled ? "#f1f5f6" : "#fff",
      borderColor: state.isFocused ? "#93c5fd" : "#d1d5db",
      boxShadow: state.isFocused ? "0 0 0 2px rgba(96,165,250, 0.5)" : "none",
      borderRadius: "0.375rem",
      transition: "all 0.2s ease-in-out",
      "&:hover": {
        borderColor: "#93c5fd",
      },
      maxHeight: "200px",
      pointerEvents: isDisabled ? "none" : "auto",
    }),
    singleValue: (base) => ({
      ...base,
      color: isDisabled ? "#4a5568" : base.color,
    }),
    menu: (base) => ({
      ...base,
      borderRadius: "0.375rem",
      boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
      zIndex: 10,
      maxHeight: "200px",
      overflowY: "auto",
    }),
    menuList: (base) => ({
      ...base,
      padding: 0,
      maxHeight: "200px",
      overflowY: "auto",
      margin: 0,
    }),
    option: (base, state) => ({
      ...base,
      padding: "10px",
      backgroundColor: state.isSelected
        ? "#3b82f6"
        : state.isFocused
          ? "#dbeafe"
          : "white",
      color: state.isSelected ? "white" : "#374151",
      cursor: "pointer",
      "&:active": {
        backgroundColor: state.isSelected ? "#3b82f6" : "#dbeafe",
      },
      "&:hover": {
        backgroundColor: state.isSelected ? "#3b82f6" : "#dbeafe",
      },
      transition: "all 0.2s ease-in-out",
    }),
    input: (base) => ({
      ...base,
      color: "#374151",
    }),
    placeholder: (base) => ({
      ...base,
      color: "#9ca3af",
    }),
  };

  // Custom option rendering function to display image if exists
  const customOption = ({ innerProps, innerRef, isSelected, isFocused, data }) => (
    <div
      ref={innerRef}
      {...innerProps}
      className={`flex items-center space-x-2 px-3 py-2 cursor-pointer justify-between ${isSelected
        ? "bg-blue-500 text-white"
        : isFocused
          ? "bg-blue-50"
          : "bg-white"
        } ${isSelected ? "" : "hover:bg-blue-50"}`}
    >
      <span>{data.label}</span>
      {data.image ? (
        <img
          src={`${import.meta.env.VITE_MEDIA_URL}/${data.image}`}
          alt={data.label}
          className="w-6 h-6 rounded-full object-cover"
        />
      ) : (
        <Icon icon="mdi:image-off-outline" width="24" height="24" />
      )}
    </div>
  );

  return (
    <div className={className}>
      <label
        className="block text-gray-700 text-base font-medium mb-2"
        htmlFor={props.id || props.name}
      >
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div>
        <Select
          value={formattedOptions.find((opt) => opt.value === field.value)}
          options={formattedOptions}
          isClearable={isClearable}
          onChange={handleChange}
          onBlur={() => helpers.setTouched(true)}
          placeholder={placeholder}
          styles={customStyles}
          isDisabled={isDisabled}
          components={{ Option: customOption }}
          classNamePrefix="react-select"
        />
      </div>
      {field.touched && field.error ? (
        <div className="text-red-500 text-xs mt-2">{field.error}</div>
      ) : null}
    </div>
  );
};

export default InputSelect;
