import React, { useRef, useState } from "react";
import { useField, useFormikContext } from "formik";
import Icon from "@/components/ui/Icon";
import { usePostApiMutation } from '@/store/api/apihandler/commonSlice';
import { toast } from "react-toastify";

const FileUpload = ({
    name,
    label = "Browse",
    placeholder = "Choose a file or drop it here...",
    multiple = false,
    accept = "image/*,application/pdf", // Allow images and PDFs
    className = "",
    id,
    title = "",
    description,
    classLabel = "",
    horizontal = false,
    endpoint,
    showFileLink = false,
    valueKey = "file",
}) => {
    const [field, meta, helpers] = useField({ name });
    const { setValue, setError } = helpers;
    const [selectedFiles, setSelectedFiles] = useState([]);
    const [fileSizeError, setFileSizeError] = useState("");
    const fileInputRef = useRef(null);
    const { setFieldValue } = useFormikContext();
    const [postApi] = usePostApiMutation();
    const [responseMsg, setResponseMsg] = useState('');

    const handleFileChange = async (e) => {
        const files = Array.from(e.target.files);
        const maxSizeInBytes = 3 * 1024 * 1024; // 3 MB

        const allowedTypes = accept.split(",").map(type => type.trim());

        // Filter files based on size and type
        const validFiles = files.filter(file => {
            const isValidType = allowedTypes.some(allowedType => {
                if (allowedType === "image/*") {
                    return file.type.startsWith("image/");
                }
                return allowedType === file.type;
            });

            const isValidSize = file.size <= maxSizeInBytes;
            return isValidType && isValidSize;
        });

        if (validFiles.length < files.length) {
            setFileSizeError("Some files are too large or of invalid type. Please upload files smaller than 3 MB and of the correct type.");
        } else {
            setFileSizeError("");
        }

        setSelectedFiles(validFiles);

        // Upload the file using the postApi
        if (endpoint && validFiles.length > 0) {
            const formData = new FormData();
            validFiles.forEach(file => formData.append(valueKey, file));

            try {
                const response = await postApi({
                    end_point: endpoint,
                    body: formData,
                }).unwrap();

                const fileUrl = response;
                setFieldValue(name, fileUrl);
                showFileLink ? setResponseMsg(response) : toast.success("File uploaded successfully!");

            } catch (err) {
                console.error("File upload failed:", err);
                setError("File upload failed. Please try again.");
                // toast.error("File upload failed!");
            }
        }
    };

    const handleRemoveFile = (event, fileToRemove) => {
        event.stopPropagation();
        const updatedFiles = selectedFiles.filter(file => file !== fileToRemove);
        setSelectedFiles(updatedFiles);
        setValue(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    };

    return (
        <div>
            <div className="filegroup">
                {title && (
                    <label
                        className={`block mb-2 text-[#1D1D1F] text-base font-medium ${classLabel} ${horizontal ? "flex-0 mr-6 md:w-[100px] w-[60px] break-words" : ""
                            }`}
                    >
                        {title}
                    </label>
                )}
                {responseMsg && showFileLink &&
                    <a href={`${import.meta.env.VITE_MEDIA_URL}/${responseMsg}`} target="_blank" className="text-green-500 mb-2 flex items-center">
                        <Icon icon="mdi:eye" className="mr-2" />
                        See Uploaded File
                    </a>}
                <div className={`w-full h-[40px] file-control border rounded-lg border-gray-300 flex items-center ${className}`}>
                    <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        className="hidden"
                        name={name}
                        id={id}
                        multiple={multiple}
                        accept={accept} // Accept both images and PDFs
                    />
                    <div className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap cursor-pointer" onClick={() => fileInputRef.current.click()}>
                        {selectedFiles.length > 0 ? (
                            selectedFiles.map((file, index) => (
                                <span key={index} className="flex items-center text-gray-600 dark:text-white">
                                    {file.name}
                                    <button
                                        type="button"
                                        onClick={(event) => handleRemoveFile(event, file)}
                                        className="ml-2 text-red-500"
                                    >
                                        <Icon icon="heroicons:x-mark" width={18} />
                                    </button>
                                </span>
                            ))
                        ) : (
                            <span className="text-gray-400">
                                {placeholder}
                            </span>
                        )}
                    </div>
                    <span
                        className="file-name flex-none cursor-pointer border-l px-4 border-gray-300 dark:border-gray-700 h-full inline-flex items-center bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400 text-base rounded-tr-md rounded-br-md font-normal"
                        onClick={() => fileInputRef.current.click()}
                    >
                        {label}
                    </span>
                </div>
            </div>
            {
                description && (
                    <span className="text-gray-500 text-sm mt-2">{description}</span>
                )
            }
            {/* {isError && (
                <div className="text-red-500 mt-2 text-sm">
                    {meta.error}
                </div>
            )} */}
            {
                fileSizeError && (
                    <div className="text-red-500 mt-2 text-sm">
                        {fileSizeError}
                    </div>
                )
            }
        </div >
    );
};

export default FileUpload;
