import React from "react";
import { useField, useFormikContext } from "formik";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./customDatePickerWidth.css";

const DateTimePicker = ({
   name,
   label,
   required,
   height = "40px",
   width = "395px",
   inputType,
   placeholderText,
}) => {
   const { setFieldValue, values } = useFormikContext();
   const [field, meta] = useField(name);

   const formatDate = (date) => {
      if (!date) return "";
      if (inputType === "datetime") {
         return `${date.getFullYear()}-${(date.getMonth() + 1)
            .toString()
            .padStart(2, "0")}-${date
            .getDate()
            .toString()
            .padStart(2, "0")} ${date
            .getHours()
            .toString()
            .padStart(2, "0")}:${date
            .getMinutes()
            .toString()
            .padStart(2, "0")}:00`;
      } else {
         return `${date.getFullYear()}-${(date.getMonth() + 1)
            .toString()
            .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
      }
   };

   const handleDateChange = (date) => {
      setFieldValue(name, formatDate(date));
   };

   const defaultPlaceholderText =
      placeholderText ||
      (inputType === "datetime" ? "Select Date and Time" : "Select Date");

   return (
      <div className="customDatePickerWidth mb-4 w-full customDatePicker">
         <label
            htmlFor={name}
            className="block text-[#1D1D1F] text-base font-medium mb-2"
         >
            {label}
            {required && <span className="text-red-500">*</span>}
         </label>
         <DatePicker
            selected={values[name] ? new Date(values[name]) : null}
            onChange={handleDateChange}
            showTimeSelect={inputType === "datetime"}
            timeFormat="hh:mm aa"
            timeIntervals={15}
            placeholderText={defaultPlaceholderText}
            className={` w-full h-[${height}] rounded form-control cursor-pointer`}
            showIcon
            calendarIconClassName="mt-1 right-0"
            dateFormat={inputType === "datetime" ? "yyyy-MM-dd hh:mm aa" : "yyyy-MM-dd"}
         />
         {meta.touched && meta.error ? (
            <div className="text-red-500 text-sm">{meta.error}</div>
         ) : null}
      </div>
   );
};

export default DateTimePicker;