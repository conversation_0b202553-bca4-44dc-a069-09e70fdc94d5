import { Icon } from "@iconify/react";
import React from "react";
import Modal from "../Modal";

const ConfirmationModal = ({ isOpen, onClose, message, icon, confirm = false, onConfirm }) => {
    if (!isOpen) return null;

    return (
        <Modal title="Confirmation" activeModal={isOpen} onClose={onClose} className={"max-w-xl"}>
            <div className="rounded-lg p-5">
                <div className="flex items-center gap-3 mb-4">
                    {icon && <Icon icon={icon} className={`${confirm ? "text-green-500" : "text-red-500"} text-xl`} />}
                    <h2 className="text-lg font-semibold text-gray-800">Confirmation</h2>
                </div>
                <p className="text-sm text-gray-600 mb-6">{message}</p>
                <div className="flex justify-end gap-3">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={() => {
                            onConfirm();
                            onClose();
                        }}
                        className={`px-4 py-2 ${confirm ? "bg-green-500" : "bg-red-500"} text-white rounded hover:bg-red-600`}
                    >
                        Confirm
                    </button>
                </div>
            </div>
        </Modal>
    );
};

export default ConfirmationModal;
