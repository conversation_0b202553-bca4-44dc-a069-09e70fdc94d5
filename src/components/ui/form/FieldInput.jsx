import React, { forwardRef } from "react";
import { useField } from "formik";

const FieldInput = forwardRef(
  ({ placeholder, className, label, required, type, ...props }, ref) => {
    const [field, meta] = useField(props);
    const isError = meta.touched && meta.error;

    return (
      <div>
        {/* Label styling */}
        {label && (
          <label
            htmlFor={props.id || props.name}
            className="block text-gray-800 text-base font-semibold mb-2"
          >
            {label}
          </label>
        )}

        {/* Input field with proper value handling */}
        <input
          {...field}
          {...props}
          type={type}
          ref={ref}
          placeholder={placeholder}
          className={`${className} no-spinner focus:outline-none focus:border-transparent w-full ${
            isError ? 'text-red-500' : ''
          }`}
          value={field.value || ""} // Ensure Formik field value is used, fallback to empty string
        />

        {/* Error handling */}
        {/* {isError && <span className="text-red-500 text-sm mt-2 block">{meta.error}</span>} */}
      </div>
    );
  }
);

export default FieldInput;
