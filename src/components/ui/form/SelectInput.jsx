import React, { useState, useEffect, useRef } from "react";
import { useField } from "formik";
import { Icon } from "@iconify/react";

const SelectInput = ({
  label,
  options = [],
  placeholder,
  className,
  required,
  ...props
}) => {
  const [field, , helpers] = useField(props);
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState("");
  const dropdownRef = useRef(null); // Ref for the dropdown

  // Sync the selected state with the field's value (for initialization and updates)
  useEffect(() => {
    const selectedOption = options.find((option) => option.value === field.value);
    if (selectedOption) {
      setSelected(selectedOption.label);
    }
  }, [field.value, options]);

  const handleSelect = (option) => {
    setSelected(option.label);
    helpers.setValue(option.value);
    setOpen(false);
  };

  // Close dropdown when clicked outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownRef]);

  return (
    <div className={className} ref={dropdownRef}>
      <div className="relative w-fit text-white cursor-pointer">
        <p
          onClick={() => setOpen((prev) => !prev)}
          className={`rounded-sm flex gap-2 items-center px-3 py-2 ${
            label === "Status"
              ? selected === "Active"
                ? "bg-green-400"
                : selected === "Inactive"
                ? "bg-red-400"
                : "bg-blue-400"
              : "bg-blue-400"
          }`}
        >
          {selected ? selected : label}{" "}
          {selected ? (
            <Icon className="text-xl" icon="flat-color-icons:ok" />
          ) : (
            <Icon className="text-xl" icon="ic:round-plus" />
          )}
        </p>
        <ul
          className={`absolute top-12 z-50 w-full space-y-1 rounded-sm ${
            open ? "visible" : "invisible"
          }`}
        >
          {options.map((item, idx) => (
            <li
              key={idx}
              className={`rounded-sm bg-sky-400 mt-2 p-2 ${
                open ? "opacity-100 duration-500" : "opacity-0 duration-200"
              } hover:bg-sky-500 hover:text-white`}
              style={{
                transform: `translateX(${open ? 0 : (idx + 1) * 20}px)`,
              }}
              onClick={() => handleSelect(item)}
            >
              {item.label}
            </li>
          ))}
        </ul>
      </div>
      {field.touched && field.error ? (
        <div className="text-red-500 text-xs mt-2 text-start">{field.error}</div>
      ) : null}
    </div>
  );
};

export default SelectInput;
