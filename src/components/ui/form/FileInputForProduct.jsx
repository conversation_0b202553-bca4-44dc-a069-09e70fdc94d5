// import React, { useRef, useState } from "react";
// import { useField } from "formik";
// import Icon from "@/components/ui/Icon";

// const FileInputForProduct = ({
//   name,
//   initialImage,
//   label = "Browse",
//   placeholder = "Choose a file or drop it here...",
//   multiple,
//   accept = "image/*", // Accept only images by default
//   preview,
//   className = "",
//   id,
//   title = "",
//   description,
//   classLabel = "",
//   horizontal = false,
//   fileName,
// }) => {
//   const [field, , helpers] = useField({ name });
//   const [selectedFiles, setSelectedFiles] = useState([]);
//   const [error, setError] = useState("");
//   const fileInputRef = useRef(null);

//   const handleChange = (event) => {
//     const files = Array.from(event.currentTarget.files);
//     const maxSizeInBytes = 3 * 1024 * 1024; // 3 MB

//     // Filter files size limit
//     const validFiles = files.filter((file) => file.size <= maxSizeInBytes);

//     if (validFiles.length < files.length) {
//       setError(
//         "Some files are too large. Please upload files smaller than 3 MB."
//       );
//     } else {
//       setError("");
//     }

//     setSelectedFiles(validFiles);
//     helpers.setValue(multiple ? validFiles : validFiles[0]);

//     // Log selected files after setting state
//     // console.log("Selected Files: ", validFiles);
//   };

//   const handleRemoveFile = (event, fileToRemove) => {
//     event.stopPropagation();
//     const updatedFiles = selectedFiles.filter((file) => file !== fileToRemove);
//     setSelectedFiles(updatedFiles);
//     helpers.setValue(multiple ? updatedFiles : null);

//     if (fileInputRef.current) {
//       fileInputRef.current.value = "";
//     }
//   };

//   return (
//     <div className="my-5 flex items-center">
//       {selectedFiles.length > 0 ? (
//         <div className="group group-hover:cursor-pointer relative sm:w-[350px]">
//           <img
//             className="h-full w-full rounded-lg object-cover md:p-8"
//             src={URL.createObjectURL(selectedFiles[0])} // Show image preview
//             alt={selectedFiles[0]?.name}
//           />
//           <span
//             onClick={(e) => handleRemoveFile(e, selectedFiles[0])}
//             className="absolute top-0 right-0 z-30 flex h-[40px] w-[40px]"
//           >
//             <Icon
//               className="w-full h-6 text-gray-600"
//               icon="radix-icons:cross-2"
//             />
//           </span>
//         </div>
//       ) : (
//         <label
//           className="mx-auto flex max-w-[600px] flex-col items-center justify-center space-y-3 p-6"
//           htmlFor={name}
//         >
//           <div className="group group-hover:cursor-pointer relative sm:w-[350px]">
//             {!initialImage && <Icon
//               className="h-full w-full max-sm:w-40 md:p-5 object-cover rounded-lg"
//               icon="ph:image-thin"
//             />}
//             {initialImage && <img
//             className="h-full w-full rounded-lg object-cover md:p-8"
//             src={initialImage} // Show image preview
//             alt="img"
//           />}
//             <span className="absolute top-0 right-0 z-30 flex h-[40px] w-[40px]">
//               <Icon
//                 className="w-full h-6 text-gray-600"
//                 icon="akar-icons:edit"
//               />
//             </span>
//           </div>
//         </label>
//       )}

//       <input
//         ref={fileInputRef}
//         onChange={handleChange}
//         className="hidden"
//         id={name} // This links the input to the label's `htmlFor`
//         type="file"
//         accept={accept}
//         multiple={multiple}
//       />

//       {description && (
//         <span className="text-gray-500 text-sm mt-2">{description}</span>
//       )}
//       {error && <div className="text-red-500 mt-2 text-sm">{error}</div>}
//     </div>
//   );
// };

// export default FileInputForProduct;

// -------------------last final code =========

// import React, { useRef, useState } from "react";
// import { useField } from "formik";
// import Icon from "@/components/ui/Icon";

// const FileInputForProduct = ({
//   name,
//   initialImage,
//   label = "Browse",
//   placeholder = "Choose a file or drop it here...",
//   multiple,
//   accept = "image/*",
//   preview,
//   className = "",
//   id,
//   title = "",
//   description,
//   classLabel = "",
//   horizontal = false,
//   fileName,
// }) => {
//   const [field, , helpers] = useField({ name });
//   const [selectedFiles, setSelectedFiles] = useState([]);
//   const [error, setError] = useState("");
//   const fileInputRef = useRef(null);

//   const handleChange = (event) => {
//     const files = Array.from(event.currentTarget.files);
//     const maxSizeInBytes = 3 * 1024 * 1024; // 3 MB

//     // Filter files size limit
//     const validFiles = files.filter((file) => file.size <= maxSizeInBytes);

//     if (validFiles.length < files.length) {
//       setError(
//         "Some files are too large. Please upload files smaller than 3 MB."
//       );
//     } else {
//       setError("");
//     }

//     setSelectedFiles(validFiles);
//     helpers.setValue(multiple ? validFiles : validFiles[0]);

//     if (fileInputRef.current) {
//       fileInputRef.current.value = "";
//     }
//   };

//   const handleRemoveFile = (event, fileToRemove) => {
//     event.stopPropagation();
//     const updatedFiles = selectedFiles.filter((file) => file !== fileToRemove);
//     setSelectedFiles(updatedFiles);
//     helpers.setValue(multiple ? updatedFiles : null);
//   };

//   return (
//     <div className="my-5 flex flex-col items-center justify-center">
//       {selectedFiles.length > 0 ? (
//         <div className="group relative flex items-center justify-center w-[350px] h-[350px]">
//           <img
//             className="h-full w-full object-cover rounded-lg"
//             src={URL.createObjectURL(selectedFiles[0])} // Show image preview
//             alt={selectedFiles[0]?.name}
//           />
//           <span
//             onClick={(e) => handleRemoveFile(e, selectedFiles[0])}
//             className="absolute top-0 right-0 z-30 flex h-[40px] w-[40px]  rounded-full"
//           >
//             <Icon
//               className="w-full h-6 text-white-500 border border-danger-500 rounded-full m-2 text-danger-500"
//               icon="radix-icons:cross-2"
//             />
//           </span>
//         </div>
//       ) : (
//         <label
//           className="flex flex-col items-center justify-center max-w-[600px] space-y-3 p-6 "
//           htmlFor={name}
//         >
//           <div className="relative flex items-center justify-center w-[350px] h-[350px]">
//             {!initialImage && (
//               <Icon
//                 className="h-full w-full max-w-[200px] max-h-[200px] object-cover"
//                 icon="ph:image-thin"
//               />
//             )}
//             {initialImage && (
//               <img
//                 className="h-full w-full object-cover rounded-lg"
//                 src={initialImage} // Show current image preview
//                 alt="img"
//               />
//             )}
//             <span className="absolute top-0 right-0 z-30 flex h-[40px] w-[40px]">
//               <Icon
//                 className="w-full h-6 text-primary-500"
//                 icon="akar-icons:edit"
//               />
//             </span>
//           </div>
//         </label>
//       )}

//       <input
//         ref={fileInputRef}
//         onChange={handleChange}
//         className="hidden"
//         id={name}
//         type="file"
//         accept={accept}
//         multiple={multiple}
//       />

//       {description && (
//         <span className="text-gray-500 text-sm mt-2">{description}</span>
//       )}
//       {error && <div className="text-red-500 mt-2 text-sm">{error}</div>}
//     </div>
//   );
// };

// export default FileInputForProduct;

// -------------------last final code =========

import React, { useRef, useState } from "react";
import { useField } from "formik";
import Icon from "@/components/ui/Icon";

const FileInputForProduct = ({
  name,
  initialImage,
  label = "Browse",
  placeholder = "Choose a file or drop it here...",
  multiple,
  accept = "image/*", // Accept only images by default
  preview,
  className = "",
  id,
  title = "",
  description,
  classLabel = "",
  horizontal = false,
  fileName,
  imageWidth = "350px",
  imageHeight = "350px",
}) => {
  const [field, , helpers] = useField({ name });
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [error, setError] = useState("");
  const fileInputRef = useRef(null);

  const handleChange = (event) => {
    const files = Array.from(event.currentTarget.files);
    const maxSizeInBytes = 3 * 1024 * 1024; // 3 MB

    // Filter files size limit
    const validFiles = files.filter((file) => file.size <= maxSizeInBytes);

    if (validFiles.length < files.length) {
      setError(
        "Some files are too large. Please upload files smaller than 3 MB."
      );
    } else {
      setError("");
    }

    setSelectedFiles(validFiles);
    helpers.setValue(multiple ? validFiles : validFiles[0]);

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleRemoveFile = (event, fileToRemove) => {
    event.stopPropagation();
    const updatedFiles = selectedFiles.filter((file) => file !== fileToRemove);
    setSelectedFiles(updatedFiles);
    helpers.setValue(multiple ? updatedFiles : null);
  };

  return (
    <div className="my-5 flex flex-col items-center justify-center">
      {selectedFiles.length > 0 ? (
        <div
          className="group relative flex items-center justify-center"
          style={{ width: imageWidth, height: imageHeight }}
        >
          <img
            className="h-full w-full object-cover rounded-lg"
            src={URL.createObjectURL(selectedFiles[0])} // Show image preview
            alt={selectedFiles[0]?.name}
          />
          <span
            onClick={(e) => handleRemoveFile(e, selectedFiles[0])}
            className="absolute top-0 right-0 z-30 flex h-[40px] w-[40px]  rounded-full"
          >
            <Icon
              className="w-full h-6 text-white-500 border border-danger-500 rounded-full m-2 text-danger-500 bg-slate-300"
              icon="radix-icons:cross-2"
            />
          </span>
        </div>
      ) : (
        <label
          className="flex flex-col items-center justify-center max-w-[600px] space-y- p-2 border-2 border-slate-300 rounded-md"
          htmlFor={name}
        >
          <div
            className="relative flex items-center justify-center"
            style={{ width: imageWidth, height: imageHeight }}
          >
            {!initialImage && (
              <Icon
                className="h-full w-full max-w-[200px] max-h-[200px] object-cover"
                icon="ph:image-thin"
              />
            )}
            {initialImage && (
              <img
                className="h-full w-full object-cover rounded-lg"
                src={initialImage} // Show current image preview
                alt="img"
              />
            )}
            <span className="absolute top-0 right-0 z-30 flex h-[40px] w-[40px]">
              <Icon
                className="w-full h-6 text-primary-500"
                icon="akar-icons:edit"
              />
            </span>
          </div>
        </label>
      )}

      <input
        ref={fileInputRef}
        onChange={handleChange}
        className="hidden"
        id={name} // This links the input to the label's `htmlFor`
        type="file"
        accept={accept}
        multiple={multiple}
      />

      {description && (
        <span className="text-gray-500 text-sm mt-2">{description}</span>
      )}
      {error && <div className="text-red-500 mt-2 text-sm">{error}</div>}
    </div>
  );
};

export default FileInputForProduct;
