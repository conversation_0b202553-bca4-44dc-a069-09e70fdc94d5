import React, { useRef, useState } from "react";
import { useField } from "formik";
import Icon from "@/components/ui/Icon";

const InputFile = ({
    name,
    label = "Browse",
    placeholder = "Choose a file or drop it here...",
    multiple,
    accept = "image/*",  // Accept only images by default
    preview,
    className = "",
    id,
    title = "",
    description,
    classLabel = "",
    horizontal = false,
    fileName,
}) => {
    const [field, , helpers] = useField({ name });
    const [selectedFiles, setSelectedFiles] = useState([]);
    const [error, setError] = useState("");
    const fileInputRef = useRef(null);

    const handleChange = (event) => {
        const files = Array.from(event.currentTarget.files);
        const maxSizeInBytes = 3 * 1024 * 1024; // 3 MB

        // Filter files that exceed the size limit
        const validFiles = files.filter(file => file.size <= maxSizeInBytes);

        if (validFiles.length < files.length) {
            setError("Some files are too large. Please upload files smaller than 3 MB.");
        } else {
            setError(""); // Clear the error if all files are valid
        }

        setSelectedFiles(validFiles);
        helpers.setValue(multiple ? validFiles : validFiles[0]);
    };

    const handleRemoveFile = (event, fileToRemove) => {
        event.stopPropagation();
        const updatedFiles = selectedFiles.filter(file => file !== fileToRemove);
        setSelectedFiles(updatedFiles);
        helpers.setValue(multiple ? updatedFiles : null);

        // Reset the file input value to allow selecting the same file again
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    };

    const handleLabelClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    return (
        <div>
            <div className="filegroup">
                {title && (
                    <label
                        className={`block mb-2 text-[#1D1D1F] text-base font-medium ${classLabel} ${
                            horizontal ? "flex-0 mr-6 md:w-[100px] w-[60px] break-words" : ""
                        }`}
                    >
                        {title}
                    </label>
                )}
                <div className={`w-full h-[40px] file-control border rounded-lg border-gray-300 flex items-center ${className}`}>
                    <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleChange}
                        className="hidden"
                        name={name}
                        id={id}
                        multiple={multiple}
                        accept={accept}
                    />
                    <div className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap cursor-pointer" onClick={handleLabelClick}>
                        {selectedFiles.length > 0 ? (
                            selectedFiles.map((file, index) => (
                                <span key={index} className="flex items-center text-gray-600 dark:text-white">
                                    {fileName || file.name}
                                    <button
                                        type="button"
                                        onClick={(event) => handleRemoveFile(event, file)}
                                        className="ml-2 text-red-500"
                                    >
                                        <Icon icon="heroicons:x-mark" width={18} />
                                    </button>
                                </span>
                            ))
                        ) : (
                            <span className="text-gray-400">
                                {placeholder}
                            </span>
                        )}
                    </div>
                    <span className="file-name flex-none cursor-pointer border-l px-4 border-gray-300 dark:border-gray-700 h-full inline-flex items-center bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400 text-base rounded-tr-md rounded-br-md font-normal" onClick={handleLabelClick}>
                        {label} 
                    </span>
                </div>
            </div>
            {description && (
                <span className="text-gray-500 text-sm mt-2">{description}</span>
            )}
            {error && (
                <div className="text-red-500 mt-2 text-sm">
                    {error}
                </div>
            )}
        </div>
    );
};

export default InputFile;
