import React from 'react';
import { useField } from 'formik';

const FieldOFTextArea = ({ className, label, required, columns = 5, ...props }) => {
    const [field, meta] = useField(props);

    return (
        <div className="mb-4">
            {/* <label
                htmlFor={props.id || props.name}
                className="block text-gray-700 text-base font-medium mb-2"
            >
                {label} {required && <span className="text-red-500">*</span>}
            </label> */}
            <textarea
                className={`${className} w-full border-transparent focus:outline-none bg-gray-100 rounded-lg p-3 h-28 ${meta.touched && meta.error ? 'text-red-500' : ''
                    }`}
                    placeholder={label}
                rows={columns}
                {...field}
                {...props}
            />
            {meta.touched && meta.error && (
                <div className="text-red-500 text-xs mt-1">{meta.error}</div>
            )}
        </div>
    );
};

export default FieldOFTextArea;
