import React from 'react';

const TableSkeleton = ({ columns, actions = [] }) => {
  const renderSkeletonRows = () => {
    return Array(10).fill(0).map((_, rowIndex) => (
      <tr key={rowIndex}>
        {Array(columns.length + (actions.length > 0 ? 1 : 0)).fill(0).map((_, colIndex) => (
          <td key={colIndex} className="table-td">
            <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
          </td>
        ))}
      </tr>
    ));
  };

  const renderSkeletonHeader = () => {
    return (
      <tr>
        {columns.map((_, index) => (
          <th key={index} className="table-th">
            <div className="h-4 w-20 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
          </th>
        ))}
        {actions.length > 0 && (
          <th className="table-th">
            <div className="h-4 w-16 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
          </th>
        )}
      </tr>
    );
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
        <thead className="bg-slate-200 dark:bg-slate-700 w-full">
          {renderSkeletonHeader()}
        </thead>
        <tbody className="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
          {renderSkeletonRows()}
        </tbody>
      </table>
    </div>
  );
};

export default TableSkeleton;
