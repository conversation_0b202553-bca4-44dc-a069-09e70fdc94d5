import React, { useState } from "react";
import Card from "@/components/ui/Card";
import GlobalFilter from "./GlobalFilter";
import Dropdown from "@/components/ui/Dropdown";
import Icon from "@/components/ui/Icon";
import { Menu } from "@headlessui/react";
import Pagination from "./pagination";
import { useDispatch, useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { useNavigate } from "react-router-dom";
import TableSkeleton from "@/components/ui/TableSkeleton";

const BasicTablePage = ({
  title,
  createButton,
  BackButton,
  statusCheckReport,
  createPage,
  editPage,
  actions = [],
  columns,
  changePage,
  data,
  filter,
  setFilter,
  currentPage,
  totalPages,
  goto = "",
  gotoLink = "",
  submitForm,
  loading
}) => {
  const dispatch = useDispatch();
  const { showModal, showEditModal } = useSelector(
    (state) => state.commonReducer
  );
  console.log(showModal);
  const handlePageChange = (value) => {
    changePage(`?page=${value}`);
  };
  const openCreateModal = () => {
    dispatch(setShowModal(true));
  };
  const navigate = useNavigate();

  return (
    <div className="">
      <Card noborder>
        <div className="md:flex justify-between items-center mb-6">
          <h4 className="card-title mb-5 lg:mb-0 md:mb-0">
            {title}{" "}
          </h4>
          {statusCheckReport && <>{statusCheckReport}</>}
          <div className="flex flex-wrap gap-2 md:flex-nowrap">
            {filter}
            <div className="w-full md:w-auto">
              <GlobalFilter filter={filter} setFilter={setFilter} />
            </div>
            {goto && gotoLink && (
              <button
                className="btn btn-primary btn-sm w-full md:w-auto flex justify-center items-center"
                onClick={() => navigate(`${gotoLink}`)}
              >
                {goto}
              </button>
            )}

            {createButton && (
              <button
                className="btn btn-primary btn-sm w-full md:w-auto flex justify-center items-center"
                onClick={() => openCreateModal()}
              >
                {createButton}
              </button>

            )}
            {BackButton && <>{BackButton}</>}

            {showModal && createPage}
            {showEditModal && editPage}
          </div>
        </div>
        {loading ? (
          <TableSkeleton columns={columns} actions={actions} />
        ) :
          data?.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                <thead className="bg-slate-200 dark:bg-slate-700 w-full">
                  <tr className="w-full">
                    {columns.map((column, i) => (
                      <th key={i} scope="" className=" table-th ">
                        {column.label}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                  {data?.map((row, dataIndex) => {
                    // const isLastRow = dataIndex === data.length - 1;
                    // const isSecondLastRow = dataIndex === data.length - 2;
                    return (
                      <tr
                        key={dataIndex}
                        className={`hover:bg-slate-200 dark:hover:bg-slate-700 ${row.rowColor}`}
                      >
                        {columns.map(
                          (column, index) =>
                            column.field && (
                              <td key={index} className="table-td">
                                {row[column.field]}
                              </td>
                            )
                        )}
                        {actions.length > 0 && (
                          <td className="table-td ">
                            <Dropdown
                              // classMenuItems={`right-0 w-[140px]  ${isLastRow || isSecondLastRow ? '-top-[690%]' : 'top-[110%]'}`}
                              classMenuItems={`right-0 w-[140px] top-[110%]`}
                              label={
                                <span className="text-xl text-center block w-full">
                                  <Icon icon="heroicons-outline:dots-vertical" />
                                </span>
                              }
                            >
                              <div className="divide-y divide-slate-100 dark:divide-slate-800">
                                {actions.map((item, i) => (
                                  <Menu.Item key={i}>
                                    <div
                                      className={`
                              w-full border-b border-b-gray-500 border-opacity-10 px-4 py-2 text-sm  last:mb-0 cursor-pointer 
                              first:rounded-t last:rounded-b flex space-x-2 items-center rtl:space-x-reverse `}
                                      onClick={() => item.onClick(dataIndex)}
                                    >
                                      <span className="text-base">
                                        <Icon icon={item.icon} />
                                      </span>
                                      <span>{item.name} </span>
                                    </div>
                                  </Menu.Item>
                                ))}
                              </div>
                            </Dropdown>
                          </td>
                        )}
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            "No data found"
          )}
        {totalPages > 0 && (
          <Pagination
            totalPages={totalPages}
            currentPage={currentPage}
            handlePageChange={handlePageChange}
          />
        )}
      </Card>
    </div>
  );
};

export default BasicTablePage;
