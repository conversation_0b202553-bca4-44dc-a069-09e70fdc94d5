import React, { useState } from "react";
import Datepicker from "react-tailwindcss-datepicker";

const DateFilter = ({ value, onChange }) => {
  const [dateRange, setDateRange] = useState({
    startDate: value?.startDate || null,
    endDate: value?.endDate || null,
  });

  const handleValueChange = (newValue) => {
    if (!newValue?.startDate || !newValue?.endDate) return;

    const formattedDates = {
      startDate: newValue.startDate,
      endDate: newValue.endDate,
      rawValue: newValue,
    };

    setDateRange({
      startDate: newValue.startDate,
      endDate: newValue.endDate,
    });

    onChange(formattedDates); 
  };

  return (
    <div className="w-72">
      <Datepicker
        value={dateRange}
        onChange={handleValueChange}
        showShortcuts={true}
        primaryColor="blue"
        displayFormat="DD-MM-YYYY"
        asSingle={false} 
        useRange={true} 
        inputClassName="w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm placeholder:text-slate-400 focus:outline-none focus:ring-2 focus:ring-primary-500 dark:border-slate-600 dark:placeholder:text-slate-500"
      />
    </div>
  );
};

export default DateFilter;
