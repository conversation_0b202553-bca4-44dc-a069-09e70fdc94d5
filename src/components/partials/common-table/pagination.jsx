import React, { useState, useEffect } from "react";
import Icon from "@/components/ui/Icon";

const Pagination = ({
  totalPages,
  currentPage,
  handlePageChange,
  text,
  className = "custom-class",
}) => {
  const [pages, setPages] = useState([]);

  // Calculate the visible range of pages
  useEffect(() => {
    let pagesToDisplay = [];

    // Determine the start and end range of the visible pages
    const startFrom = Math.max(1, currentPage - 1); // Start from two pages before the current page
    const endTo = Math.min(totalPages, startFrom + 3); // End 3 pages ahead of the start

    for (let i = startFrom; i <= endTo; i++) {
      pagesToDisplay.push(i);
    }

    setPages(pagesToDisplay);
  }, [currentPage, totalPages]);

  return (
    <div className={`${className} mt-4`}>
      <ul className="pagination flex items-center space-x-2 justify-end mr-5">
        <li>
          {text ? (
            <button
              className="text-slate-600 dark:text-slate-300 prev-next-btn"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </button>
          ) : (
            <button
              className="text-xl leading-4 text-slate-900 dark:text-white h-6 w-6 flex items-center justify-center flex-col prev-next-btn"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <Icon icon="heroicons-outline:chevron-left" />
            </button>
          )}
        </li>

        {/* Show '...' only if currentPage is sufficiently far from the first page */}
        {currentPage > 3 && (
          <>
            <li>
              <button className="page-link" onClick={() => handlePageChange(1)}>
                1
              </button>
            </li>
            {currentPage > 4 && <li>...</li>} {/* Ellipsis only if there's a gap */}
          </>
        )}

        {/* Render page numbers */}
        {pages.map((page) => (
          <li key={page}>
            <button
              className={`${page === currentPage ? "active" : ""} page-link`}
              onClick={() => handlePageChange(page)}
              disabled={page === currentPage}
            >
              {page}
            </button>
          </li>
        ))}

        {/* Show '...' only if currentPage is sufficiently far from the last page */}
        {currentPage < totalPages - 2 && (
          <>
            {currentPage < totalPages - 3 && <li>...</li>} {/* Ellipsis only if there's a gap */}
            <li>
              <button
                className="page-link"
                onClick={() => handlePageChange(totalPages)}
              >
                {totalPages}
              </button>
            </li>
          </>
        )}

        <li>
          {text ? (
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="text-slate-600 dark:text-slate-300 prev-next-btn"
            >
              Next
            </button>
          ) : (
            <button
              className="text-xl leading-4 text-slate-900 dark:text-white h-6 w-6 flex items-center justify-center flex-col prev-next-btn"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              <Icon icon="heroicons-outline:chevron-right" />
            </button>
          )}
        </li>
      </ul>
    </div>
  );
};

export default Pagination;
