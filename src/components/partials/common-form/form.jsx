import React, { useState, useRef } from "react";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import Textinput from "@/components/ui/Textinput";

const Form = ({items}) => {
  return (
    <div className="text-base text-slate-600 dark:text-slate-300">
        <Textinput
            label="Email"
            type="email"
            placeholder="Type your email"
        />
        <Textinput
            label="Password"
            type="password"
            placeholder="8+ characters, 1 capitat letter "
        />
    </div>
    );
};

export default Form;
