// import React from "react";
// import Icon from "@/components/ui/Icon";
// import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
// import Tooltip from "@/components/ui/Tooltip";

// const GroupChart3 = () => {
//   const { data: dashboardData } = useGetApiQuery({ url: "dashboard" });

//   const statistics = [
//     {
//       title: "Available Product",
//       count: dashboardData?.total_sale_qty,
//       count1: dashboardData?.total_available_qty,
//       bgColor: "bg-halfDutchWhite-50",
//       textColor: "text-chalky-200",
//       icon: "akar-icons:equal",
//       countColorOne: "text-green-700",
//       countColorTwo: "text-primary-900",
//       tooltipTextOne: "Sold",
//       tooltipTextTwo: "Available",
//       totalStatus: "Total Product",
//     },
//     {
//       title: "Generated Invoice",
//       count: dashboardData?.total_sales,
//       bgColor: "bg-selago-50",
//       textColor: "text-portage-400",
//       icon: "heroicons:tag",
//       countColorOne: "text-downriver-900",
//       countColorTwo: "text-downriver-900",
//       tooltipTextOne: "Count",
//       tooltipTextTwo: "Count",
//       totalStatus: "From Today",
//     },
//     {
//       title: "Service Expense",
//       count: dashboardData?.total_expense,
//       bgColor: "bg-seashellPeach-50",
//       textColor: "text-monaLisa-300",
//       icon: "system-uicons:fullscreen",
//       takaIcon: "tabler:coin-taka",
//       countColorOne: "text-downriver-900",
//       countColorTwo: "text-downriver-900",
//       tooltipTextOne: "Count",
//       tooltipTextTwo: "Count",
//       totalStatus: "From Today",
//     },
//     {
//       title: "Pending Task",
//       count: dashboardData?.total_tasks,
//       bgColor: "bg-frostee-100",
//       textColor: "text-celadon-300",
//       icon: "icon-park-outline:loading-one",
//       countColorOne: "text-downriver-900",
//       countColorTwo: "text-downriver-900",
//       tooltipTextOne: "Count",
//       tooltipTextTwo: "Count",
//       totalStatus: "From Today",
//     },
//   ];

//   return (
//     <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
//       {statistics.map((item, i) => (
//         <div
//           key={i}
//           className="bg-white p-4 flex items-center justify-between border rounded-md border-[#EDF4FF]  shadow-lg shadow-[#edf4ff80] drop-shadow"
//         >
//           <div
//             className={`w-11 h-11 flex items-center justify-center rounded-full mb-4 ${item.bgColor}`}
//           >
//             <Icon
//               icon={item.icon}
//               className={`w-6 h-6 font-extrabold ${item.textColor}`}
//             />
//           </div>
//           <div className="text-en">
//             <h3 className="text-lg font-semibold text-fuscousGray-700 dark:text-slate-200">
//               {item.title}
//             </h3>

//             <div className="flex gap- items-end justify-end mx-auto">
//               {item.count || item.count1 ? (
//                 <>
//                   {item.count && (
//                     <Tooltip
//                       content={
//                         <>
//                           <span className="font-bold">
//                             {item.tooltipTextOne}
//                           </span>
//                         </>
//                       }
//                       placement="top"
//                       arrow
//                       animation="interactive"
//                     >
//                       <p
//                         className={`text-2xl font-bold my-2 p-2  ${item.countColorOne}`}
//                       >
//                         {item.count}
//                       </p>
//                     </Tooltip>
//                   )}

//                   {item.count1 && (
//                     <Tooltip
//                       content={
//                         <>
//                           <span className="font-bold">
//                             {item.tooltipTextTwo}
//                           </span>
//                         </>
//                       }
//                       placement="top"
//                       arrow
//                       animation="interactive"
//                     >
//                       <p
//                         className={`text-2xl font-bold my-2 p-2 ${item.countColorTwo}`}
//                       >
//                         <span className="font-bold text-slate-500 me-2">/</span>
//                         {item.count1}
//                       </p>
//                     </Tooltip>
//                   )}
//                 </>
//               ) : (
//                 <p className="text-2xl font-bold text-downriver-900 my-2 p-2">
//                   0
//                 </p>
//               )}
//             </div>

//             <div className="text-sm text-fuscousGray-500  mt-1 text-end">
//               {item.totalStatus}
//             </div>
//           </div>
//         </div>
//       ))}
//     </div>
//   );
// };

// export default GroupChart3;

// import React from "react";
// import Icon from "@/components/ui/Icon";
// import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
// import Tooltip from "@/components/ui/Tooltip";

// const GroupChart3 = () => {
//   const { data: dashboardData } = useGetApiQuery({ url: "dashboard" });

//   const statistics = [
//     {
//       title: "Available Product",
//       count: dashboardData?.total_sale_qty,
//       count1: dashboardData?.total_available_qty,
//       bgColor: "bg-halfDutchWhite-50",
//       textColor: "text-chalky-200",
//       icon: "akar-icons:equal",
//       countColorOne: "text-green-700",
//       countColorTwo: "text-primary-900",
//       tooltipTextOne: "Sold",
//       tooltipTextTwo: "Available",
//       totalStatus: "Total Product",
//     },
//     {
//       title: "Generated Invoice",
//       count: dashboardData?.total_sales,
//       bgColor: "bg-selago-50",
//       textColor: "text-portage-400",
//       icon: "heroicons:tag",
//       countColorOne: "text-downriver-900",
//       countColorTwo: "text-downriver-900",
//       tooltipTextOne: "Count",
//       tooltipTextTwo: "Count",
//       totalStatus: "From Today",
//     },
//     {
//       title: "Service Expense",
//       // count: dashboardData?.total_expense,
//       count: 10,
//       bgColor: "bg-seashellPeach-50",
//       textColor: "text-monaLisa-300",
//       icon: "system-uicons:fullscreen",
//       takaSymbol: "	৳",
//       countColorOne: "text-downriver-900",
//       countColorTwo: "text-downriver-900",
//       tooltipTextOne: "Expense",
//       tooltipTextTwo: "Expense",
//       totalStatus: "From Today",
//     },
//     {
//       title: "Pending Task",
//       count: dashboardData?.total_tasks,
//       bgColor: "bg-frostee-100",
//       textColor: "text-celadon-300",
//       icon: "icon-park-outline:loading-one",
//       countColorOne: "text-downriver-900",
//       countColorTwo: "text-downriver-900",
//       tooltipTextOne: "Count",
//       tooltipTextTwo: "Count",
//       totalStatus: "From Today",
//     },
//   ];

//   return (
//     <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
//       {statistics.map((item, i) => (
//         <div
//           key={i}
//           className="bg-white p-4 flex items-center justify-between border rounded-md border-[#EDF4FF] shadow-lg shadow-[#edf4ff80] drop-shadow"
//         >
//           <div
//             className={`w-11 h-11 flex items-center justify-center rounded-full mb-4 ${item.bgColor}`}
//           >
//             <Icon
//               icon={item.icon}
//               className={`w-6 h-6 font-extrabold ${item.textColor}`}
//             />
//           </div>
//           <div className="text-en">
//             <h3 className="text-lg font-semibold text-fuscousGray-700 dark:text-slate-200">
//               {item.title}
//             </h3>

//             <div className="flex gap- items-end justify-end mx-auto">
//               {item.count || item.count1 ? (
//                 <>
//                   {item.count && (
//                     <Tooltip
//                       content={
//                         <>
//                           <span className="font-bold">
//                             {item.tooltipTextOne}
//                           </span>
//                         </>
//                       }
//                       placement="top"
//                       arrow
//                       animation="interactive"
//                     >
//                       <p
//                         className={`text-2xl font-bold my-2 p-2  ${item.countColorOne}`}
//                       >
//                         {item.takaSymbol && item.title === "Service Expense"
//                           ? `${item.takaSymbol}${item.count}`
//                           : item.count}
//                       </p>
//                     </Tooltip>
//                   )}

//                   {item.count1 && (
//                     <Tooltip
//                       content={
//                         <>
//                           <span className="font-bold">
//                             {item.tooltipTextTwo}
//                           </span>
//                         </>
//                       }
//                       placement="top"
//                       arrow
//                       animation="interactive"
//                     >
//                       <p
//                         className={`text-2xl font-bold my-2 p-2 ${item.countColorTwo}`}
//                       >
//                         <span className="font-bold text-slate-500 me-2">/</span>
//                         {item.count1}
//                       </p>
//                     </Tooltip>
//                   )}
//                 </>
//               ) : (
//                 <p className="text-2xl font-bold text-downriver-900 my-2 p-2">
//                   0
//                 </p>
//               )}
//             </div>

//             <div className="text-sm text-fuscousGray-500 mt-1 text-end">
//               {item.totalStatus}
//             </div>
//           </div>
//         </div>
//       ))}
//     </div>
//   );
// };

// export default GroupChart3;

// import React from "react";
// import Icon from "@/components/ui/Icon";
// import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
// import Tooltip from "@/components/ui/Tooltip";
// import { useNavigate } from "react-router-dom";

// const GroupChart3 = () => {
//   const navigate = useNavigate();
//   const { data: dashboardData } = useGetApiQuery({ url: "dashboard" });

//   const statistics = [
//     {
//       title: "Available Product",
//       count: dashboardData?.total_sale_qty || 0,
//       count1: dashboardData?.total_available_qty || 0,
//       bgColor: "bg-halfDutchWhite-50",
//       textColor: "text-chalky-200",
//       icon: "akar-icons:equal",
//       countColorOne: "text-green-700",
//       countColorTwo: "text-primary-900",
//       tooltipTextOne: "Sold",
//       tooltipTextTwo: "Available",
//       totalStatus: "Total Product",
//       goto: "/inventory-list",
//     },
//     {
//       title: "Generated Invoice",
//       count: dashboardData?.total_sales || 0,
//       bgColor: "bg-selago-50",
//       textColor: "text-portage-400",
//       icon: "heroicons:tag",
//       countColorOne: "text-downriver-900",
//       countColorTwo: "text-downriver-900",
//       tooltipTextOne: "Count",
//       tooltipTextTwo: "Count",
//       totalStatus: "From Today",
//       goto: "/sales-list",
//     },
//     {
//       title: "Service Expense",
//       count: dashboardData?.total_expense || 0,
//       bgColor: "bg-seashellPeach-50",
//       textColor: "text-monaLisa-300",
//       icon: "system-uicons:fullscreen",
//       takaSymbol: "৳",
//       countColorOne: "text-downriver-900",
//       countColorTwo: "text-downriver-900",
//       tooltipTextOne: "Expense",
//       tooltipTextTwo: "Expense",
//       totalStatus: "From Today",
//       goto: "",
//     },
//     {
//       title: "Pending Task",
//       count: dashboardData?.total_tasks || 0,
//       bgColor: "bg-frostee-100",
//       textColor: "text-celadon-300",
//       icon: "icon-park-outline:loading-one",
//       countColorOne: "text-downriver-900",
//       countColorTwo: "text-downriver-900",
//       tooltipTextOne: "Count",
//       tooltipTextTwo: "Count",
//       totalStatus: "From Today",
//       goto: "/support-list",
//     },
//   ];

//   return (
//     <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
//       {statistics.map((item, i) => (
//         <div
//           key={i}
//           onClick={() => navigate(item.goto)}
//           className="bg-white p-4 flex items-center justify-between border rounded-md border-[#EDF4FF] shadow-lg shadow-[#edf4ff80] drop-shadow cursor-pointer"
//         >
//           <div
//             className={`w-11 h-11 flex items-center justify-center rounded-full mb-4 ${item.bgColor}`}
//           >
//             <Icon
//               icon={item.icon}
//               className={`w-6 h-6 font-extrabold ${item.textColor}`}
//             />
//           </div>
//           <div className="text-en">
//             <h3 className="text-lg font-semibold text-fuscousGray-700 dark:text-slate-200">
//               {item.title}
//             </h3>

//             <div className="flex gap- items-end justify-end mx-auto">
//               <Tooltip
//                 content={
//                   <>
//                     <span className="font-bold">{item.tooltipTextOne}</span>
//                   </>
//                 }
//                 placement="top"
//                 arrow
//                 animation="interactive"
//               >
//                 <p
//                   className={`text-2xl font-bold my-2 p-2 ${item.countColorOne}`}
//                 >
//                   {item.takaSymbol && item.title === "Service Expense"
//                     ? `${item.takaSymbol}${item.count}`
//                     : item.count}
//                 </p>
//               </Tooltip>

//               {item.count1 && (
//                 <Tooltip
//                   content={
//                     <>
//                       <span className="font-bold">{item.tooltipTextTwo}</span>
//                     </>
//                   }
//                   placement="top"
//                   arrow
//                   animation="interactive"
//                 >
//                   <p
//                     className={`text-2xl font-bold my-2 p-2 ${item.countColorTwo}`}
//                   >
//                     <span className="font-bold text-slate-500 me-2">/</span>
//                     {item.count1}
//                   </p>
//                 </Tooltip>
//               )}
//             </div>

//             <div className="text-sm text-fuscousGray-500 mt-1 text-end">
//               {item.totalStatus}
//             </div>
//           </div>
//         </div>
//       ))}
//     </div>
//   );
// };

// export default GroupChart3;
