import React from "react";
import products1 from "@/assets/images/all-img/p-1.png";
import products2 from "@/assets/images/all-img/p-2.png";
import products3 from "@/assets/images/all-img/p-3.png";
import products4 from "@/assets/images/all-img/p-4.png";
import products5 from "@/assets/images/all-img/p-5.png";
import products6 from "@/assets/images/all-img/p-6.png";
const products = [
  {
    img: "https://i.ytimg.com/vi/CTs8GckE_x0/maxresdefault.jpg",
    price: "$500000.00",
    title: "Digital Smart Board",
  },
  {
    img: "https://ryans.com/storage/products/main/dell-inspiron-15-3501-10th-gen-intel-core-i3-11612426462.webp",
    price: "$45500.00",
    title: "Dell Inspiron 15 3501 Laptop",
  },
  {
    img: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSlsVd9ZfIJxK7zVUlHScfOCDNSMIfuYCKWbg&s",
    price: "$350000.00",
    title: "BacBon Smart Solution",
  },
  {
    img: "https://cdn1.smartprix.com/rx-iHDoDXknd-w420-h420/hp-15s-fr4001tu-lapt.jpg",
    price: "$55000.00",
    title: "HP 15s-fr4001TU Laptop",
  },
  {
    img: "https://electronics.mrinmoy.com.bd/wp-content/uploads/2024/01/Aweo-A200BL-in-BD.webp",
    price: "$1500.00",
    title: "Awei A200BL Bluetooth Headphone",
  },
  {
    img: "https://i5.walmartimages.com/seo/Oculus-Quest-128GB-VR-Headset_43a7f582-63ed-4447-a2b2-40469b798f94_2.68a36a95313362add36073f6ff17d9d4.jpeg",
    price: "$45000.00",
    title: "Oculus VR Headset",
  },
];
const Products = () => {
  return (
    <div className="grid md:grid-cols-3 grid-cols-1 gap-5">
      {products.map((item, i) => (
        <div
          key={i}
          className="bg-slate-50 dark:bg-slate-900 p-4 rounded text-center"
        >
          <div className="h-20 w-40 rounded-lg mb-4 mx-auto">
            <img src={item.img} alt="" className="w-full h-full rounded-lg" />
          </div>
          <span className="text-slate-500 dark:text-slate-300 text-sm mb-1 block font-normal">
            {item.price}
          </span>
          <span className="text-slate-600 dark:text-slate-300 text-sm mb-4 block">
            {item.title}
          </span>
          {/* <a
            href="#"
            className="btn btn-secondary dark:bg-slate-800 dark:hover:bg-slate-600 block w-full text-center btn-sm"
          >
            View More
          </a> */}
        </div>
      ))}
    </div>
  );
};

export default Products;
