import React, { useEffect, useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import Icon from "@/components/ui/Icon";
import { useDispatch } from "react-redux";
import useMobileMenu from "@/hooks/useMobileMenu";
import Submenu from "./Submenu";
import { useSelector } from "react-redux";

const Navmenu = ({ menus }) => {
  const [activeSubmenu, setActiveSubmenu] = useState(null);
  const [activeMultiMenu, setMultiMenu] = useState(null);

  const { user } = useSelector((state) => state.auth);

  const location = useLocation();
  const locationName = location.pathname.replace("/", "");
  const [mobileMenu, setMobileMenu] = useMobileMenu();

  const toggleSubmenu = (i) => {
    if (activeSubmenu === i) {
      setActiveSubmenu(null);
    } else {
      setActiveSubmenu(i);
    }
  };

  const toggleMultiMenu = (j) => {
    if (activeMultiMenu === j) {
      setMultiMenu(null);
    } else {
      setMultiMenu(j);
    }
  };

  const isLocationMatch = (targetLocation) => {
    return (
      locationName === targetLocation ||
      locationName.startsWith(`${targetLocation}/`)
    );
  };

  useEffect(() => {
    let submenuIndex = null;
    let multiMenuIndex = null;

    menus?.forEach((item, i) => {
      if (isLocationMatch(item.url ? item.url : "#")) {
        submenuIndex = i;
      }

      if (item?.sub_menus?.length > 0) {
        item.sub_menus.forEach((childItem, j) => {
          if (isLocationMatch(childItem.url)) {
            submenuIndex = i;
          }

          if (childItem.multi_menu) {
            childItem.multi_menu.forEach((nestedItem) => {
              if (isLocationMatch(nestedItem.multiLink)) {
                submenuIndex = i;
                multiMenuIndex = j;
              }
            });
          }
        });
      }
    });

    setActiveSubmenu(submenuIndex);
    setMultiMenu(multiMenuIndex);

    if (mobileMenu) {
      setMobileMenu(false);
    }
  }, [location]);

  return (
    <>
      <ul>
        {menus?.map((item, i) => (
          <li
            key={i}
            className={`single-sidebar-menu 
              ${item.sub_menus ? "item-has-children" : ""}
              ${activeSubmenu === i ? "open" : ""}
              ${locationName === item.url ? "menu-item-active" : ""}`}
          >
            {/* single menu with no submenus */}
            {/* {!item.child && !item.isHeadr && (
              <>
                <div className="menu-link" style={{ pointerEvents: "none" }}>
                  <span className="menu-icon flex-grow-0">
                    <Icon icon={item.icon} />
                  </span>
                  <div className="text-box flex-grow">{item.title}</div>
                  {item.badge && <span className="menu-badge">{item.badge}</span>}
                </div>
                :
                <NavLink className="menu-link" to={item.link} >
                  <span className="menu-icon flex-grow-0">
                    <Icon icon={item.icon} />
                  </span>
                  <div className="text-box flex-grow">{item.title}</div>
                  {item.badge && <span className="menu-badge">{item.badge}</span>}
                </NavLink>
              </>
            )} */}
            {!item?.sub_menus?.length && (
              <NavLink className="menu-link" to={item.url}>
                <span className="menu-icon flex-grow-0">
                  <Icon icon={item.icon} />
                </span>
                <div className="text-box flex-grow">{item.name}</div>
                {item.badge && <span className="menu-badge">{item.badge}</span>}
              </NavLink>
            )}

            {/* Menu with submenus */}
            {item.sub_menus?.length > 0 && (
              <div
                className={`menu-link ${activeSubmenu === i ? "parent_active not-collapsed" : "collapsed"}`}
                onClick={() => toggleSubmenu(i)}  // Toggle only the parent menu
              >
                <div className="flex-1 flex items-start">
                  <span className="menu-icon">
                    <Icon icon={item.icon} />
                  </span>
                  <div className="text-box">{item.name}</div>
                </div>
                <div className="flex-0">
                  <div className={`menu-arrow transform transition-all duration-300 ${activeSubmenu === i ? "rotate-90" : ""}`}>
                    <Icon icon="heroicons-outline:chevron-right" />
                  </div>
                </div>
              </div>
            )}

            {/* Pass submenu and multi-menu props */}
            <Submenu
              activeSubmenu={activeSubmenu}
              item={item}
              i={i}
              toggleMultiMenu={toggleMultiMenu}
              activeMultiMenu={activeMultiMenu}
            />
          </li>
        ))}
      </ul>
    </>
  );
};

export default Navmenu;
