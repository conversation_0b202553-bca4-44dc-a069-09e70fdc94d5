import React, { useRef, useState } from "react";
import Flatpickr from "react-flatpickr";
// import "flatpickr/dist/themes/material_blue.css";
import { Icon } from '@iconify/react';
// import calendarIcon from '@iconify-icons/mdi/calendar'; 

const DatePicker = ({
  label,
  placeholder = "mm/dd/yyyy",
  classLabel = "form-label",
  className = "",
  classGroup = "",
  register,
  name,
  readonly,
  value,
  error,
  icon,
  disabled,
  id,
  horizontal,
  validate,
  onChange,
  options,
  onFocus,
  defaultValue,
  msgTooltip,
  description,
  ...rest
}) => {
  const [selectedDate, setSelectedDate] = useState(defaultValue || null);
  const datepickerRef = useRef(null);

  const handleIconClick = () => {
    if (datepickerRef.current) {
      datepickerRef.current.flatpickr.open();
    }
  };

  return (
    <div
      className={`formGroup ${error ? "has-error" : ""} ${
        horizontal ? "flex" : ""
      } ${validate ? "is-valid" : ""}`}
    >
      {label && (
        <label
          htmlFor={id}
          className={`block text-[#1D1D1F] text-base font-medium mb-2 ${classLabel} ${
            horizontal ? "flex-0 mr-6 md:w-[100px] w-[60px] break-words" : ""
          }`}
        >
          {label}
        </label>
      )}
      <div className={`relative ${horizontal ? "flex-1" : ""}`}>
        <Flatpickr
          ref={datepickerRef}
          {...(register ? register(name) : {})}
          {...rest}
          value={selectedDate}
          onChange={(date) => {
            setSelectedDate(date);
            if (onChange) onChange(date);
          }}
          options={options}
          className={`${
            error ? " has-error" : " "
          } appearance-none border rounded h-10 w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:-outline ${className}`}
          placeholder={placeholder}
          readOnly={readonly}
          disabled={disabled}
          onFocus={onFocus}
          id={id}
        />
        {/* icon */}
        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 cursor-pointer" onClick={handleIconClick}>
        <Icon icon="simple-line-icons:calender" />
        </div>
      </div>
      {/* error and success message */}
      {error && (
        <div
          className={`mt-2 ${
            msgTooltip
              ? "inline-block bg-danger-500 text-white text-[10px] px-2 py-1 rounded"
              : "text-danger-500 block text-sm"
          }`}
        >
          {error}
          {/* {error.message} */}
        </div>
      )}
      {validate && (
        <div
          className={`mt-2 ${
            msgTooltip
              ? "inline-block bg-success-500 text-white text-[10px] px-2 py-1 rounded"
              : "text-success-500 block text-sm"
          }`}
        >
          {validate}
        </div>
      )}
      {description && <span className="input-description">{description}</span>}
    </div>
  );
};

export default DatePicker;

