import { Outlet, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';

const InvoiceOutlet = ({ allowedRoles = [] }) => {
  const navigate = useNavigate();

  useEffect(() => {
    const isAuthenticated = () => {
      return localStorage.getItem("crm_token") !== null;
    };

    const userString = localStorage.getItem('user');
    const user = userString ? JSON.parse(userString) : null;
    const role = user?.role;

    const hasAccess = allowedRoles.includes(role);

    if (!isAuthenticated() || !hasAccess) {
      navigate('/404', { replace: true }); 
    }
  }, [navigate, allowedRoles]);

  return <Outlet />;
};

export default InvoiceOutlet;
