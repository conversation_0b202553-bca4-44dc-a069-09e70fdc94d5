import React from 'react';
import { Navigate } from 'react-router-dom';

const ProtectedRoutes = ({ element: Component, allowedRoles = [], ...rest }) => {

  const userString = localStorage.getItem('user');
  const user = userString ? JSON.parse(userString) : null;
  const role = user?.role;

  const hasAccess = allowedRoles.includes(role);

  if (!hasAccess) {
    return <Navigate to="/404" />;
  }

  return <Component {...rest} />;
};

export default ProtectedRoutes;
