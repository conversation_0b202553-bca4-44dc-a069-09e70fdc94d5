import { Outlet, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';

const PublicOutlet = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const isAuthenticated = () => {
      return localStorage.getItem("crm_token") !== null;
    };
    const localAuth = isAuthenticated();
    const isInvoiceRoute = window.location.pathname.includes("invoice");
    if (localAuth && !isInvoiceRoute) { navigate('/dashboard', { replace: true }) }
  }, [navigate])

  return <Outlet />
};

export default PublicOutlet;