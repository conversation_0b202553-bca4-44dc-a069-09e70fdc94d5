import { Outlet, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import { useSelector } from "react-redux";

const PrivateOutlet = () => {
  const navigate = useNavigate();
  
  const { user } = useSelector((state) => state.auth);
  // 
  useEffect(() => {
    const isAuthenticated = () => {
       return localStorage.getItem("crm_token") != null;
   };
    const localAuth = isAuthenticated();
    if (!localAuth) {navigate('/', {replace: true})}
  }, [navigate])

  return <Outlet />
};

export default PrivateOutlet;