import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { toast } from "react-toastify";
// import { logout } from "../../features/authSlice";
const baseQuery = fetchBaseQuery({
  baseUrl: import.meta.env.VITE_HOST_URL,
  // credentials: "include",
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth?.user?.token;
    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }
    return headers;
  },
});

const baseQueryWithReauth = async (args, api, extraOptions) => {
  let result = await baseQuery(args, api, extraOptions);
  if (result?.error?.status === 401) {
    // toast.error("Session Expired. Please login again");
    // setTimeout(() => {
    //   window.location.reload(false);
    // }, 3000);

  }
  if (result?.error) {
    toast.warning(result.error.data.message);
    return result;
  } else {
    if (api.type == 'mutation') {
      toast.success(result.message);
    }
    return result.data;
  }
};

export const apiSlice = createApi({
  baseQuery: baseQueryWithReauth,
  tagTypes: ["Admin"],
  reducerPath: "apiSliceAdmin",

  endpoints: (builder) => ({

  }),
});
