import { apiSlice } from "../apiSlice";
export const categoryApi = apiSlice.injectEndpoints({
    reducerPath: "categoryApi",
    endpoints: (builder) => ({
        getAllCategories: builder.query({
            query: (params) => ({
                url: "categories",
                method: "GET",
                params: params
            }),
            providesTags: ["categories"],
        }),
    }),
});

export const { useGetAllCategoriesQuery } = categoryApi;