import { createSlice } from "@reduxjs/toolkit";

const initialState = {
    productSerials: {},
    submissionStatus: {}, 
};

const serialSlice = createSlice({
    name: "serials",
    initialState,
    reducers: {
        saveSerials(state, action) {
            const { productId, serials, qty } = action.payload;
            let adjustedSerials = [];

            if (serials.length > qty) {
                adjustedSerials = serials.slice(0, qty);
            } else if (serials.length < qty) {
                adjustedSerials = [...serials, ...Array(qty - serials.length).fill("")];
            } else {
                adjustedSerials = [...serials];
            }
            state.productSerials[productId] = adjustedSerials;
        },
        clearSerials(state, action) {
            const { productId } = action.payload;
            delete state.productSerials[productId];
            delete state.submissionStatus[productId]; 
        },
        clearAllSerials(state) {
            state.productSerials = {};
            state.submissionStatus = {};
        },
        setSerialFormSubmitted(state, action) {
            const { productId, isSubmitted } = action.payload;
            state.submissionStatus[productId] = isSubmitted; 
        }
    }
});

export const { saveSerials, clearSerials, setSerialFormSubmitted,clearAllSerials } = serialSlice.actions;
export default serialSlice.reducer;
