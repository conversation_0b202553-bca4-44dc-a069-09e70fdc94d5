import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import Badge from "@/components/ui/Badge";
import { useNavigate } from "react-router-dom";
import { formattedDate } from "@/constant/data";

const MenuList = () => {
    const [apiParam, setApiParam] = useState(0);
    const [filter, setFilter] = useState("");
    const { data, isLoading, isFetching } = useGetApiQuery({ url: "warranty", params: apiParam });
    const changePage = (value) => {
        setApiParam(value);
    };

    const columns = [
        { label: "Sl", field: "sl" },
        { label: "Customer Name & Phone", field: "client_name" },
        { label: "Invoice Number", field: "invoice_no" },
        { label: "Product & Serials", field: "product_serials" },
        { label: "Quantity", field: "qty" },
        { label: "Warranty", field: "warranty" },
        { label: "Warranty Status", field: "warranty_status" },
    ];

    const tableData = data?.data?.map((item, index) => {
        return {
            sl: index + 1,
            client_name: <div>
                <p>{item?.client_name}</p>
                <p>{item?.client_phone}</p>
            </div>,
            invoice_no: item.invoice_no,
            product_serials: (<div>
                <strong>{item.product_name}</strong>
                <div className="mt-1 text-sm text-gray-600 space-y-1">
                    {
                        item?.serial_numbers?.split(',')
                            .map((serial, index) => (
                                <div key={index}>{serial.trim()}</div>
                            ))
                    }
                </div>
            </div>),
            qty: <Badge className="bg-green-500 px-2 py-[3px] text-sm font-semibold text-slate-100  capitalize">
                {item.qty} pcs
            </Badge>,
            warranty: <div>
                <p>{item?.warranty_period} {item?.warranty_type}</p>
                <p>Expires at : {formattedDate(item?.warranty_expiry_date)}</p>
            </div>,
            warranty_status: <Badge className={item?.warranty_status == "Valid" ? "bg-green-500 text-white" : "bg-red-500 text-white"}>
                {item?.warranty_status}
            </Badge>
        };
    });

    return (
        <div>
            <BasicTablePage
                title={'Warranty List'}
                loading={isLoading || isFetching}
                columns={columns}
                changePage={changePage}
                filter={filter}
                setFilter={setApiParam}
                data={tableData}
                currentPage={data?.current_page}
                totalPages={Math.ceil(data?.total / data?.per_page)}
            />

        </div>
    );
};

export default MenuList;
