import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";

const index = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery({
    url: "product-serials",
    params: apiParam,
  });
  const navigate = useNavigate();

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Serial Number", field: "serial_number" },
    { label: "Name", field: "name" },
    { label: "Sold", field: "sold" },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      name: item.product?.name,
      serial_number: item.serial_number,
      sold: (
        <Badge
          className={
            item.sold === 0
              ? `bg-success-200 text-success-600 px-2 shadow-md`
              : `bg-danger-200 text-danger-600 px-2 shadow-md`
          }
        >
          {item.sold === 0 ? "Available" : "SoldOut"}
        </Badge>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        title="Product Serial List"
        loading={isLoading || isFetching}
        columns={columns}
        changePage={changePage}
        filter={filter}
        setFilter={setApiParam}
        data={tableData}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
    </div>
  );
};

export default index;
