import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputSelect from "@/components/ui/form/InputSelect";
import TextAreaField from "@/components/ui/form/TextAreaField";
import {
  createProductSerialValidationSchema,
  isProductSerialActiveOptions,
  isSoldOptions,
  productSerialInitialValues,
} from "@/formHandlers/ProductSerialCrud";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import React from "react";
import { useNavigate } from "react-router-dom";
import Button from "@/components/ui/Button";

const CreateProductSerial = () => {
  const navigate = useNavigate();
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();
  const headerSlotContent = (
    <Button
      onClick={() => navigate("/product-serials-list")}
      className="btn btn-outline-primary"
    >
      Product Serials List
    </Button>
  );

  const handleSubmit = async (values, { resetForm }) => {
    const modifiedValues = { ...values };
    // console.log(values);

    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        }
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const response = await postApi({
        end_point: "product-serials",
        body: formData,
      }).unwrap();
      // console.log("Submission successful:", response);
      navigate("/product-serials-list");
      resetForm();
    } catch (err) {
      // console.error("Submission failed:", err);
    }
  };

  return (
    <div>
      <Formik
        initialValues={productSerialInitialValues}
        validationSchema={createProductSerialValidationSchema()}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Create Product Serial"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-2 gap-4">
                <InputField
                  label="Product Id"
                  name="product_id"
                  type="number"
                  required
                  placeholder="Enter Product Id"
                />
                <InputField
                  label="Serial Number"
                  name="serial_number"
                  type="text"
                  required
                  placeholder="Enter Serial Number"
                />
                <InputSelect
                  label="Is Active"
                  name="is_active"
                  options={isProductSerialActiveOptions}
                  placeholder="Select status"
                  required
                />
                <InputSelect
                  label="Sold"
                  name="sold"
                  options={isSoldOptions}
                  placeholder="Is Sold"
                  required
                />
              </div>
              <div className="mt-3">
                <TextAreaField
                  label="Description"
                  name="description"
                  type="text"
                  required
                  placeholder="Enter description"
                />
              </div>

              <div className="flex justify-end my-5 gap-3">
                <Button
                  type="submit"
                  className="btn text-center btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateProductSerial;
