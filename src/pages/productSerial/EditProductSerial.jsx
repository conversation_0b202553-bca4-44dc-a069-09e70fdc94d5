import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputSelect from "@/components/ui/form/InputSelect";
import TextAreaField from "@/components/ui/form/TextAreaField";
import {
  createProductSerialValidationSchema,
  isProductSerialActiveOptions,
  isSoldOptions,
} from "@/formHandlers/ProductSerialCrud";
import Button from "@/components/ui/Button";
import { useUpdateApiMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import React from "react";
import Modal from "@/components/ui/Modal";
import { toast } from "react-toastify";

const EditProductSerial = ({ showEditModal, setShowEditModal, data }) => {
  const [updateApi] = useUpdateApiMutation();

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();
    Object.keys(values).forEach((key) => {
      formData.append(key, values[key]);
    });

    try {
      await updateApi({
        end_point: "product-serials/" + data?.id,
        body: formData,
      }).unwrap();
      resetForm();
      setShowEditModal(false);
      toast.success("Product Serial Updated Successfully!");
    } catch (err) {
      console.error("Submission failed:", err);
    }
  };

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => setShowEditModal(false)}
      title="Edit Product Serial"
      className="max-w-2xl"
    >
      <Formik
        initialValues={{
          // product_id: data?.product_id || "",
          serial_number: data?.serial_number || "",
          description: data?.description || "",
          is_active: data?.is_active || 1,
          // sold: data?.sold || 0,
        }}
        validationSchema={createProductSerialValidationSchema()}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting }) => (
          <Form>
            <div className="grid grid-cols-1 gap-4">
              {/* <InputField
                label="Product Id"
                name="product_id"
                type="number"
                required
                placeholder="Enter Product Id"
                readOnly
              /> */}
              <InputField
                label="Serial Number"
                name="serial_number"
                type="text"
                required
                placeholder="Enter Serial Number"
              />
              {/* <InputSelect
                label="Is Active"
                name="is_active"
                options={isProductSerialActiveOptions}
                placeholder="Select status"
                required
                isDisabled={true}
              /> */}
              {/* <InputSelect
                label="Sold"
                name="sold"
                options={isSoldOptions}
                placeholder="Is Sold"
                required
                isDisabled={true}
              /> */}
            </div>
            <div className="mt-3">
              <TextAreaField
                label="Description"
                name="description"
                type="text"
                // required
                placeholder="Enter description"
              />
            </div>
            <Button
              type="submit"
              className="btn text-center btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditProductSerial;
