import BasicTablePage from "@/components/partials/common-table/table-basic";
import React, { useState } from "react";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import { useNavigate, useParams } from "react-router-dom";
import Badge from "@/components/ui/Badge";
import DeleteProductSerial from "./DeleteProductSerial";
import EditProductSerial from "./EditProductSerial";

const ProductSerialList = () => {
  const { id } = useParams();
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const allProductSerials = useGetApiQuery({
    url: `product-serials?product_id=${id}`,
    params: apiParam,
  });
  const navigate = useNavigate();

  //Edit modal state
  const [showEditModal, setShowEditModal] = useState(false);
  const [editData, setEditData] = useState(null);

  //Delete Modal state
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    // { label: "Product Name", field: "product_name" },
    { label: "Serial Number", field: "serial_number" },
    { label: "Sold", field: "sold" },
    // { label: "Status", field: "is_active" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        setEditData(allProductSerials?.data.data[val]);
        setShowEditModal(true);
      },
      //   onClick: (val) => {
      //     navigate(
      //       `/edit-product-serials/${allProductSerials?.data?.data[val]?.id}`
      //     );
      //   },
    },
    // {
    //   name: "Delete",
    //   icon: "heroicons-outline:trash",
    //   onClick: (val) => {
    //     setDeleteData(allProductSerials?.data.data[val]);
    //     setShowDeleteModal(true);
    //   },
    // },
  ];

  const tableData = allProductSerials?.data?.data?.map((item, index) => {
    return {
      // product_name: item.product.name,
      serial_number: item.serial_number,
      sold: (
        <Badge
          className={
            item.sold === 0
              ? `bg-success-200 text-success-600 px-2 shadow-md`
              : `bg-danger-200 text-danger-600 px-2 shadow-md`
          }
        >
          {item.sold === 0 ? "Available" : "SoldOut"}
        </Badge>
      ),
      // is_active: (
      //   <Badge
      //     className={
      //       item.is_active
      //         ? `bg-success-500 text-white`
      //         : `bg-danger-500 text-white`
      //     }
      //   >
      //     {" "}
      //     {item.is_active ? "Active" : "Inactive"}
      //   </Badge>
      // ),
    };
  });

  // Extract product name for the title
  const productName =
    allProductSerials?.data?.data?.length > 0
      ? allProductSerials.data.data[0]?.product?.name
      : "";

  return (
    <div>
      <BasicTablePage
        // title="Product Serial List"
        title={
          <>
            {/* Product{" "} */}

            {productName && (
              <span className="bg-primary-200 text-primary-500 p-2 rounded-lg">
                {" "}
                {productName}
              </span>
            )}
          </>
        }
        columns={columns}
        actions={actions}
        changePage={changePage}
        data={tableData}
        filter={filter}
        setFilter={setApiParam}
        currentPage={allProductSerials?.data?.current_page}
        totalPages={Math.ceil(
          allProductSerials?.data?.total / allProductSerials?.data?.per_page
        )}
      />

      {/* Edit modal */}
      <EditProductSerial
        showEditModal={showEditModal}
        setShowEditModal={setShowEditModal}
        data={editData}
      />

      {/* Delete Modal */}
      <DeleteProductSerial
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </div>
  );
};

export default ProductSerialList;
