import React from "react";
import Modal from "@/components/ui/Modal";

const PropertiesModal = ({ isOpen, onClose, data }) => {
  return (
    <Modal activeModal={isOpen} onClose={onClose} title="Detailed Properties">
      <div className="text-left whitespace-pre-wrap">
        <div>
          <strong>Old Data:</strong>
          {data?.old ? (
            Object.entries(data.old).map(([key, value]) => (
              <div key={key}>
                <strong>{key}:</strong>{" "}
                {value !== null ? value.toString() : "N/A"}
              </div>
            ))
          ) : (
            <p>No Old Data Available</p>
          )}
        </div>
        <div className="mt-2">
          <strong>Attributes:</strong>
          {data?.attributes ? (
            Object.entries(data.attributes).map(([key, value]) => (
              <div key={key}>
                <strong>{key}:</strong>{" "}
                {value !== null ? value.toString() : "N/A"}
              </div>
            ))
          ) : (
            <p>No Attributes Available</p>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default PropertiesModal;
