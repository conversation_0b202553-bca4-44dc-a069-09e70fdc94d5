import BasicTablePage from "@/components/partials/common-table/table-basic";
import Tooltip from "@/components/ui/Tooltip";
import PropertiesModal from "./propertiesModal";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

const Index = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const [selectedProperties, setSelectedProperties] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { data, isLoading, isFetching } = useGetApiQuery({
    url: "activity-log",
    params: apiParam,
  });

  const navigate = useNavigate();

  const changePage = (value) => {
    setApiParam(value);
  };

  const openModal = (properties) => {
    setSelectedProperties(properties);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedProperties(null);
  };

  const columns = [
    { label: "User Name", field: "causer" },
    { label: "Log Name", field: "log_name" },
    { label: "Description", field: "description" },
    { label: "Event", field: "event" },
    { label: "Attributes", field: "properties" },
    { label: "Time", field: "created_at" },
  ];

  const tableData = data?.data?.map((item) => {
    return {
      id: item.id,
      causer: item?.causer?.name,
      log_name: item.log_name,
      description: (
        <Tooltip
          content={
            <>
              Details of{" "}
              <span className="font-bold">
                {item.description.length > 15
                  ? `${item.description.slice(0, 15)}...`
                  : item.description}
              </span>
            </>
          }
          placement="top"
          arrow
          animation="Interactive"
        >
          <button
            // onClick={() =>
            //   navigate(`/details-support/${item.id}`, {
            //     state: { support: item },
            //   })
            // }
            className="hover:underline hover:text-primary-500"
          >
            {item.description.length > 50
              ? `${item.description.slice(0, 50)}...`
              : item.description}
          </button>
        </Tooltip>
      ),
      event: (
        <span className="bg-primary-400 text-slate-50 font-medium p-1 rounded-md uppercase">
          {item.event}
        </span>
      ),
      properties: (
        <button
          onClick={() => openModal(item.properties)}
          className="text-primary-500 underline font-semibold"
        >
          View Details
        </button>
      ),
      created_at: new Date(item.created_at).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      }),
    };
  });

  return (
    <div>
      <BasicTablePage
        title="Activity Logs"
        loading={isLoading || isFetching}
        columns={columns}
        changePage={changePage}
        filter={filter}
        setFilter={setApiParam}
        data={tableData}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />

      {/* Modal for showing properties */}
      <PropertiesModal
        isOpen={isModalOpen}
        onClose={closeModal}
        data={selectedProperties}
      />
    </div>
  );
};

export default Index;
