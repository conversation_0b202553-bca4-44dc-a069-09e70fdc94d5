import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import {
  createValidationSchema,
  isActiveOptions,
} from "@/formHandlers/CategoryCrud";
import {
  useGetApiQuery,
  useGetApiWithIdQuery,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";

const EditCategory = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const { data: category } = useGetApiWithIdQuery(["categories", id]);
  const [updateApi] = useUpdateApiMutation();

  const headerSlotContent = (
    <Button
      onClick={() => navigate("/category-list")}
      className="btn text-center btn-outline-primary"
    >
      Category List
    </Button>
  );

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        } else if (Array.isArray(values.image)) {
          // If multiple files are uploaded
          values.image.forEach((file) => formData.append(key, file));
        }
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const data = {
        end_point: "categories/" + id,
        body: formData,
      };

      const response = await updateApi(data).unwrap();
      toast.success("Category Updated Successfully!");
      resetForm();
      navigate("/category-list");
    } catch (err) {
      // console.error("Submission failed:", err);
      toast.error("Category Updated failed. Please try again.");
    }
  };
  // console.log(category);

  return (
    <div>
      <Formik
        initialValues={{
          name: category?.name || "",
          image: category?.image || "",
          // parent_id: category?.parent_id || "",
          description: category?.description || "",
          is_active: category?.is_active === true ? 1 : 0,
        }}
        validationSchema={createValidationSchema()}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Edit Category"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid md:grid-cols-2 grid-cols-1 gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter name"
                />
                <InputFile
                  label="image"
                  name="image"
                  type="file"
                  title="Upload your file"
                  accept="image/*"
                />
              </div>
              <div className="mt-4">
                <TextAreaField
                  label="Description"
                  name="description"
                  type="text"
                  placeholder="Enter description"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5 my-5">
                <InputSelect
                  label="Is Active"
                  name="is_active"
                  options={isActiveOptions}
                  placeholder="Select status"
                  required
                />
              </div>

              <div className="w-full text-end">
                <Button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditCategory;
