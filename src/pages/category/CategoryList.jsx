import BasicTablePage from "@/components/partials/common-table/table-basic";
import React, { useState } from "react";
import DeleteCategory from "./DeleteCategory";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import { useNavigate } from "react-router-dom";
import Badge from "@/components/ui/Badge";
import NoImage from "@/assets/CRM/NoImage.png";

const CategoryList = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery({ url: "categories", params: apiParam });

  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Name", field: "name" },
    { label: "Image", field: "image" },
    { label: "Status", field: "is_active" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/edit-category/${data?.data[val]?.id}`);
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      image: (
        <>
          <img
            className="w-10 h-10 rounded"
            src={
              item.image
                ? `${import.meta.env.VITE_MEDIA_URL}/${item.image}`
                : NoImage
            }
          />
        </>
      ),
      name: item.name,
      company_name: item.company_name,
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {" "}
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        title="Category List"
        loading={isLoading || isFetching}
        columns={columns}
        actions={actions}
        goto={"Create New Category"}
        gotoLink={"/create-category"}
        changePage={changePage}
        data={tableData}
        filter={filter}
        setFilter={setApiParam}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
      <DeleteCategory
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </div>
  );
};

export default CategoryList;
