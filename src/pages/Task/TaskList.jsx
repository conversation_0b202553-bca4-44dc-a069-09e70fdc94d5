import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

const TaskList = () => {
    const [apiParam, setApiParam] = useState(0);
    const [filter, setFilter] = useState("");
    const allSales = useGetApiQuery({ url: "supports", params: apiParam });
    const navigate = useNavigate();
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deleteData, setDeleteData] = useState(null);

    const changePage = (value) => {
        setApiParam(value);
    };

    const columns = [
        { label: "Sl", field: "index" },
        { label: "Client Name", field: "client_name" },
        { label: "Invoice Number", field: "invoice_no" },
        { label: "Sub Total", field: "sub_total" },
        { label: "Grand Total", field: "grand_total" },
        { label: "Paid Amount", field: "paid_amount" },
        { label: "Payment Status", field: "payment_status" },
        { label: "Due Amount", field: "due_amount" },
        { label: "Action", field: "" },
    ];

    const actions = [
        {
            name: "View",
            icon: "heroicons-outline:eye",
            onClick: (val) => {
                // setDeleteData(allSales?.data.data[val]);
                // setShowDeleteModal(true);
            },
        },
    ];

    const tableData = allSales?.data?.data?.map((item, index) => {
        return {
            index: index + 1,
            client_name: item.client_name,
            vendor_id: item.vendor?.name,
            invoice_no: item.invoice_no,
            sub_total: item.sub_total,
            grand_total: item.grand_total,
            paid_amount: item.paid_amount,
            due_amount: item.due_amount,
            payment_status: (
                <Badge
                    className={
                        item.payment_status === "Paid"
                            ? `bg-success-500 text-white`
                            : `bg-danger-500 text-white`
                    }
                >
                    {" "}
                    {item.payment_status === "Paid" ? "Paid" : "Due"}
                </Badge>
            ),
        };
    });

    return (
        <div>
            <BasicTablePage
                title="Task List"
                columns={columns}
                actions={actions}
                goto={"Create New Sale"}
                gotoLink={"/store-sale"}
                changePage={changePage}
                data={tableData}
                filter={filter}
                setFilter={setApiParam}
                currentPage={allSales?.data?.current_page}
                totalPages={Math.ceil(
                    allSales?.data?.total / allSales?.data?.per_page
                )}
            />
        </div>
    );
};

export default TaskList;
