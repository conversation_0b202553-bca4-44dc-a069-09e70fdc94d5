import BasicTablePage from '@/components/partials/common-table/table-basic';
import React, { useState } from 'react';
import { useGetApiQuery } from '@/store/api/apihandler/commonSlice';
import { useNavigate } from 'react-router-dom';
import Badge from '@/components/ui/Badge';
import DeleteProductTag from './DeleteProductTag';

const ProductTagList = () => {
    const [apiParam, setApiParam] = useState(0);
    const [filter,setFilter] = useState('');
    const allProductTag = useGetApiQuery({url:"product-tags",params:apiParam});
    const navigate = useNavigate();
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deleteData, setDeleteData] = useState(null);

    const changePage = (value) => {
        setApiParam(value);
    }

    const columns = [
        { label: 'Name', field: 'name'},
        { label: 'Description', field: 'description'},
        { label: 'Status', field: 'is_active'},
        { label: 'Action', field: ''},
    ];

    const actions = [
        {
            name: "edit",
            icon: "heroicons:pencil-square",
            onClick: (val) => {
                navigate(`/edit-product-tag/${allProductTag?.data?.data[val]?.id}`);
            },
        },
        {
            name: "delete",
            icon: "heroicons-outline:trash",
            onClick: (val) => {
                setDeleteData(allProductTag?.data.data[val]);
                setShowDeleteModal(true);
            },
        },
    ];

    const tableData = allProductTag?.data?.data?.map((item, index) => {
        return {
            id: item.id,
            name: item.name,
            description: item.description,
            is_active: (
                <Badge
                    className={
                        item.is_active
                            ? `bg-success-500 text-white`
                            : `bg-danger-500 text-white`
                    }
                >
                    {" "}
                    {item.is_active ? "Active" : "Inactive"}
                </Badge>
            ),

        }
    });



    return (
        <div>
            <BasicTablePage
                title="Product Tag List"
                columns={columns}
                actions={actions}
                goto={'Create New Product Tag'}
                gotoLink={'/create-product-tags'}
                changePage={changePage}
                data={tableData}
                filter={filter} setFilter={setApiParam}
                currentPage={allProductTag?.data?.current_page}
                totalPages={Math.ceil(allProductTag?.data?.total / allProductTag?.data?.per_page)}
            />
            <DeleteProductTag
                showDeleteModal={showDeleteModal}
                setShowDeleteModal={setShowDeleteModal}
                data={deleteData}
            />
        </div>
    );
};

export default ProductTagList;