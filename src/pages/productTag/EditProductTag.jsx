import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputSelect from "@/components/ui/form/InputSelect";
import TextAreaField from "@/components/ui/form/TextAreaField";
import { productTagActiveOptions, productTagValidationSchema } from "@/formHandlers/ProductTagCrud";
import { useGetApiWithIdQuery, useUpdateApiJsonMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import React from "react";
import { useNavigate, useParams } from "react-router-dom";

const EditProductTag = () => {
    const { id } = useParams();
  const navigate = useNavigate();
  const { data: productTag } = useGetApiWithIdQuery(["product-tags", id]);
  const [updateApiJson] = useUpdateApiJsonMutation();

  const headerSlotContent = (
    <button
      onClick={() => navigate("/product-tag-list")}
      className="bg-blue-500 text-white p-2 rounded"
    >
      Product Tag List
    </button>
  );

  const handleSubmit = async (values, { resetForm }) => {
    // console.log(values)

    try {
      const data = {
        end_point: "product-tags/" + id,
        body: values,
      };

      const response = await updateApiJson(data).unwrap();
      resetForm();
      navigate("/product-tag-list");
    } catch (err) {
      console.error("Submission failed:", err);
    }
  };
//   console.log(productTag);


  return (
    <div>
      <Formik
        initialValues={{
            name: productTag?.name || "",
            description: productTag?.description || "",
            is_active: productTag?.is_active === true ? 1 : 0,
          }}
        validationSchema={productTagValidationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Edit Product Tag"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-2 gap-4">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter name"
                />
                <InputSelect
                  label="Is Active"
                  name="is_active"
                  options={productTagActiveOptions}
                  placeholder="Select status"
                  required
                />
              </div>
              <div className="mt-3">
                <TextAreaField
                  label="Description"
                  name="description"
                  type="text"
                  required
                  placeholder="Enter description"
                />
              </div>

              <button
                type="submit"
                className="bg-blue-500 text-white font-bold py-2 px-4 mt-4 rounded"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Submit"}
              </button>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditProductTag;
