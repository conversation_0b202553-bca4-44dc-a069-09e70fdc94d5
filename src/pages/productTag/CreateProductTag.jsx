import Card from '@/components/ui/Card';
import InputField from '@/components/ui/form/InputField';
import InputSelect from '@/components/ui/form/InputSelect';
import TextAreaField from '@/components/ui/form/TextAreaField';
import { productTagActiveOptions, productTagInitialValues, productTagValidationSchema } from '@/formHandlers/ProductTagCrud';
import { usePostApiMutation } from '@/store/api/apihandler/commonSlice';
import { Form, Formik, FieldArray } from 'formik';
import React from 'react';
import { useNavigate } from 'react-router-dom';

const CreateProductTag = () => {
    const navigate = useNavigate();
    const [postApi] = usePostApiMutation();

    const handleSubmit = async (values, { resetForm }) => {
        const modifiedValues = { ...values };
        try {
            const response = await postApi({
                end_point: 'product-tags',
                body: values
            }).unwrap();
            resetForm();
            navigate('/product-tag-list');
        } catch (err) {
            console.error('Submission failed:', err);
        }
    };


    const headerSlotContent = (
        <button onClick={() => navigate('/product-tag-list')} className="bg-blue-500 text-white p-2 rounded">
            Tag List
        </button>
    );

    return (
        <div>
            <Formik
                initialValues={productTagInitialValues}
                validationSchema={productTagValidationSchema}
                onSubmit={handleSubmit}
            >
                {({ values, isSubmitting }) => (
                    <Form>
                        <Card
                            headerslot={headerSlotContent}
                            title="Create Product Tags"
                            className="w-full"
                            titleClass="text-lg font-bold text-gray-800"
                        >
                            <FieldArray name="tags">
                                {({ push, remove }) => (
                                    <>
                                        {values.tags.map((tag, index) => (
                                            <div key={index} className="grid grid-cols-2 gap-4 mb-4">
                                                {/* Name Field - Half Width */}
                                                <div className="col-span-1">
                                                    <InputField
                                                        label="Name"
                                                        name={`tags[${index}].name`}
                                                        type="text"
                                                        required
                                                        placeholder="Enter tag name"
                                                    />
                                                </div>

                                                {/* Is Active Field - Half Width */}
                                                <div className="col-span-1">
                                                    <InputSelect
                                                        label="Is Active"
                                                        name={`tags[${index}].is_active`}
                                                        options={productTagActiveOptions}
                                                        placeholder="Select status"
                                                        required
                                                    />
                                                </div>

                                                {/* Description Field - Full Width */}
                                                <div className="col-span-2">
                                                    <TextAreaField
                                                        label="Description"
                                                        name={`tags[${index}].description`}
                                                        type="text"
                                                        required
                                                        placeholder="Enter description"
                                                        rows={4}
                                                    />
                                                </div>

                                                <div className="col-span-2 flex justify-end">
                                                    {values.tags.length > 1 && (
                                                        <button
                                                            type="button"
                                                            className="bg-red-500 text-white p-2 rounded"
                                                            onClick={() => remove(index)}
                                                        >
                                                            Remove Tag
                                                        </button>
                                                    )}
                                                </div>
                                            </div>
                                        ))}

                                        {/* Add Tag Button */}
                                        <button
                                            type="button"
                                            className="bg-green-500 text-white font-bold py-2 px-4 mt-4 rounded mr-2"
                                            onClick={() => push({ name: '', description: '', is_active: true })}
                                        >
                                            Add Tag
                                        </button>
                                    </>
                                )}
                            </FieldArray>

                            {/* Submit Button */}
                            <button
                                type="submit"
                                className="bg-blue-500 text-white font-bold py-2 px-4 mt-4 rounded"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? "Submitting..." : "Submit"}
                            </button>
                        </Card>
                    </Form>
                )}
            </Formik>
        </div>
    );
};

export default CreateProductTag;
