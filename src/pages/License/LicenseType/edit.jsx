import React from "react";
import Card from "@/components/ui/Card";
import {
  useGetApiWithIdQuery,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";
import { validationSchema } from "./formSetting";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";

const edit = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Fetch the License type data using the ID
  const { data: licenseType, isLoading } = useGetApiWithIdQuery([
    "license-types",
    id,
  ]);

  const [updateApi] = useUpdateApiMutation();

  // Initial values populated dynamically with the fetched data
  const initialValues = {
    name: licenseType?.name || "",
    icon: licenseType?.icon || "",
    description: licenseType?.description || "",
    is_active: licenseType?.is_active ? true : false,
  };

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        }
      } else if (key === "is_active") {
        // Convert boolean to integer (1 or 0)
        formData.append(key, values.is_active ? 1 : 0);
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const data = {
        end_point: "license-types/" + id,
        body: formData,
      };

      const response = await updateApi(data).unwrap();

      toast.success("License Type updated successfully!");
      navigate("/license-type-list");
      resetForm();
    } catch (err) {
      toast.error("Failed to update License Type. Please try again.");
    }
  };

  // Handle loading and form display
  if (isLoading) {
    return <p>Loading...</p>;
  }

  return (
    <div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values, setFieldValue }) => (
          <Form>
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-bold text-lg">
                        Edit{" "}
                        <span className="text-primary-500">{values.name}</span>
                      </span>
                    </div>
                    <div>
                      <Tooltip
                        content="Back to License Type List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() => navigate("/license-type-list")}
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </Card>
              </div>
            </div>

            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-8 col-span-12">
                <div className="grid gap-4">
                  <Card>
                    <>
                      <span className="font-bold text-slate-600 my-4">
                        Name:
                      </span>
                      <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                        <span className="flex items-center text-slate-700">
                          {values.name || "Untitled Name"}
                        </span>
                      </div>
                    </>
                  </Card>

                  <Card>
                    <>
                      <span className="font-bold text-slate-600 my-4">
                        Description:
                      </span>
                      <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                        <span className="flex items-center text-slate-700">
                          {values.description ||
                            "Untitled License Type Description"}
                        </span>
                      </div>
                    </>
                  </Card>
                  <Card>
                    <div className="flex justify-center items-center bg-slate-100 rounded-lg px-3 py-2">
                      <span className="mr-1 font-bold text-slate-600">
                        Icon:
                      </span>
                      <Icon icon={values.icon} width={25} />
                    </div>
                  </Card>
                  <Card>
                    <div className="flex justify-center items-center bg-slate-100 rounded-lg px-3 py-2">
                      <span className="mr-1 font-bold text-slate-600">
                        Status:
                      </span>
                      <Badge
                        className={
                          values.is_active
                            ? `text-success-500 font-bold text-base`
                            : `text-danger-500 font-bold text-base`
                        }
                      >
                        {values.is_active ? (
                          <>
                            Active
                            <Icon
                              icon="icon-park-solid:correct"
                              className="ml-2"
                            />
                          </>
                        ) : (
                          <>
                            <Icon icon="maki:cross" className="mr-2" />
                            Inactive
                          </>
                        )}
                      </Badge>
                    </div>
                  </Card>
                </div>
              </div>

              <div className="xl:col-span-4 col-span-12 space-y-4">
                <Card>
                  <div className="grid grid-cols-1 my-3 gap-3">
                    <InputField
                      label="Name"
                      name="name"
                      type="text"
                      placeholder="Enter Name"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-1 my-3">
                    <InputField
                      label="Icon"
                      name="icon"
                      type="text"
                      placeholder="Enter Icon"
                    />
                  </div>
                  <div className="grid grid-cols-1 my-3">
                    <TextAreaField
                      label="Description"
                      name="description"
                      type="text"
                      placeholder="Enter Description"
                    />
                  </div>

                  <div className="my-3">
                    <Switch
                      label="Is Active"
                      activeClass="bg-success-500"
                      name="is_active"
                      value={values.is_active}
                      onChange={() =>
                        setFieldValue("is_active", !values.is_active)
                      }
                    />
                  </div>
                </Card>
              </div>
            </div>

            {/* Submit and Cancel Buttons */}
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-end items-center gap-4">
                    <Button
                      type="button"
                      className="btn text-center btn-danger"
                      onClick={() => navigate("/license-type-list")}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="btn text-center btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Updating..." : "Update"}
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default edit;
