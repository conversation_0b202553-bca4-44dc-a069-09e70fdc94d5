import React from "react";
import { useLocation } from "react-router-dom";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import { useNavigate } from "react-router-dom";
import Tooltip from "@/components/ui/Tooltip";

const details = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { licenseType } = location.state || {};

  console.log(licenseType);

  return (
    <>
      <div className="grid gap-4 grid-cols-12 mt-5">
        <div className="xl:col-span-12 col-span-12 space-y-4">
          <Card>
            <div className="xl:col-span-12 col-span-12 space-y-4">
              <Card>
                <div className="flex justify-between items-center">
                  <div>
                    <span className="font-bold text-lg">
                      License Type Details of{" "}
                      <span className="text-primary-500">
                        {licenseType.name}
                      </span>
                    </span>
                  </div>
                  <div className="flex gap-3">
                    <div>
                      <Tooltip
                        content="Back to License Type List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() => navigate("/license-type-list")}
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                    <div>
                      <Tooltip
                        content={`Edit ${licenseType.name} License Type`}
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() =>
                            navigate(`/edit-license-type/${licenseType.id}`)
                          }
                        >
                          <Icon
                            icon="mynaui:edit-one"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </Card>
        </div>
      </div>
      <div className="grid gap-4 grid-cols-1 md:grid-cols-3 mt-5">
        <Card className="">
          <>
            <span className="font-bold text-slate-600 my-4">Name:</span>
            <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
              <span className="flex items-center text-slate-700">
                {licenseType.name}
              </span>
            </div>
          </>
        </Card>
        <Card>
          <>
            <span className="font-bold text-slate-600 my-4">Icon:</span>
            <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
              <span className="flex items-center justify-center text-slate-700">
                {<Icon icon={licenseType.icon} width={25} />}
              </span>
            </div>
          </>
        </Card>

        <Card>
          <div className="flex justify-center items-center bg-slate-100 rounded-lg px-3 py-2 mt-8">
            <span className="mr-1 font-bold text-slate-600">Status:</span>
            <Badge
              className={
                licenseType.is_active
                  ? `text-success-500 font-bold text-base`
                  : `text-danger-500 font-bold text-base`
              }
            >
              {licenseType.is_active ? (
                <>
                  Active
                  <Icon icon="icon-park-solid:correct" className="ml-2" />
                </>
              ) : (
                <>
                  <Icon icon="maki:cross" className="mr-2" />
                  Inactive
                </>
              )}
            </Badge>
          </div>
        </Card>
      </div>
      <div className="grid gap-4 grid-cols-1 mt-5">
        <Card>
          <>
            <span className="font-bold text-slate-600 my-4">Description:</span>
            <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
              <span className="flex items-center text-slate-700">
                {licenseType.description}
              </span>
            </div>
          </>
        </Card>
      </div>
    </>
  );
};

export default details;
