import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import DeleteLicense from "./deleteLicense";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";

const index = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");

  const { data, isLoading, isFetching } = useGetApiQuery({
    url: "licenses",
    params: apiParam,
  });
  const navigate = useNavigate();

  //Delete Modal
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "License Type", field: "license_type_id" },
    { label: "Key", field: "key" },
    { label: "In Use", field: "is_used" },
    { label: "Status", field: "is_active" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "View",
      icon: "lets-icons:view-alt",
      onClick: (val) => {
        navigate(`/details-license/${data?.data[val]?.id}`, {
          // state: { license: data?.data[val] },
        });
      },
    },
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/edit-license/${data?.data[val]?.id}`, {
          state: { license: data?.data[val] },
        });
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      license_type_id: (
        <div className="flex space-x-3 my-auto items-center">
          <Icon
            icon={item?.license_type?.icon || "uil:lock-slash"}
            width="24"
            height="24"
          />
          <span>{item?.license_type?.name}</span>
        </div>
      ),
      is_used: (
        <Badge
          className={`${item?.is_used
              ? `bg-success-500 text-white`
              : `bg-blue-800 text-white`
            }`}
        >
          <p>{item.is_used ? "In Use" : "Not In Use"}</p>
        </Badge>
      ),
      key: <span className="text-primary-400 font-semibold">{item.key}</span>,
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        title="License List"
        columns={columns}
        actions={actions}
        goto={
          <div className="flex items-center gap-2">
            <Icon
              icon="oui:ml-create-single-metric-job"
              className="text-white font-bold"
            />
            Add New License
          </div>
        }
        gotoLink={"/create-license"}
        loading={isLoading || isFetching}
        changePage={changePage}
        filter={filter}
        setFilter={setApiParam}
        data={tableData}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
      <DeleteLicense
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </div>
  );
};

export default index;
