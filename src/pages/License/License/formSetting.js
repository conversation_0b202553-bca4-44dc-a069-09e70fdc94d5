import * as yup from "yup";

// Initial values for form fields
export const initialValues = {
    license_type_id: "",
    key: "",
    is_used: "",
    description: "",
    purchase_price: "",
    sale_price: "",
    is_active: true,

};

// Validation schema for form fields
export const validationSchema = yup.object({
    license_type_id: yup
        .string()
        .required("License Type is required"),
    key: yup
        .string()
        .required("License Key is required"),
    purchase_price: yup
        .number()
        .typeError("Purchase price must be a number"),
    sale_price: yup
        .number()
        .typeError("Sale price must be a number"),


});
