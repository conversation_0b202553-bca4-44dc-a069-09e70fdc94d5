import React from "react";
import Card from "@/components/ui/Card";
import {
  useGetApiQuery,
  useGetApiWithIdQuery,
  useUpdateApiJsonMutation,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import Text<PERSON>rea<PERSON>ield from "@/components/ui/form/TextAreaField";
import InputSelect from "@/components/ui/form/InputSelect";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";
import { validationSchema } from "./formSetting";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import Loading from "@/components/Loading";

const Edit = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Fetch all License Type
  const { data: licenseTypeData } = useGetApiQuery({ url: "license-types" });
  const allLicenseType = licenseTypeData?.data?.filter(
    (item) => item?.is_active === 1
  );
  const allLicenseTypeOptions = allLicenseType?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  // Fetch the License data using the ID
  const { data: license, isLoading } = useGetApiWithIdQuery(["licenses", id]);

  const [updateApi] = useUpdateApiMutation();
  const [updateApiJson] = useUpdateApiJsonMutation();

  // Initial values populated dynamically with the fetched data
  const initialValues = {
    license_type_id: license?.license_type_id || "",
    key: license?.key || "",
    is_used: license?.is_used || "",
    description: license?.description || "",
    purchase_price: license?.purchase_price || "",
    sale_price: license?.sale_price || "",
    is_active: license?.is_active ? true : false,
  };

  const handleSubmit = async (values, { resetForm }) => {
    const payload = Object.fromEntries(
      Object.entries(values).filter(([_, value]) => value !== "")
    );
    payload.is_active = values.is_active ? 1 : 0;

    try {
      const data = {
        end_point: `licenses/${id}`,
        body: payload,
      };

      const response = await updateApiJson(data).unwrap();
      toast.success("License updated successfully!");
      navigate("/license-list");
      resetForm();
    } catch (err) {
      console.error("Error during API call:", err);
      // toast.error("Failed to update License. Please try again.");
    }
  };

  if (isLoading) {
    return <Loading/>;
  }

  return (
    <div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values, setFieldValue }) => (
          <Form>
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-bold text-lg">
                        Edit{" "}
                        <span className="text-primary-500">{values.key}</span>
                      </span>
                    </div>
                    <div>
                      <Tooltip
                        content="Back to License List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() => navigate("/license-list")}
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </Card>
              </div>
            </div>

            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-8 col-span-12 hidden md:block">
                {/* Read-only cards */}
              </div>

              <div className="xl:col-span-4 col-span-12 space-y-4">
                <Card>
                  <div className="grid grid-cols-1 my-3 gap-3">
                    <InputSelect
                      label="License Type"
                      name="license_type_id"
                      options={allLicenseTypeOptions}
                      placeholder="Select License Type"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-1 my-3">
                    <InputField
                      label="Key"
                      name="key"
                      type="text"
                      placeholder="Enter Key"
                    />
                  </div>
                  <div className="grid grid-cols-1 my-3">
                    <TextAreaField
                      label="Description"
                      name="description"
                      type="text"
                      placeholder="Enter Description"
                    />
                  </div>
                  <div className="grid grid-cols-1 my-3">
                    <InputField
                      label="Purchase Price"
                      name="purchase_price"
                      type="text"
                      placeholder="Enter Purchase Price"
                    />
                  </div>
                  <div className="grid grid-cols-1 my-3">
                    <InputField
                      label="Sale Price"
                      name="sale_price"
                      type="text"
                      placeholder="Enter Sale Price"
                    />
                  </div>

                  <div className="my-3">
                    <Switch
                      label="Is Active"
                      activeClass="bg-success-500"
                      name="is_active"
                      value={values.is_active}
                      onChange={() =>
                        setFieldValue("is_active", !values.is_active)
                      }
                    />
                  </div>
                </Card>
              </div>
            </div>

            {/* Submit and Cancel Buttons */}
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-end items-center gap-4">
                    <Button
                      type="button"
                      className="btn text-center btn-danger"
                      onClick={() => navigate("/license-list")}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="btn text-center btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Updating..." : "Update"}
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Edit;
