import React from "react";
import Card from "@/components/ui/Card";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik, ErrorMessage } from "formik";
import { useNavigate } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import InputSelect from "@/components/ui/form/InputSelect";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import Button from "@/components/ui/Button";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import { initialValues, validationSchema } from "./formSetting";
import { toast } from "react-toastify";

const create = () => {
  // Fetch all License Type
  const { data: licenseTypeData } = useGetApiQuery({ url: "license-types" });
  const allLicenseType = licenseTypeData?.data?.filter(
    (item) => item?.is_active === 1
  );
  const allLicenseTypeOptions = allLicenseType?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  const navigate = useNavigate();
  const [postApi] = usePostApiMutation();

  // Form submission handler
  const handleSubmit = async (values, { resetForm }) => {
    // Filter out empty string fields
    const payload = Object.fromEntries(
      Object.entries(values).filter(([_, value]) => value !== "")
    );

    try {
      const response = await postApi({
        end_point: "licenses",
        body: payload,
      }).unwrap();
      toast.success("License created successfully!");
      navigate("/license-list");
      resetForm();
    } catch (err) {
      toast.error("License creation failed. Please try again.");
    }
  };

  const headerSlotContent = (
    <Tooltip
      content="Back to License List"
      placement="top"
      arrow
      animation="Interactive"
    >
      <div className="m-1" onClick={() => navigate("/license-list")}>
        <Icon
          icon="ion:arrow-back"
          className="w-6 h-6 text-gray-500 cursor-pointer hover:text-primary-500 m-1 hover:border-primary-500"
        />
      </div>
    </Tooltip>
  );

  return (
    <>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ setFieldValue, values, isSubmitting }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Add New License"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <InputSelect
                    label="License Type"
                    name="license_type_id"
                    options={allLicenseTypeOptions}
                    placeholder="Select License Type"
                    required
                  />
                  <ErrorMessage
                    name="license_type_id"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>
                <InputField
                  label="License Key"
                  name="key"
                  type="text"
                  required
                  placeholder="Enter License key"
                />

                <InputField
                  label="Purchase Price"
                  name="purchase_price"
                  type="text"
                  placeholder="Enter Purchase Price"
                />
                <InputField
                  label="Sale Price"
                  name="sale_price"
                  type="text"
                  placeholder="Enter Sale Price"
                />
              </div>
              <div className="my-4">
                <TextAreaField
                  label="Description"
                  name="description"
                  type="text"
                  placeholder="Enter Description"
                />
              </div>

              <div className="flex justify-end mt-5 mb-2 gap-3">
                <Button
                  type="button"
                  className="btn text-center btn-danger"
                  onClick={() => navigate("/license-type-list")}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="btn text-center btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default create;
