import React from "react";
import { useLocation, useParams } from "react-router-dom";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import { useNavigate } from "react-router-dom";
import Tooltip from "@/components/ui/Tooltip";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";

const details = () => {
  const {id} = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { data: licenseData } = useGetApiQuery({ url: "license-types" });
  const { data: license } = useGetApiQuery({ url: `licenses/${id}` });
  const licenseTypes = licenseData?.data || [];


  return (
    <>
      <div className="grid gap-4 grid-cols-12 mt-5">
        <div className="xl:col-span-12 col-span-12 space-y-4">
          <Card>
            <div className="xl:col-span-12 col-span-12 space-y-4">
              <Card>
                <div className="flex justify-between items-center">
                  <div>
                    <span className="font-bold text-lg">
                      License Details of{" "}
                      <span className="text-primary-500">{license?.key}</span>
                    </span>
                  </div>
                  <div className="flex gap-3">
                    <div>
                      <Tooltip
                        content="Back to License List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() => navigate("/license-list")}
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                    <div>
                      <Tooltip
                        content={`Edit ${license?.key} License`}
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() =>
                            navigate(`/edit-license/${license?.id}`)
                          }
                        >
                          <Icon
                            icon="mynaui:edit-one"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </Card>
        </div>
      </div>
      <div className="grid gap-4 grid-cols-1 md:grid-cols-3 mt-5">
        <Card>
          <>
            <span className="font-bold text-slate-600 my-4">
              License Type Name:
            </span>
            <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
              <span className="flex items-center text-slate-700">
                {license?.license_type?.name}
              </span>
            </div>
          </>
        </Card>
        <Card className="col-span-2">
          <>
            <span className="font-bold text-slate-600 my-4">Key:</span>
            <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
              <span className="flex items-start justify-start text-slate-700">
                {license?.key}
              </span>
            </div>
          </>
        </Card>
      </div>
      <div className="grid md:grid-cols-3 grid-cols-1 gap-3 my-4">
        <Card>
          <>
            <span className="font-bold text-slate-600 my-4">
              Purchase Price:
            </span>
            <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
              <span className="flex items-center text-slate-700">
                {license?.purchase_price}
              </span>
            </div>
          </>
        </Card>
        <Card>
          <>
            <span className="font-bold text-slate-600 my-4">Sale Price:</span>
            <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
              <span className="flex items-center text-slate-700">
                {license?.sale_price}
              </span>
            </div>
          </>
        </Card>
        <Card>
          <div className="flex justify-center items-center bg-slate-100 rounded-lg px-3 py-2 mt-8">
            <span className="mr-1 font-bold text-slate-600">Status:</span>
            <Badge
              className={
                license?.is_active
                  ? `text-success-500 font-bold text-base`
                  : `text-danger-500 font-bold text-base`
              }
            >
              {license?.is_active ? (
                <>
                  Active
                  <Icon icon="icon-park-solid:correct" className="ml-2" />
                </>
              ) : (
                <>
                  <Icon icon="maki:cross" className="mr-2" />
                  Inactive
                </>
              )}
            </Badge>
          </div>
        </Card>
      </div>
      <div className="grid gap-4 grid-cols-1 mt-5">
        <Card>
          <>
            <span className="font-bold text-slate-600 my-4">Description:</span>
            <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
              <span className="flex items-center text-slate-700">
                {license?.description}
              </span>
            </div>
          </>
        </Card>
      </div>
    </>
  );
};

export default details;
