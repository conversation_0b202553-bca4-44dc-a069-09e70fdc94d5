import { useRef } from "react";
import SocialContact from "./SocialContact";
import { NavBar } from "./NavBar";
import HeroSection from "./Hero";
import WhyCRM from "./WhyCRM";
import Features from "./Features";
import DemoSection from "./DemoSection";
import RelatedProducts from "./RelatedProducts";
import MobileApp from "./MobileApp";
import { Testimonials } from "./Testimonials";
import Pricings from "./Pricings";
import Footer from "./Footer";

const Home = () => {
    const heroRef = useRef(null);
    const whyCRMRef = useRef(null);
    const featuresRef = useRef(null);
    const pricingsRef = useRef(null);

    return (
        <div>
            <SocialContact />
            <NavBar
                heroRef={heroRef}
                whyCRMRef={whyCRMRef}
                featuresRef={featuresRef}
                pricingsRef={pricingsRef}
            />
            <div ref={heroRef}>
                <HeroSection />
            </div>
            <div ref={whyCRMRef}>
                <WhyCRM />
            </div>
            <div ref={featuresRef}>
                <Features />
            </div>
            <DemoSection />
            <RelatedProducts />
            <MobileApp />
            <Testimonials />
            {/* <About /> */}
            {/* <Contact /> */}
            <div ref={pricingsRef}>
                <Pricings />
            </div>
            <Footer />
        </div>
    );
};

export default Home;
