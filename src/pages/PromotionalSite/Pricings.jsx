import React from "react";
import Pricing from "./Pricing";

const Pricings = () => {
    const allPricings = [
        {
            name: "Basic",
            price: 50,
            about: "Great for small businesses starting with CRM.",
            features: [
                { isAvailable: true, feature: "Customer Management" },
                { isAvailable: true, feature: "Task Automation" },
                { isAvailable: true, feature: "Email Support" },
                { isAvailable: false, feature: "Advanced Reporting" },
                { isAvailable: false, feature: "Custom Dashboards" },
            ],
        },
        {
            name: "Pro",
            price: 100,
            about: "Ideal for growing businesses with expanding needs.",
            features: [
                { isAvailable: true, feature: "Custom Dashboards" },
                { isAvailable: true, feature: "Email Campaigns" },
                { isAvailable: true, feature: "Advanced Reporting" },
                { isAvailable: true, feature: "Sales Automation" },
                { isAvailable: false, feature: "AI-based Analytics" },
            ],
        },
        {
            name: "Enterprise",
            price: 200,
            about: "For large enterprises that require full customization.",
            features: [
                { isAvailable: true, feature: "Unlimited Users" },
                { isAvailable: true, feature: "24/7 Support" },
                { isAvailable: true, feature: "Custom Integrations" },
                { isAvailable: true, feature: "AI-based Analytics" },
                { isAvailable: true, feature: "Dedicated Account Manager" },
            ],
        },
    ];

    return (
        <div className=" md:py-14 max-sm:py-10">
            <div className="max-w-2xl mx-auto text-center pb-16 container">
                <h1 className="text-3xl md:text-4xl font-serif font-bold">
                    Pricing of{" "}
                    <span className="bg-gradient-to-r from-secondaryColor to-primaryColor bg-clip-text text-transparent">
                        BacBon Ltd.
                    </span>{" "}
                    CRM
                </h1>
                <p className="text-gray-600 mt-3">
                    Lorem ipsum dolor sit amet, consectetur adipisicing elit. Sint dicta
                    harum blanditiis pariatur voluptatibus illo at? Esse qui inventore
                    quisquam placeat voluptatum aut sunt suscipit vel, omnis corrupti
                    voluptatibus fugiat.
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-14 container">
                {allPricings.map((price, idx) => (
                    <Pricing key={idx} pricing={price} />
                ))}
            </div>
        </div>
    );
};

export default Pricings;
