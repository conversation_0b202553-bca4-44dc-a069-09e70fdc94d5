import React, { useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Navigation } from "swiper";
import "swiper/swiper-bundle.css";
import { Icon } from "@iconify/react";

const RelatedProducts = () => {
  const swiperRef = useRef(null);

  // Sample product data
  const products = [
    {
      title: "CRM Software",
      rating: 4.5,
      long_description:
        "An all-in-one CRM solution for businesses to manage their relationships effectively.",
      img: "https://i.ibb.co/C0ZspCy/dashboard4.jpg",
      go_to_link: "#",
    },
    {
      title: "Project Management Tool",
      rating: 4.7,
      long_description:
        "Streamline your projects with this comprehensive management tool.",
      img: "https://i.ibb.co/C0ZspCy/dashboard4.jpg",
      go_to_link: "#",
    },
    {
      title: "Email Marketing Service",
      rating: 4.6,
      long_description:
        "Reach your customers effectively with our email marketing service.",
      img: "https://i.ibb.co/C0ZspCy/dashboard4.jpg",
      go_to_link: "#",
    },
    {
      title: "Task Management App",
      rating: 4.8,
      long_description:
        "Organize your tasks efficiently with this user-friendly app.",
      img: "https://i.ibb.co/C0ZspCy/dashboard4.jpg",
      go_to_link: "#",
    },
    {
      title: "Analytics Platform",
      rating: 4.9,
      long_description:
        "Gain insights into your business with our powerful analytics platform.",
      img: "https://i.ibb.co/C0ZspCy/dashboard4.jpg",
      go_to_link: "#",
    },
  ];

  return (
    <div className="mt-10 pb-20">
      <div className="max-w-2xl mx-auto text-center my-10 container">
        <h1 className="text-3xl md:text-4xl font-serif font-bold">
          Related{" "}
          <span className="bg-gradient-to-r from-secondaryColor to-primaryColor bg-clip-text text-transparent">
            Products
          </span>
        </h1>
        <p className="text-gray-600 mt-3">
          Lorem ipsum dolor sit amet, consectetur adipisicing elit. Sint dicta
          harum blanditiis pariatur voluptatibus illo at? Esse qui inventore
          quisquam placeat voluptatum aut sunt suscipit vel, omnis corrupti
          voluptatibus fugiat.
        </p>
      </div>

      <div className="relative container">
        <div className="relative container px-10 overflow-hidden">
          <div className="text-start">
            <Swiper
              ref={swiperRef}
              spaceBetween={50}
              slidesPerView={1}
              //   loop={true}
              navigation={false} // Disable default navigation
              modules={[Pagination, Navigation]}
              autoplay={{
                delay: 3000,
                disableOnInteraction: false,
              }}
              breakpoints={{
                640: {
                  slidesPerView: 1,
                  spaceBetween: 20,
                },
                768: {
                  slidesPerView: 2,
                  spaceBetween: 40,
                },
                1024: {
                  slidesPerView: 3,
                  spaceBetween: 40,
                },
              }}
            >
              {products.map((product, index) => (
                <SwiperSlide key={index} className="pb-5">
                  <div className="flex-shrink-0 mx-2 shadow-md hover:shadow-lg rounded-lg bg-white">
                    <div className="overflow-hidden">
                      <div className="relative block h-60 rounded-t-lg overflow-hidden">
                        <a href={product.go_to_link} className="group">
                          {/* Image Section */}
                          <img
                            src={product.img}
                            alt={product.title}
                            className="absolute inset-0 rounded-t-lg w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                          />
                          {/* Hover Layer */}
                          <div className="absolute inset-0 rounded-t-lg flex items-center justify-center bg-gray-900 bg-opacity-60 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                            <span className="text-white text-lg font-bold underline">
                              See Demo
                            </span>
                          </div>
                        </a>
                      </div>
                      <div className="p-4">
                        <h3 className="text-lg font-semibold">
                          {product.title}
                        </h3>
                        <p className="text-yellow-500">⭐ {product.rating} </p>
                        <p className="text-gray-600 mt-2">
                          {product.long_description} Lorem ipsum dolor sit, amet
                          consectetur adipisicing elit. Similique, quis.
                        </p>
                      </div>
                    </div>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
        {/* Custom Navigation Buttons */}
        <div className="absolute -bottom-16 left-1/2 -translate-x-1/2 flex items-center gap-3">
          <div className="bg-gray-200 hover:bg-sky-100 border-2 border-transparent hover:border-sky-200 rounded-l-xl z-10">
            <button
              onClick={() => swiperRef.current.swiper.slidePrev()}
              className="text-sky-500 px-2 py-2 rounded-full"
            >
              <Icon icon="noto-v1:left-arrow" className="text-4xl" />
            </button>
          </div>
          <div className="bg-gray-200 hover:bg-sky-100 border-2 border-transparent hover:border-sky-200 rounded-r-xl z-10">
            <button
              onClick={() => swiperRef.current.swiper.slideNext()}
              className="text-sky-500 px-2 py-2 rounded-full"
            >
              <Icon icon="noto-v1:right-arrow" className="text-4xl" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RelatedProducts;
