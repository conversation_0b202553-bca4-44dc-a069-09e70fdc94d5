import { Icon } from "@iconify/react/dist/iconify.js";

const Pricing = ({ pricing }) => {
  const { name, price, about, features } = pricing;
  return (
    <div className="relative space-y-8 bg-white hover:bg-sky-100 border border-transparent hover:border-sky-300 p-8 shadow-[0px_0px_30px_2px_rgba(100,100,111,0.1)] w-full rounded-lg group">
      {/* top part  */}
      <div>
        <img
          width={60}
          height={60}
          className="h-[60px] w-[60px] rounded-full border bg-slate-100 object-cover p-2 duration-300 hover:scale-105"
          src="https://images.rawpixel.com/image_800/cHJpdmF0ZS9sci9pbWFnZXMvd2Vic2l0ZS8yMDIzLTAxL2pvYjk2OC1lbGVtZW50LTAxMi14LmpwZw.jpg"
          alt=""
        />
        {/* Price Ribbon SVG  */}
        <div className="absolute -right-[20px] -top-4 ">
          <div className="relative h-full w-full">
            {/* svg  */}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              version="1.1"
              xmlnsXlink="http://www.w3.org/1999/xlink"
              width="120"
              height="120"
              x="0"
              y="0"
              viewBox="0 0 512 512"
              style={{ enableBackground: "new 0 0 512 512" }}
              xmlSpace="preserve"
            >
              <defs>
                <linearGradient
                  id="skyGradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="100%"
                >
                  <stop
                    offset="0%"
                    style={{ stopColor: "#0095FF", stopOpacity: 1 }}
                  />
                  <stop
                    offset="100%"
                    style={{ stopColor: "#87CEFA", stopOpacity: 1 }}
                  />
                </linearGradient>
              </defs>
              <g>
                <path
                  d="M384 0H149.333c-41.237 0-74.667 33.429-74.667 74.667v426.667a10.668 10.668 0 0 0 6.592 9.856c1.291.538 2.676.813 4.075.811a10.663 10.663 0 0 0 7.552-3.115l120.448-120.619C260.48 434.795 325.44 499.2 332.416 507.136c3.261 4.906 9.882 6.24 14.788 2.979a10.67 10.67 0 0 0 3.964-4.835 6.53 6.53 0 0 0 .832-3.947v-448c0-17.673 14.327-32 32-32 5.891 0 10.667-4.776 10.667-10.667S389.891 0 384 0z"
                  style={{ fill: "url(#skyGradient)" }}
                />
                <path
                  d="M394.667 0c23.564 0 42.667 19.103 42.667 42.667v32c0 5.891-4.776 10.667-10.667 10.667H352V42.667C352 19.103 371.103 0 394.667 0z"
                  style={{ fill: "#1976d2" }}
                />
              </g>
            </svg>
            {/* Price  */}
            <div className="absolute left-7 top-8 flex flex-col text-xl font-semibold text-white">
              <span>
                <sub className="text-sm font-normal">$</sub>
                <span>{price}</span>
              </span>
              <span className="text-xs font-normal">/month</span>
            </div>
          </div>
        </div>
      </div>
      <div className="space-y-4 ">
        <p className="capitalize text-gray-700">{about}</p>
        <h3 className="text-2xl font-bold text-slate-800">{name}</h3>
        <ul className="space-y-3">
          {features?.map((feature, idx) => (
            <li
              key={idx}
              className="flex items-center gap-2 text-sm font-semibold text-sky-700"
            >
              {/* "charm:circle-cross" */}
              <Icon
                className={`${
                  feature?.isAvailable ? "text-sky-700" : "text-red-500"
                }`}
                icon={`${
                  feature?.isAvailable ? "el:ok-circle" : "charm:circle-cross"
                }`}
              />
              {feature?.feature}
            </li>
          ))}
        </ul>
        <div className="flex justify-center pt-4">
          <button className="rounded-md border border-sky-500 px-8 py-3 w-full text-xl text-sky-500 duration-200 hover:bg-sky-500 hover:text-white">
            Buy Now
          </button>
        </div>
      </div>
    </div>
  );
};

export default Pricing;
