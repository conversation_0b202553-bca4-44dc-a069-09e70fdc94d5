import { useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Navigation } from "swiper";
import "swiper/swiper-bundle.css";
import { Icon } from "@iconify/react";

export const Testimonials = () => {
  const swiperRef = useRef(null); // Create a ref for the Swiper instance

  const testimonials = [
    {
      name: "<PERSON>",
      image: "https://i.ibb.co.com/41YMp8X/member-2.jpg",
      text: "Amazing product and great support!",
    },
    {
      name: "<PERSON>",
      image: "https://i.ibb.co.com/pxpxRKh/member-1.jpg",
      text: "I love how easy it is to use this service!",
    },
    {
      name: "<PERSON>",
      image: "https://i.ibb.co.com/KGp7gM6/member-3.jpg",
      text: "Fantastic experience with this company!",
    },
    {
      name: "<PERSON>",
      image: "https://i.ibb.co.com/Gdks1yM/member-4.jpg",
      text: "The best service I have ever used!",
    },
    {
      name: "<PERSON>",
      image: "https://i.ibb.co.com/qFcs8VB/samuel-raita-Ri-Dx-Dg-Hg7pw-unsplash.jpg",
      text: "Highly recommend to anyone.",
    },
  ];

  return (
    <div className="py-10 pb-12 bg-white">
      <div className="max-w-2xl mx-auto text-center mb-8 container">
        <h1 className="text-3xl md:text-4xl font-serif font-bold">
          Our Customer's{" "}
          <span className="bg-gradient-to-r from-secondaryColor to-primaryColor bg-clip-text text-transparent">
            Feedback
          </span>
        </h1>
        <p className="text-gray-600 mt-3">
          Lorem ipsum dolor sit amet, consectetur adipisicing elit. Sint dicta
          harum blanditiis pariatur voluptatibus illo at? Esse qui inventore
          quisquam placeat voluptatum aut sunt suscipit vel, omnis corrupti
          voluptatibus fugiat.
        </p>
      </div>

      <div className="relative container px-8">
        {/* Slider container */}
        <Swiper
          ref={swiperRef}
          spaceBetween={50}
          slidesPerView={1}
          loop={true}
          navigation={false} // Disable default navigation
          modules={[Pagination, Navigation]}
          autoplay={{
            delay: 3000,
            disableOnInteraction: false,
          }}
          breakpoints={{
            640: {
              slidesPerView: 1,
              spaceBetween: 20,
            },
            768: {
              slidesPerView: 2,
              spaceBetween: 40,
            },
            1024: {
              slidesPerView: 3,
              spaceBetween: 40,
            },
          }}
        >
          {testimonials.map((testimonial, index) => (
            <SwiperSlide key={index} className="px-2">
              <div className="w-full mb-4 flex-shrink-0 relative overflow-hidden mx-1 hover:border-sky-400 shadow-lg rounded-b-xl rounded-tl-xl border border-sky-200 group">
                <div className="p-5 py-12 pt-16 max-sm:py-20 rounded-xl bg-gray-100 flex flex-col items-center overflow-hidden">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-24 h-24 rounded-l-full group-hover:scale-105 transition-transform duration-300 rounded-br-full object-cover mb-4 border-4 border-secondaryColor absolute -top-1 -right-1"
                  />
                  <p className="text-center font-semibold">{testimonial.name}</p>
                  <p className="text-center text-gray-400">Software Engineer</p>
                  <p className="text-center md:text-left text-gray-500">
                    {testimonial.text} Esse qui inventore quisquam placeat voluptatum aut sunt
                  </p>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
        
        {/* Custom Navigation Buttons */}
        <div className="absolute top-1/2 lg:-left-5 -left-2 z-10 transform -translate-y-1/2">
          <button onClick={() => swiperRef.current.swiper.slidePrev()} className="text-sky-500 px-2 py-2 rounded-full">
          <Icon icon="icon-park-outline:left" className="text-4xl" />
          </button>
        </div>
        <div className="absolute top-1/2 lg:-right-5 -right-2 z-10 transform -translate-y-1/2">
          <button onClick={() => swiperRef.current.swiper.slideNext()} className="text-sky-500 px-2 py-2 rounded-full">
          <Icon icon="mingcute:right-line" className="text-4xl" />
          </button>
        </div>
      </div>
    </div>
  );
};
