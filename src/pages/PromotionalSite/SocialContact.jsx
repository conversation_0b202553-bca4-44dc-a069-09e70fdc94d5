import { Icon } from '@iconify/react/dist/iconify.js';
import React from 'react';

const SocialContact = () => {
    return (
        <div className='fixed right-5 bottom-8 max-sm:bottom-14 flex max-sm:flex-col items-center gap-2 z-50'>
            {/* WhatsApp Link */}
            <a
                href="https://wa.me/+8801836149699"
                target="_blank"
                rel="noopener noreferrer"
            >
                <Icon className='text-5xl' icon="logos:whatsapp-icon" />
            </a>

            {/* Telegram Link */}
            <a
                href="https://t.me/bacbonLtd"
                target="_blank"
                rel="noopener noreferrer"
            >
                <Icon className='text-5xl border-4 border-white rounded-full' icon="logos:telegram" />
            </a>
        </div>
    );
};

export default SocialContact;
