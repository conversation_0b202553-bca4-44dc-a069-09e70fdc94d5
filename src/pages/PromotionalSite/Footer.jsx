import { Icon } from "@iconify/react/dist/iconify.js";
import isoImg from "./assets/iso-certificate.png";
import isoImg2 from "./assets/iso-certification.webp";

export default function Footer() {
    return (
        <footer className="bg-gray-900 text-white py-5 ">
            <div className="container">
                <div className="flex md:justify-between max-sm:flex-col md:items-center gap-5 text-gray-400 p-8">
                    <nav className="w-72">
                        <img
                            className="my-4"
                            src="https://www.bacbonltd.com/images/logo/logo.png"
                            alt=""
                        />
                        <p>
                            Lorem ipsum dolor sit amet consectetur adipisicing elit.
                            Reiciendis sint recusandae distinctio debitis neque. Repellendus
                            placeat qui quasi perspiciatis corrupti.
                        </p>
                    </nav>
                    <nav className="">
                        <ul className="">
                            <li>
                                <a className="cursor-pointer hover:underline flex items-center gap-2">
                                    <Icon className="text-[10px]" icon="lucide:move-right" /> Home
                                </a>
                            </li>
                            <li>
                                <a className="cursor-pointer hover:underline flex items-center gap-2">
                                    <Icon className="text-[10px]" icon="lucide:move-right" />{" "}
                                    Features
                                </a>
                            </li>
                            <li>
                                <a className="cursor-pointer hover:underline flex items-center gap-2">
                                    <Icon className="text-[10px]" icon="lucide:move-right" />{" "}
                                    About
                                </a>
                            </li>
                        </ul>
                    </nav>
                    <nav className="">
                        <ul className="">
                            <li>
                                <a className="cursor-pointer hover:underline flex items-center gap-2">
                                    <Icon className="text-[10px]" icon="lucide:move-right" />{" "}
                                    Services
                                </a>
                            </li>
                            <li>
                                <a className="cursor-pointer hover:underline flex items-center gap-2">
                                    <Icon className="text-[10px]" icon="lucide:move-right" /> Our
                                    Products
                                </a>
                            </li>
                            <li>
                                <a className="cursor-pointer hover:underline flex items-center gap-2">
                                    <Icon className="text-[10px]" icon="lucide:move-right" />{" "}
                                    Vision
                                </a>
                            </li>
                        </ul>
                    </nav>
                    <nav className="text-lg">
                        <div className="flex gap-3 mb-5">
                            <img className="w-16" src={isoImg} alt="" />
                            <img className="w-16" src={isoImg2} alt="" />
                        </div>
                    </nav>
                </div>
                <aside className="text-center text-sm text-gray-500 flex justify-around border-t pt-6 border-gray-500">
                    <p>&copy; 2024 Bacbon Ltd. All Rights Reserved.</p>

                    <ul className="flex h-full flex-wrap items-center justify-center text-3xl gap-5">
                        <li className="cursor-pointer">
                            <a>
                                <Icon className="max-sm:text-xl" icon="logos:facebook" />
                            </a>
                        </li>
                        <li className="cursor-pointer">
                            <a>
                                <Icon className="text-2xl" icon="logos:youtube-icon" />
                            </a>
                        </li>
                        <li className="cursor-pointer">
                            <a>
                                <Icon icon="skill-icons:linkedin" />
                            </a>
                        </li>
                    </ul>
                </aside>
            </div>
        </footer>
    );
}
