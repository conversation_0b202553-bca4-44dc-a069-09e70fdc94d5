import React from "react";
import Feature from "./Feature";

const Features = () => {
    const allFeatures = [
        {
            title: "Lead Management",
            details:
                "Our CRM allows you to capture leads from multiple sources such as web forms, emails, and social media. Track the lead journey, assign tasks, and optimize lead conversion with real-time insights.",
            features: [
                { isAvailable: true, feature: "Automated lead capture" },
                { isAvailable: true, feature: "Lead scoring & qualification" },
                { isAvailable: true, feature: "Custom lead stages" },
            ],
            feature_image: "https://i.ibb.co.com/C0ZspCy/dashboard4.jpg",
            detail_link: "https://crmapp.com/lead-management",
            buy_link: "https://crmapp.com/pricing",
        },
        {
            title: "Sales Pipeline",
            details:
                "Visualize your sales process with our intuitive drag-and-drop pipeline. Move deals through various stages, monitor progress, and forecast future sales to make data-driven decisions.",
            features: [
                { isAvailable: true, feature: "Drag-and-drop deal management" },
                { isAvailable: true, feature: "Sales forecasting tools" },
                { isAvailable: false, feature: "Customizable deal stages" },
            ],
            feature_image: "https://i.ibb.co.com/G5k1b2G/dashboard3.jpg",
            detail_link: "https://crmapp.com/sales-pipeline",
            buy_link: "https://crmapp.com/pricing",
        },
        {
            title: "Task Automation",
            details:
                "Save time by automating repetitive tasks such as follow-up emails, task reminders, and lead nurturing workflows. Focus on what matters while our automation tools handle the routine work.",
            features: [
                { isAvailable: true, feature: "Automated email follow-ups" },
                { isAvailable: true, feature: "Task reminders & notifications" },
                { isAvailable: true, feature: "Custom workflow automation" },
            ],
            feature_image: "https://i.ibb.co.com/JpB3QjX/dashboard6.png",
            detail_link: "https://crmapp.com/task-automation",
            buy_link: "https://crmapp.com/pricing",
        },
        {
            title: "Customer Support",
            details:
                "Enhance customer satisfaction with our built-in support ticket system. Track, manage, and resolve customer inquiries efficiently while keeping detailed records of past interactions.",
            features: [
                { isAvailable: true, feature: "Ticket management system" },
                { isAvailable: true, feature: "Customer interaction history" },
                { isAvailable: true, feature: "Automated responses & ticket routing" },
            ],
            feature_image: "https://i.ibb.co.com/brnjSvf/dashboard5.png",
            detail_link: "https://crmapp.com/customer-support",
            buy_link: "https://crmapp.com/pricing",
        },
    ];

    return (
        <div>
            <div className="max-w-2xl mx-auto text-center my-10 container">
                <h1 className="text-3xl md:text-4xl font-serif font-bold">
                    Features of{" "}
                    <span className="bg-gradient-to-r from-secondaryColor to-primaryColor bg-clip-text text-transparent">
                        BacBon Ltd.
                    </span>{" "}
                    CRM
                </h1>
                <p className="text-gray-600 mt-3">
                    Lorem ipsum dolor sit amet, consectetur adipisicing elit. Sint dicta
                    harum blanditiis pariatur voluptatibus illo at? Esse qui inventore
                    quisquam placeat voluptatum aut sunt suscipit vel, omnis corrupti
                    voluptatibus fugiat.
                </p>
            </div>

            <div>
                {allFeatures.map((feature, idx) => (
                    <Feature key={idx} feature={feature} id={idx} />
                ))}
            </div>
        </div>
    );
};

export default Features;
