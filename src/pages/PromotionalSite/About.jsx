import { Icon } from "@iconify/react/dist/iconify.js";

const About = () => {
    const services = [
        {
            title: "Advanced English Writing (AEW)",
            icon: "mdi:book-open-variant",
        },
        {
            title: "Smart Lecture Correction (SLC)",
            icon: "mdi:comment-check-outline",
        },
        {
            title: "Audio Visual Content Development",
            icon: "mdi:video-outline",
        },
        {
            title: "Research and Consultancy",
            icon: "mdi:magnify",
        },
        {
            title: "Training & Event Management",
            icon: "mdi:account-group-outline",
        },
        {
            title: "BacBon School",
            icon: "mdi:school-outline",
        },
        {
            title: "BacBon Tutors",
            icon: "mdi:teach",
        },
        {
            title: "BacBon Multimedia",
            icon: "mdi:monitor-screenshot",
        },
        {
            title: "Software Solutions",
            icon: "mdi:cog-outline",
        },
    ];

    return (
        <div className="bg-sky-100 relative w-full">
            <div className="flex max-sm:flex-col container gap-10 py-20 items-center sticky">
                <div className="flex-1">
                    <img src="https://www.bacbonltd.com/images/gallery/16.jpg" alt="" />
                </div>
                <div className="flex-1 text-gray-700">
                    <h1 className="text-3xl sm:text-3xl md:text-4xl font-serif font-bold text-gray-800">
                        About
                        <span className="bg-gradient-to-r from-secondaryColor ml-2 to-primaryColor bg-clip-text text-transparent">
                            BacBon Ltd.
                        </span>
                    </h1>
                    <p className="my-4">
                        BacBon Limited is an Ed-Tech-based company in Bangladesh,
                        established in 2013. We focus on making a high social impact on
                        creating better human beings for a better society through providing
                        quality products and services by implementing and accepting
                        innovative ideas to accommodate inclusive and sustainable growth.
                        Our primaryColor focus is to employ ICT innovation to ensure inclusive
                        educational opportunities for all.
                    </p>
                    <div>
                        <p className="text-lg">Our Services:</p>
                        <div className="grid grid-cols-1 md:grid-cols-2">
                            {services.map((service, idx) => (
                                <p className="flex items-center gap-3" key={idx}>
                                    <Icon className="text-primaryColor" icon={service.icon} />
                                    {service?.title}
                                </p>
                            ))}
                        </div>

                        <button className="bg-primaryColor text-white py-2 px-4 mt-4 rounded-md hover:bg-secondaryColor transition">
                            See more
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default About;
