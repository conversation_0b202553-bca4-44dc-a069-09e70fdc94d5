import { Icon } from "@iconify/react/dist/iconify.js";
import { useEffect, useState } from "react";

const Feature = ({ feature, id }) => {
    const { title, details, features, feature_image, details_link, buy_link } = feature;

    const [isVisible, setIsVisible] = useState(false);

    const handleScroll = () => {
        const position = document.getElementById(`feature-${id}`).getBoundingClientRect().top;
        if (position < window.innerHeight * 0.8) {
            setIsVisible(true);
        } else {
            setIsVisible(false);
        }
    };

    useEffect(() => {
        window.addEventListener("scroll", handleScroll);
        return () => {
            window.removeEventListener("scroll", handleScroll);
        };
    }, []);

    return (
        <section
            id={`feature-${id}`}
            className={`container flex flex-col md:flex-row max-sm:flex-col-reverse items-center justify-center pt-5 pb-16 gap-10 ${id % 2 === 0 && "md:flex-row-reverse"
                } overflow-x-hidden `}
        >
            {/* Text Section */}
            <div
                className={`transform transition-all duration-700 ${isVisible
                        ? "translate-x-0 opacity-100"
                        : id % 2 === 0
                            ? "translate-x-8 opacity-0"
                            : "-translate-x-8 opacity-0"
                    }`}
            >
                <h2 className="md:text-4xl text-3xl font-bold font-serif text-gray-800">{title}</h2>
                <p className="my-5 text-gray-600">{details}</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-1 mb-4">
                    {features?.map((item, idx) => (
                        <p key={idx} className="flex gap-3 my-0 items-center">
                            <Icon
                                className={`${item?.isAvailable ? "text-green-500" : "text-red-500"
                                    }`}
                                icon={`${item?.isAvailable ? "el:ok-circle" : "charm:circle-cross"
                                    }`}
                            />
                            {item?.feature}
                        </p>
                    ))}
                </div>

                <div className="text-white flex gap-3">
                    <a href={details_link} className="px-4 py-2 rounded bg-primaryColor">
                        See Details
                    </a>
                    <a href={buy_link} className="px-4 py-2 rounded bg-secondaryColor">
                        Buy Now
                    </a>
                </div>
            </div>

            {/* Image Section */}
            <div
                className={`relative overflow-hidden rounded-lg w-[90%] mx-auto transition-all duration-700 transform shadow-xl hover:-translate-y-2 ${isVisible
                        ? "translate-x-0 opacity-100"
                        : id % 2 === 0
                            ? "-translate-x-10 opacity-0"
                            : "translate-x-10 opacity-0"
                    }`}
            >
                <img className="w-full h-auto" src={feature_image} alt="Dashboard" />
            </div>
        </section>
    );
};

export default Feature;
