import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";

export const NavBar = ({ heroRef, whyCRMRef, featuresRef, pricingsRef }) => {
    const [dropDownState, setDropDownState] = useState(false);
    const dropDownMenuRef = useRef();
    const navigate = useNavigate();

    useEffect(() => {
        const closeDropDown = (e) => {
            if (!dropDownMenuRef?.current?.contains(e?.target)) {
                setDropDownState(false);
            }
        };

        document.addEventListener("mousedown", closeDropDown);

        return () => {
            document.removeEventListener("mousedown", closeDropDown);
        };
    }, []);

    const scrollToSection = (ref) => {
        const navbarHeight = document.querySelector('nav').offsetHeight; // Get the height of the fixed navbar
        const customOffset = 30; // Adjust this value to control how much higher you want the section
    
        if (ref.current) {
            const sectionPosition = ref.current.getBoundingClientRect().top + window.pageYOffset - navbarHeight - customOffset; 
            // Scroll to the calculated position, with custom offset
            window.scrollTo({ top: sectionPosition, behavior: "smooth" });
            setDropDownState(false); // Close dropdown if open
        }
    };

    return (
        <div>
            <nav className="bg-white py-2 fixed z-50 left-0 top-0 w-full shadow-sm">
                <div className="flex items-center justify-between container">
                    <div className="cursor-pointer rounded-2xl py-2 font-semibold transition-all duration-200">
                        <img
                            src="https://www.bacbonltd.com/images/logo/logo.png"
                            alt="Logo"
                            className="w-32 lg:w-40"
                        />
                    </div>
                    <ul className="hidden items-center justify-between gap-10 text-lg md:flex">
                        <li onClick={() => scrollToSection(heroRef)}>
                            <a className="group flex cursor-pointer flex-col">
                                Home
                                <span className="mt-[2px] h-[3px] w-[0px] rounded-full bg-sky-500 transition-all duration-300 group-hover:w-full"></span>
                            </a>
                        </li>
                        <li onClick={() => scrollToSection(whyCRMRef)}>
                            <a className="group flex cursor-pointer flex-col">
                                Why CRM
                                <span className="mt-[2px] h-[3px] w-[0px] rounded-full bg-sky-500 transition-all duration-300 group-hover:w-full"></span>
                            </a>
                        </li>
                        <li onClick={() => scrollToSection(featuresRef)}>
                            <a className="group flex cursor-pointer flex-col">
                                Features
                                <span className="mt-[2px] h-[3px] w-[0px] rounded-full bg-sky-500 transition-all duration-300 group-hover:w-full"></span>
                            </a>
                        </li>
                        <li onClick={() => scrollToSection(pricingsRef)}>
                            <a className="group flex cursor-pointer flex-col">
                                Pricing
                                <span className="mt-[2px] h-[3px] w-[0px] rounded-full bg-sky-500 transition-all duration-300 group-hover:w-full"></span>
                            </a>
                        </li>
                        <button
                            onClick={() => navigate("/login")}
                            className="bg-primaryColor text-white py-2 px-4 rounded-md hover:bg-secondaryColor transition"
                        >
                            Log In
                        </button>
                    </ul>

                    {/* Mobile Dropdown */}
                    <div
                        ref={dropDownMenuRef}
                        onClick={() => setDropDownState(!dropDownState)}
                        className="relative flex transition-transform md:hidden"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="cursor-pointer"
                        >
                            <line x1="4" x2="20" y1="12" y2="12" />
                            <line x1="4" x2="20" y1="6" y2="6" />
                            <line x1="4" x2="20" y1="18" y2="18" />
                        </svg>
                        {dropDownState && (
                            <ul className="z-50 gap-2 bg-gray-200 py-4 absolute right-0 top-11 flex w-[200px] flex-col rounded-lg text-black">
                                <li
                                    onClick={() => scrollToSection(heroRef)}
                                    className="cursor-pointer px-4"
                                >
                                    Home
                                </li>
                                <li
                                    onClick={() => scrollToSection(whyCRMRef)}
                                    className="cursor-pointer px-4"
                                >
                                    Why CRM
                                </li>
                                <li
                                    onClick={() => scrollToSection(featuresRef)}
                                    className="cursor-pointer px-4"
                                >
                                    Features
                                </li>
                                <li
                                    onClick={() => scrollToSection(pricingsRef)}
                                    className="cursor-pointer px-4"
                                >
                                    Pricing
                                </li>
                                <li
                                    onClick={() => navigate("/login")}
                                    className="cursor-pointer px-4"
                                >
                                    Log In
                                </li>
                            </ul>
                        )}
                    </div>
                </div>
            </nav>
        </div>
    );
};
