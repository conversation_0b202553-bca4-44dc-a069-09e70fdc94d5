const Contact = () => {
    return (
        <div
            className="relative h-[65vh] bg-cover bg-center"
            style={{
                backgroundImage: "url('https://i.ibb.co.com/LJCHMH6/demo1.jpg')",
                backgroundAttachment: "fixed",
            }}
        >
            <div className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"></div>

            {/* Content Section */}
            <div className="relative z-10 flex items-center justify-center h-full">
                <div className="bg-white bg-opacity-90 p-8 rounded-lg shadow-lg w-full max-w-lg">
                    <h2 className="text-3xl font-bold mb-6 text-gray-800 text-center">
                        Request a Demo
                    </h2>

                    {/* CRM Demo Request Form */}
                    <form>
                        <div className="flex gap-4">
                            <div className="mb-4">
                                <label
                                    htmlFor="name"
                                    className="block text-gray-700 font-medium mb-2"
                                >
                                    Your Name
                                </label>
                                <input
                                    id="name"
                                    type="text"
                                    placeholder="Enter your name"
                                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor"
                                />
                            </div>

                            <div className="mb-4">
                                <label
                                    htmlFor="email"
                                    className="block text-gray-700 font-medium mb-2"
                                >
                                    Email Address
                                </label>
                                <input
                                    id="email"
                                    type="email"
                                    placeholder="Enter your email"
                                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor"
                                />
                            </div>
                        </div>

                        <div className="mb-4">
                            <label
                                htmlFor="message"
                                className="block text-gray-700 font-medium mb-2"
                            >
                                Message
                            </label>
                            <textarea
                                id="message"
                                rows="4"
                                placeholder="Tell us about your business needs"
                                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor"
                            ></textarea>
                        </div>

                        <button
                            type="submit"
                            className="w-full bg-primaryColor text-white py-2 px-4 rounded-md hover:bg-primaryColor-dark transition"
                        >
                            Submit Request
                        </button>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Contact;
