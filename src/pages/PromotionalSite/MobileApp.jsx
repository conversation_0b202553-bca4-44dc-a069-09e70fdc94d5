import { Icon } from "@iconify/react/dist/iconify.js";
import androidIcon from "./assets/androidIcon.png";
// import androidIcon from "../../../assets/androidIcon.png";
import iosIcon from "./assets/iosIcon.png";

const MobileApp = () => {
    //   const apps = [
    //     {
    //       title: "Android App",
    //       img: "https://via.placeholder.com/150x300",
    //       mobileShape: "https://i.ibb.co.com/jT9tcdD/android-Icon.png",
    //       features: [
    //         "User Authentication",
    //         "Push Notifications",
    //         "Dark Mode",
    //         "In-App Purchases",
    //         "Real-time Messaging",
    //         "GPS and Location Services",
    //         "Offline Access",
    //         "Data Syncing",
    //       ],
    //       link: "https://play.google.com/store",
    //     },
    //     {
    //       title: "iOS App",
    //       img: "https://via.placeholder.com/150x300",
    //       mobileShape: "https://i.ibb.co.com/HrfVW4n/iosIcon.png",
    //       features: [
    //         "User Authentication",
    //         "Push Notifications",
    //         "Face ID Authentication",
    //         "In-App Purchases",
    //         "Real-time Messaging",
    //         "GPS and Location Services",
    //         "Offline Access",
    //         "Data Syncing",
    //       ],
    //       link: "https://apps.apple.com/",
    //     },
    //   ];

    const androidFeatures = [
        { feature: "User Authentication", icon: "mdi:account-lock-outline" },
        { feature: "Push Notifications", icon: "mdi:bell-outline" },
        { feature: "Dark Mode", icon: "mdi:weather-night" },
        { feature: "In-App Purchases", icon: "mdi:cart-outline" },
        { feature: "Real-time Messaging", icon: "mdi:message-text-outline" },
        { feature: "GPS and Location Services", icon: "mdi:map-marker-outline" },
        { feature: "Offline Access", icon: "mdi:wifi-off" },
        { feature: "Data Syncing", icon: "mdi:sync" },
    ];

    const iosFeatures = [
        { feature: "User Authentication", icon: "mdi:account-lock-outline" },
        { feature: "Push Notifications", icon: "mdi:bell-outline" },
        { feature: "Face ID Authentication", icon: "mdi:face-recognition" },
        { feature: "In-App Purchases", icon: "mdi:cart-outline" },
        { feature: "Real-time Messaging", icon: "mdi:message-text-outline" },
        { feature: "GPS and Location Services", icon: "mdi:map-marker-outline" },
        { feature: "Offline Access", icon: "mdi:wifi-off" },
        { feature: "Data Syncing", icon: "mdi:sync" },
    ];

    return (
        <div className="pb-12">
            <div className="max-w-2xl mx-auto text-center my-10 container">
                <h1 className="text-3xl md:text-4xl font-serif font-bold">
                    Features of our{" "}
                    <span className="bg-gradient-to-r from-secondaryColor to-primaryColor bg-clip-text text-transparent">
                        CRM App
                    </span>
                </h1>
                <p className="text-gray-600 mt-3">
                    Lorem ipsum dolor sit amet, consectetur adipisicing elit. Sint dicta
                    harum blanditiis pariatur voluptatibus illo at? Esse qui inventore
                    quisquam placeat voluptatum aut sunt suscipit vel, omnis corrupti
                    voluptatibus fugiat.
                </p>
            </div>

            <div className="p-6 bg-gray-100 container">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
                    {/* {apps.map((app, index) => ( */}

                    {/* android app  */}
                    <div className="bg-white shadow-md rounded-lg p-8 lg:px-16 md:w-[80%] mx-auto relative overflow-hidden">
                        <div>
                            <h2 className="text-2xl font-semibold text-center">Android</h2>
                            <div className="w-60 h-10 absolute top-8 bg-sky-500 -rotate-45 -left-20"><p className="text-center text-white pt-2 pl-4 font-serif text-sm">Comming Soon..</p></div>
                            <div className="relative h-[50%] w-[170px] flex justify-center mx-auto my-5">
                                <a
                                    target="_blank"
                                    href="https://play.google.com/store"
                                    className="absolute overflow-hidden inset-0 w-[90%] translate-x-[5%] translate-y-[3%] h-[95%] flex justify-center apps-center z-10 group"
                                >
                                    <img
                                        src="https://i.ibb.co.com/L64V7m8/Screenshot-2024-09-24-125329.png"
                                        alt="Content"
                                        className="w-full h-[99%] rounded-lg transition-transform duration-300 group-hover:scale-105"
                                    />

                                    {/* Hover Layer */}
                                    <div className="absolute inset-0 flex items-center rounded-lg justify-center bg-gray-900 bg-opacity-60 h-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                        <span className="text-white text-md font-bold group-hover:cursor-pointer underline rounded-md">
                                            See Demo
                                        </span>
                                    </div>
                                </a>
                                <img
                                    src={androidIcon}
                                    alt="shape"
                                    className="relative z-20 w-full pointer-events-none"
                                />
                            </div>
                            {/* <h3 className="text-lg font-bold mb-2">Key Features:</h3> */}
                            <ul className="list-disc">
                                {androidFeatures.map((feature, index) => (
                                    <li
                                        key={index}
                                        className="mb-1 flex items-center gap-3 text-gray-600 duration-300 hover:text-primaryColor"
                                    >
                                        <Icon icon={feature.icon} className="text-sky-600" />{" "}
                                        {feature.feature}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>

                    {/* ios app  */}
                    <div className="bg-white shadow-md rounded-lg p-8 lg:px-16 md:w-[80%] max-sm:mx-auto relative overflow-hidden">
                        <h2 className="text-2xl font-semibold text-center">IOS</h2>
                        <div className="w-60 h-10 absolute top-8 bg-sky-500 -rotate-45 -left-20"><p className="text-center text-white pt-2 pl-4 font-serif text-sm">Comming Soon..</p></div>
                        <div className="relative h-[50%] w-[220px] flex justify-center mx-auto my-5">
                            <a
                                target="_blank"
                                href="https://apps.apple.com/"
                                className="absolute overflow-hidden rounded-3xl inset-0 w-[70%] translate-x-[21%] translate-y-[5%] h-[92%] flex justify-center apps-center z-10 group"
                            >
                                <img
                                    src="https://i.ibb.co.com/PrTQxCd/Screenshot-2024-09-24-125154.png"
                                    alt="Content"
                                    className="w-full h-[99%] rounded-3xl transition-transform duration-300 group-hover:scale-105"
                                />

                                {/* Hover Layer */}
                                <div className="absolute inset-0 flex items-center rounded-lg justify-center bg-gray-900 bg-opacity-60 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <span className="text-white text-md font-bold group-hover:cursor-pointer underline rounded-md">
                                        See Demo
                                    </span>
                                </div>
                            </a>
                            <img
                                src={iosIcon}
                                alt="shape"
                                className="relative z-20 w-full pointer-events-none"
                            />
                        </div>
                        {/* <h3 className="text-lg font-bold mb-2">Key Features:</h3> */}
                        <ul className="list-disc">
                            {iosFeatures.map((feature, index) => (
                                <li
                                    key={index}
                                    className="mb-1 flex items-center gap-3 text-gray-600 duration-300 hover:text-primaryColor"
                                >
                                    <Icon icon={feature.icon} className="text-sky-600" />{" "}
                                    {feature.feature}
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* ))} */}
                </div>
            </div>
        </div>
    );
};

export default MobileApp;
