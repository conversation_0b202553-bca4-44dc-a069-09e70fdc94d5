import { Icon } from "@iconify/react/dist/iconify.js";
import React from "react";

const CRM = ({ data }) => {
    const { icon, title, details } = data;
    return (
        <div className="shadow-md hover:shadow-xl bg-white px-8 py-10 text-center rounded-lg ">
            <div>
                <Icon className="text-6xl text-secondaryColor mx-auto mb-3" icon={icon} />
            </div>
            <div>
                <h1 className="text-xl font-semibold">{title}</h1>
                <p className="text-gray-600">{details}</p>
            </div>
        </div>
    );
};

export default CRM;
