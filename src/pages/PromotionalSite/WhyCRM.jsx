import React from "react";
import CRM from "./CRM";

const WhyCRM = () => {
    const crmBenefits = [
        {
            icon: "bi:people",
            title: "User-Friendly Interface",
            details:
                "Our CRM is designed with simplicity in mind, ensuring that your team can easily navigate and manage tasks with minimal training.",
        },
        {
            icon: "mdi:security-lock-outline",
            title: "Enhanced Security",
            details:
                "With advanced encryption and secure access control, we prioritize your business data's safety.",
        },
        {
            icon: "fluent:task-list-square-24-regular",
            title: "Comprehensive Task Management",
            details:
                "Keep track of tasks, deadlines, and team performance with powerful task management features that improve productivity.",
        },
        {
            icon: "carbon:analytics",
            title: "In-Depth Analytics",
            details:
                "Get actionable insights through real-time analytics and reports, allowing you to make data-driven decisions.",
        },
        {
            icon: "carbon:cloud-upload",
            title: "Cloud Integration",
            details:
                "Our CRM supports seamless cloud integration, ensuring that your data is always accessible, no matter where you are.",
        },
        {
            icon: "fluent:people-team-20-regular",
            title: "Team Collaboration",
            details:
                "Improve collaboration across your team with integrated tools that make communication and project management a breeze.",
        },
    ];

    return (
        <div className="md:pb-10 max-sm:py-10">
            <div className="max-w-2xl mx-auto text-center my-10 container">
                <h1 className="text-3xl md:text-4xl font-serif font-bold">
                    Why our{" "}
                    <span className="bg-gradient-to-r from-secondaryColor to-primaryColor bg-clip-text text-transparent">
                        CRM
                    </span>{" "}?
                </h1>
                <p className="text-gray-600 mt-3">
                    Lorem ipsum dolor sit amet, consectetur adipisicing elit. Sint dicta
                    harum blanditiis pariatur voluptatibus illo at? Esse qui inventore
                    quisquam placeat voluptatum aut sunt suscipit vel, omnis corrupti
                    voluptatibus fugiat.
                </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 container">
                {crmBenefits.map((data, idx) => (
                    <CRM key={idx} data={data} />
                ))}
            </div>
        </div>
    );
};

export default WhyCRM;
