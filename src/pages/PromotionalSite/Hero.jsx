import React from "react";
import laptopImage from "./assets/laptopIcon.png";

const HeroSection = () => {
  return (
    <div
      style={{ height: "80vh" }}
      className="relative overflow-hidden max-sm:mt-10 bg-white"
    >
      <div className="flex flex-col md:flex-row max-sm:flex-col-reverse h-full items-center justify-center max-sm:gap-5 md:gap-10 bg-transparent  absolute container mx-auto left-1/2 -translate-x-1/2 z-20 mt-12">
        <div className="md:w-1/2 w-full text-center md:text-left mb-8 z-10 md:mb-0">
          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-serif font-bold text-gray-800 mb-4">
            Optimize Business with <br className="hidden lg:block" />
            <span className="bg-gradient-to-r from-secondaryColor to-primaryColor bg-clip-text text-transparent">
              CRM Solutions
            </span>
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 mb-6">
            Empower your team with the tools they need to manage customer
            relationships, track sales, and grow your business. Our CRM software
            is designed to help you work smarter, not harder.
          </p>
          <button className="text-lg w-40 h-12 before:absolute before:block before:inset-0 before:-z-10 before:bg-sky-500 text-white after:block hover:after:w-full after:w-0 after:hover:left-0 after:right-0 after:top-0 after:h-full after:-z-10 after:duration-300 after:bg-sky-700 after:absolute relative inline-block">Get Started</button>

        </div>

        <div className="relative md:w-1/2 w-full flex justify-center md:justify-end">
          {/* Background Laptop Image */}
          <img
            src={laptopImage}
            alt="Laptop"
            className="relative z-20 w-full"
          />

          {/* Inner Image */}
          <div className="absolute inset-0 w-[70%] translate-x-[21%] -translate-y-[5%] flex justify-center items-center z-10">
            <img
              src="https://i.ibb.co.com/G5k1b2G/dashboard3.jpg"
              alt="Inner Content"
              className="object-contain w-full"
            />
          </div>
        </div>
      </div>

      <div className="w-[600px] bg-secondaryColor h-[600px] absolute rotate-45 z-0 mt-5 -translate-y-1/2 top-1/2 md:-right-[40%] lg:-right-[30%] xl:-right-[22%] hidden md:block"></div>
    </div>
  );
};

export default HeroSection;
