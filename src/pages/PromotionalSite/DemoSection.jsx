import laptopImage from "./assets/laptopIcon.png";

const DemoSection = () => {
  const demos = [
    {
      title: "Admin",
      go_to_link: "#",
      img: "https://i.ibb.co.com/Cn5bPZv/crm-demo.png",
    },
    {
      title: "User",
      go_to_link: "#",
      img: "https://i.ibb.co.com/G5k1b2G/dashboard3.jpg",
    },
  ];
  return (
    <div className="py-10 bg-white">
      <div className="max-w-2xl mx-auto text-center my-10 mt-5 container">
        <h1 className="text-3xl md:text-4xl font-serif font-bold">
          Demos of our{" "}
          <span className="bg-gradient-to-r from-secondaryColor to-primaryColor bg-clip-text text-transparent">
            CRM
          </span>{" "}
        </h1>
        <p className="text-gray-600 mt-3">
          Lorem ipsum dolor sit amet, consectetur adipisicing elit. Sint dicta
          harum blanditiis pariatur voluptatibus illo at? Esse qui inventore
          quisquam placeat voluptatum aut sunt suscipit vel, omnis corrupti
          voluptatibus fugiat.
        </p>
      </div>

      <div className="grid grid-cols-2 container gap-10 pt-5">
        {demos.map((item, idx) => (
          <div key={idx}>
          <div className="relative md:w-2/3 w-full flex justify-center mx-auto">
            {/* Inner Image Group */}
            <a
              href={item.go_to_link}
              className="absolute inset-0 w-[70%] translate-x-[21%] translate-y-[5%] h-[90%] flex justify-center items-center z-10 group"
            >
              {/* Inner Image */}
              <img
                src={item.img}
                alt="Inner Content"
                className="object-contain w-full transition-transform duration-300 group-hover:scale-105"
              />
        
              {/* Hover Layer */}
              <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-60 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <span className="text-white text-lg font-bold group-hover:cursor-pointer underline rounded-md">
                  See Demo
                </span>
              </div>
            </a>
        
            {/* Background Laptop Image (on top) */}
            <img
              src={laptopImage}
              alt="Laptop"
              className="relative z-20 w-full pointer-events-none"
            />
          </div>
          <p className="md:text-xl font-semibold text-center">{item.title}</p>
        </div>
        
        ))}
      </div>
    </div>
  );
};

export default DemoSection;
