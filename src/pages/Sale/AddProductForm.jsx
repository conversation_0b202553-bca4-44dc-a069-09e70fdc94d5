import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Modal from "@/components/ui/Modal";
import ProductForm from "./ProductForm";

const AddProductForm = ({ isModalOpen, closeModal, handleAddProduct }) => {
    const getValidationSchema = (values) =>
        Yup.object().shape({
            product_id: Yup.string().required("Please select a product."),
            unit_price: Yup.number()
                .required("Unit price is required")
                .typeError("Unit price must be a valid number")
                .positive("Unit price must be a positive number."),
            qty: Yup.number()
                .required("Quantity is required")
                .typeError("Quantity must be a valid number")
                .positive("Quantity must be a positive number."),
            // warranty_period_value: Yup.string().nullable().when('warranty_value', {
            //     is: true,
            //     then: () => Yup.string()
            //         .required("Warranty Note is required"),
            //     otherwise: () => Yup.string().nullable(),
            // }),
            warranty_type: Yup.string().nullable().when('warranty_value', {
                is: true,
                then: () => Yup.string()
                    .required("Warranty Type is required"),
                otherwise: () => Yup.string().nullable(),
            }),
            warranty_period: Yup.string()
                .nullable()
                .when('warranty_value', {
                    is: true,
                    then: () =>
                        Yup.string()
                            .required("Required")
                            .matches(/^\d*$/, "Only numbers are acceptable")
                            .test("positive-number", "Required", (value) => {
                                return value && parseInt(value, 10) > 0;
                            }),
                    otherwise: () => Yup.string().nullable(),
                }),
            guarantee_period: Yup.string()
                .nullable()
                .when(['enable_guaranty', 'warranty_value'], {
                    is: (enable_guaranty, warranty_value) => !!enable_guaranty && !!warranty_value, // Ensure both are explicitly true
                    then: () =>
                        Yup.string()
                            .required("Guarantee period is required")
                            .matches(/^\d*$/, "Only numbers are acceptable")
                            .test("positive-number", "Required", (value) => {
                                return value && parseInt(value, 10) > 0;
                            }),
                    otherwise: () => Yup.string().nullable(),
                }),
            // warranty_period: Yup.string()
            // .nullable()
            // .when('warranty_value', {
            //     is: true,
            //     then: () =>
            //         Yup.string()
            //             .required("Warranty period is required")
            //             .matches(/^\d*$/, "Only numbers are acceptable") 
            //             .test("positive-number", "Warranty period must be a number and greater than 0", (value) => {
            //                 return value && parseInt(value, 10) > 0;
            //             }),
            //     otherwise: () => Yup.string().nullable(),
            // }),       
        });

    const validateForm = (values) => {
        try {
            const schema = getValidationSchema(values);
            schema.validateSync(values, { abortEarly: false });
        } catch (error) {
            return error.inner.reduce((acc, currentError) => {
                acc[currentError.path] = currentError.message;
                return acc;
            }, {});
        }
        return {};
    };

    return (
        <Modal activeModal={isModalOpen} onClose={closeModal} title="Select Product" className="max-w-5xl">
            <Formik
                initialValues={{
                    category_id: '',
                    sub_category_id: '',
                    brand_id: '',
                    product_id: '',
                    unit_price: null,
                    qty: null,
                    warranty_value: false,
                    enable_guaranty: false,
                    warranty_period_value: "",
                    description: '',
                    warranty_type: 'years',
                    warranty_period: '',
                    guarantee_period: '',
                    related_products: [],
                }}
                validate={validateForm}
                onSubmit={(values, { resetForm }) => {
                    handleAddProduct(values, resetForm);
                    closeModal();
                }}
            >
                {({ values, handleChange, errors, touched, setFieldValue }) => (
                    <ProductForm
                        values={values}
                        touched={touched}
                        errors={errors}
                        setFieldValue={setFieldValue}
                    />
                )}
            </Formik>
        </Modal>
    );
};

export default AddProductForm;
