import React, { useState, useEffect } from 'react';
import InputField from '@/components/ui/form/InputField';
import Card from '@/components/ui/Card';
import { useGetApiQuery } from '@/store/api/apihandler/commonSlice';
import { Icon } from '@iconify/react';
import DateTimePicker from '@/components/ui/form/DateTimePicker';

const SellInformationCard = ({ values, setFieldValue, touched, errors, onClientSelect }) => {
    const [isClientNameDropdownOpen, setIsClientNameDropdownOpen] = useState(false);
    const [isPhoneDropdownOpen, setIsPhoneDropdownOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedClientData, setSelectedClientData] = useState(null);
    const [isTyping, setIsTyping] = useState(false);

    const { data: clientsData, isLoading } = useGetApiQuery({
        url: `clients?search=${searchTerm}`,
        params: { pagination: 0, is_active: 1 },
    });

    const clients = clientsData || [];

    useEffect(() => {
        if (selectedClientData) {
            console.log('Complete Selected Client Data:', selectedClientData);
        }
    }, [selectedClientData]);


    const handleInputChange = (e, field) => {
        const value = e.target.value;
        setIsTyping(true);

        if (selectedClientData) {
            setSelectedClientData(null);

            // Reset all related fields
            const fieldsToReset = {
                client_id: null,
                client_name: '',
                phone: '',
                billing_address: '',
                shipping_address: '',
            };

            Object.entries(fieldsToReset).forEach(([key, defaultValue]) => {
                setFieldValue(key, defaultValue);
            });
        }

        setFieldValue(field, value);

        if (field === 'phone') {
            const adjustedSearchTerm = value.startsWith('0') ? value.slice(1) : value;
            setSearchTerm(adjustedSearchTerm);
            setIsPhoneDropdownOpen(adjustedSearchTerm.length > 0);
            setIsClientNameDropdownOpen(false);
        } else {
            setSearchTerm(value);
            setIsClientNameDropdownOpen(value.length > 0);
            setIsPhoneDropdownOpen(false);
        }
    };


    const handleSelectClient = (client) => {
        setSelectedClientData(client);
        setIsTyping(false);

        const formattedClientData = {
            client_id: client.id,
            client_name: client.name || '',
            phone: client.phone || '',
            billing_address: client.address || '',
            shipping_address: client.shipping_address || '',
        };

        Object.entries(formattedClientData).forEach(([field, value]) => {
            setFieldValue(field, value);
        });

        if (onClientSelect) {
            onClientSelect(client);
        }

        setSearchTerm('');
        setIsClientNameDropdownOpen(false);
        setIsPhoneDropdownOpen(false);
    };

    const handleCheckboxChange = (e) => {
        setFieldValue('is_checked', e.target.checked);
    };

    const handleBlur = (e, field) => {
        setTimeout(() => {
            setIsClientNameDropdownOpen(false);
            setIsPhoneDropdownOpen(false);
        }, 200);

        if (isTyping && !selectedClientData) {
            setFieldValue(field, e.target.value);
        }
    };

    const renderClientDropdown = (isOpen, clients, isLoading) => {
        if (!isOpen) return null;

        return (
            <div className="absolute w-full bg-white border border-gray-300 shadow-lg mt-1 rounded-md z-50 max-h-[250px] overflow-y-auto">
                {isLoading ? (
                    <div className="flex items-center justify-center p-4 text-gray-600">
                        <Icon icon="mdi:loading" className="w-5 h-5 mr-2 animate-spin" />
                        <span>Loading...</span>
                    </div>
                ) : clients.length > 0 ? (
                    clients.map((client) => (
                        <div
                            key={client.id}
                            onClick={() => handleSelectClient(client)}
                            className="flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150"
                        >
                            {client.image ? (
                                <img
                                    src={`${import.meta.env.VITE_MEDIA_URL}/${client.image}`}
                                    alt={client.name}
                                    className="w-10 h-10 rounded-full object-cover mr-3"
                                />
                            ) : (
                                <Icon icon="mdi:account-circle" className="w-10 h-10 text-gray-400 mr-3" />
                            )}
                            <div className="flex-1">
                                <div className="font-medium text-gray-900">{client.name}</div>
                                <div className="text-sm text-gray-500">
                                    {client.phone ? `${client.phone}` : 'No phone'}
                                </div>
                            </div>
                            <Icon icon="mdi:chevron-right" className="w-5 h-5 text-gray-400" />
                        </div>
                    ))
                ) : (
                    <div className="flex items-center justify-center p-4 text-gray-600">
                        <Icon icon="mdi:account-off-outline" className="w-5 h-5 mr-2" />
                        <span>No users found</span>
                    </div>
                )}
            </div>
        );
    };

    return (
        <Card
            title="Generate Invoice"
            className="w-full"
            noborder={true}
            titleClass="text-lg font-bold text-gray-800"
        >
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
                <div className="relative">
                    <InputField
                        label="Customer Name"
                        name="client_name"
                        type="text"
                        placeholder="Search customer by name..."
                        value={values.client_name}
                        onChange={(e) => handleInputChange(e, 'client_name')}
                        onFocus={() => setIsClientNameDropdownOpen(true)}
                        onBlur={(e) => handleBlur(e, 'client_name')}
                        className="w-full"
                        autoComplete="off"
                        required
                    />
                    {renderClientDropdown(isClientNameDropdownOpen, clients, isLoading)}
                </div>

                <div className="relative">
                    <InputField
                        label="Phone Number"
                        name="phone"
                        type="tel"
                        placeholder="Search phone by number..."
                        value={values.phone}
                        onChange={(e) => handleInputChange(e, 'phone')}
                        onFocus={() => setIsPhoneDropdownOpen(true)}
                        onBlur={(e) => handleBlur(e, 'phone')}
                        className="w-full"
                        autoComplete="off"
                        required
                    />
                    {renderClientDropdown(isPhoneDropdownOpen, clients, isLoading)}
                </div>

                <InputField
                    label="Billing Address"
                    name="billing_address"
                    type="text"
                    placeholder="Enter billing address"
                    value={values.billing_address}
                    onChange={(e) => setFieldValue('billing_address', e.target.value)}
                    className="w-full"
                    autoComplete="off"
                    required
                />
                <div>

                    <InputField
                        label="Shipping Address"
                        name="shipping_address"
                        type="text"
                        placeholder="Enter shipping address"
                        value={values.shipping_address}
                        onChange={(e) => setFieldValue('shipping_address', e.target.value)}
                        className="w-full"
                        autoComplete="off"
                        required
                    />
                    {/* {selectedClientData && */}
                    <div className='mt-2'>
                        <input
                            type="checkbox"
                            id="is_checked"
                            name="is_checked"
                            checked={values.is_checked || false}
                            onChange={handleCheckboxChange}
                            className="mr-2"
                        />
                        <label htmlFor="is_checked" className="text-gray-500">Edit the shipping address</label>
                    </div>
                    {/* } */}
                </div>
                <DateTimePicker
                    name='invoice_date'
                    label='Sale Date'
                    inputType={'date'}
                />
            </div>
        </Card>
    );
};

export default SellInformationCard;