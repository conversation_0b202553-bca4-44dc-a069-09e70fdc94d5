import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import { toast } from 'react-toastify';
import { useGetApiWithIdQuery } from '@/store/api/apihandler/commonSlice';
import { Icon } from '@iconify/react';
import Modal from '@/components/ui/Modal';
import Badge from '@/components/ui/Badge';

const AddedProductsTable = ({ allProducts, addedProducts, handleEditProduct, handleDeleteProduct, handleUploadSerial }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [serialsInitialValues, setSerialsInitialValues] = useState([]);
    const { data: allSerials } = useGetApiWithIdQuery(
        ["unsold-product-serials", selectedProduct?.product_id],
        {
            skip: !selectedProduct?.product_id,
        }
    );

    const handleOpenModal = (addedProduct) => {
        setSelectedProduct(addedProduct);

        const existingSerials = addedProduct.serials || [];
        const newQty = addedProduct.qty;

        let serialsToSet = [];

        if (newQty > existingSerials.length) {
            serialsToSet = [...existingSerials, ...Array(newQty - existingSerials.length).fill("")];
        } else if (newQty < existingSerials.length) {
            serialsToSet = Array(newQty).fill("");
        } else {
            serialsToSet = [...existingSerials];
        }

        setSerialsInitialValues(serialsToSet);
        setIsModalOpen(true);
    };

    const handleCloseModal = () => {
        setIsModalOpen(false);
        setSelectedProduct(null);
        setSerialsInitialValues([]);
    };

    return (
        <div className='overflow-x-auto'>
            <table className="table-auto w-full text-left">
                <thead>
                    <tr>
                        <th className="border-r text-[#1F2F70] font-medium border-white px-4 py-2 bg-[#E3E9FE]">Product Name</th>
                        <th className="border-r font-medium border-white px-4 py-2 bg-[#E3E9FE] text-[#1F2F70]">Unit Price</th>
                        <th className="border-r font-medium border-white px-4 py-2 bg-[#E3E9FE] text-[#1F2F70]">Quantity</th>
                        <th className="border-r font-medium border-white px-4 py-2 bg-[#E3E9FE] text-[#1F2F70]">Total Price</th>
                        {/* <th className="border-r font-medium border-white px-4 py-2 bg-[#E3E9FE] text-[#1F2F70]">Warranty Period</th> */}
                        <th className="border-r font-medium border-white px-4 py-2 bg-[#E3E9FE] text-[#1F2F70]">Select Serial</th>
                        <th className="border font-medium px-4 py-2 bg-[#E3E9FE] text-[#1F2F70]">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {addedProducts.length === 0 ? (
                        <tr>
                            <td colSpan="8" className="text-center py-4 text-neutral-500">
                                No products added yet.
                            </td>
                        </tr>
                    ) : (
                        addedProducts.map((addedProduct, index) => {
                            const productInfo = allProducts?.data?.find(product => product.id === addedProduct.product_id);

                            return (
                                <tr key={index}>
                                    <td className="border px-4 py-2">{addedProduct.product_name}</td>
                                    <td className="border px-4 py-2">{addedProduct.unit_price}</td>
                                    <td className="border px-4 py-2">{addedProduct.qty}</td>
                                    <td className="border px-4 py-2">{(addedProduct.unit_price * addedProduct.qty).toFixed(2)}</td>
                                    {/* <td className="border px-4 py-2">{addedProduct.warranty_period_value} {addedProduct.warranty_period}</td> */}

                                    <td className="border px-4 py-2">
                                        {productInfo?.has_serials ? (
                                            addedProduct.serials?.length === addedProduct.qty ? (
                                                <span>Serials Selected</span>
                                            ) : (
                                                <button
                                                    type="button"
                                                    className="bg-[#E3E9FE] text-[#1F2F70] p-1 rounded-md hover:text-green-700 flex items-center space-x-1"
                                                    onClick={() => handleOpenModal(addedProduct)}
                                                >
                                                    <Icon icon="mdi:plus" className="w-5 h-5" />
                                                    <span>Select Serial</span>
                                                </button>
                                            )
                                        ) : (
                                            // <div className='p-1 bg-gray-200 rounded-md'>Non Serial Product</div>
                                            <div className='p-1 bg-gray-200 rounded-md text-center'>N/A</div>
                                        )}
                                    </td>
                                    <td className="border px-4 py-2">
                                        <div className='flex space-x-2 items-center'>
                                            <button type="button" className="text-blue-500 hover:text-blue-700" onClick={() => handleEditProduct(index)}>
                                                <Icon icon="mdi:pencil" className="w-5 h-5" />
                                            </button>
                                            <button type="button" onClick={() => handleDeleteProduct(index)} className="text-red-500 hover:text-red-700">
                                                <Icon icon="mdi:trash-can" className="w-5 h-5" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            );
                        })
                    )}
                </tbody>
            </table>

            {isModalOpen && selectedProduct && (
                <Modal
                    activeModal={isModalOpen}
                    onClose={handleCloseModal}
                    title="Select Serial"
                    className='max-w-4xl'
                >
                    <Formik
                        key={selectedProduct.product_id}
                        initialValues={{
                            serials: serialsInitialValues,
                            selectedSerialIds: []
                        }}
                        onSubmit={(values) => {
                            if (values.selectedSerialIds.length !== selectedProduct.qty) {
                                toast.error(`Please select ${selectedProduct.qty} serials.`);
                                return;
                            }

                            handleUploadSerial(selectedProduct.product_id, values.selectedSerialIds);
                            handleCloseModal();
                        }}
                        enableReinitialize={true}
                    >
                        {({ values, handleChange, setFieldValue }) => (
                            <Form>
                                <div className="p-4">
                                    <div className='w-full p-3 bg-gray-100 rounded-lg mb-5'>
                                        <h3 className="text-lg font-bold mb-2">
                                            Product Name
                                        </h3>
                                        <p>{selectedProduct.product_name}</p>
                                    </div>
                                    <div className='grid grid-cols-2'>
                                        <div className='col-span-1'>
                                            <h3 className="text-lg font-bold mb-2">
                                                Serial Select
                                            </h3>
                                            {allSerials?.map((serial, index) => (
                                                <div key={index} className="bg-gray-100 rounded-md p-2 flex items-center space-x-4 mr-5 mb-3">
                                                    <input
                                                        className='w-4 h-4'
                                                        type="checkbox"
                                                        name="selectedSerialIds"
                                                        value={serial.id}
                                                        checked={values.selectedSerialIds.includes(serial.id)}
                                                        onChange={(e) => {
                                                            if (e.target.checked) {
                                                                setFieldValue('selectedSerialIds', [...values.selectedSerialIds, serial.id]);
                                                            } else {
                                                                setFieldValue('selectedSerialIds', values.selectedSerialIds.filter(s => s !== serial.id));
                                                            }
                                                        }}
                                                    />
                                                    <label>{serial.serial_number}</label>
                                                </div>
                                            ))}
                                        </div>

                                        <div className='col-span-1'>
                                            <h3 className="text-lg font-bold mb-2">Selected Serials:</h3>
                                            {values.selectedSerialIds.length === 0 ? (
                                                <p>No serials selected</p>
                                            ) : (
                                                values?.selectedSerialIds?.map((serialId, index) => {
                                                    const serial = allSerials?.find(s => s.id === serialId);
                                                    return (
                                                        <div key={index} className="bg-gray-100 rounded-md p-2 flex items-center justify-between space-x-2 mb-3">
                                                            <Badge className='bg-green-200'>
                                                                <span>{serial?.serial_number}</span>
                                                            </Badge>
                                                            <button
                                                                type="button"
                                                                onClick={() => {
                                                                    setFieldValue('selectedSerialIds', values.selectedSerialIds.filter(s => s !== serialId));
                                                                }}
                                                                className="text-red-500 hover:text-red-700"
                                                            >
                                                                <Icon icon="mdi:trash-can" className="w-5 h-5" />
                                                            </button>
                                                        </div>
                                                    );
                                                })
                                            )}
                                            <div className="mt-4">
                                                {values.selectedSerialIds.length !== selectedProduct.qty ? (
                                                    <div className="text-red-500 font-bold text-center">Please select {selectedProduct.qty} serials.</div>
                                                ) : (
                                                    <div className='flex justify-end'>
                                                        <button
                                                            type="submit"
                                                            className="bg-blue-600 text-white py-2 px-4 rounded-md"
                                                        >
                                                            Add as Selected
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </Form>
                        )}
                    </Formik>
                </Modal>
            )}
        </div>
    );
};

export default AddedProductsTable;
