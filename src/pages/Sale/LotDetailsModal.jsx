import React, { useState } from 'react';
import Modal from '@/components/ui/Modal';
import { useGetApiQuery } from '@/store/api/apihandler/commonSlice';
import { Icon } from '@iconify/react';
import Pagination from '@/components/partials/common-table/pagination';
import TableSkeleton from '@/components/ui/TableSkeleton';

const DetailCard = ({ icon, title, content, iconColor, colSpan }) => (
    <div className={`flex items-center space-x-3 ${colSpan}`}>
        <Icon icon={icon} className={`text-2xl ${iconColor}`} />
        <div>
            <h3 className="text-lg font-semibold">{title}</h3>
            <p className="text-gray-700">{content}</p>
        </div>
    </div>
);

const LotDetailsModal = ({ isOpen, onClose, modalData }) => {
    const [apiParam, setApiParam] = useState(0);
    const [soldFilter, setSoldFilter] = useState(0);
    const [availableFilter, setAvailableFilter] = useState(1);

    const { data, error, isLoading } = useGetApiQuery({
        url: 'inventory-lot-details',
        params: { invoice_number: modalData?.invoiceNumber, product_id: modalData?.productId, page: apiParam, sold: soldFilter, available: availableFilter },
    });

    const handleFilterChange = setter => e => setter(e.target.value);

    if (error) return <div className="p-8 text-center text-red-600">Error loading data. Please try again later.</div>;

    const { serials, sold_qty, available_qty, pagination } = data || {};
    const columns = ['Serial Number', 'Status'];

    return (
        <Modal activeModal={isOpen} onClose={onClose} title="Lot Details" className="max-w-4xl">
            <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <DetailCard icon="mdi:package-variant" iconColor="text-green-500" title="Sold Quantity" content={`${sold_qty} pcs`} colSpan="md:col-span-1" />
                <DetailCard icon="mdi:package-variant-closed" iconColor="text-yellow-500" title="Available Quantity" content={`${available_qty} pcs`} colSpan="md:col-span-1" />

                <div className="md:col-span-2">
                    <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center space-x-3">
                            <Icon icon="mdi:barcode" className="text-2xl text-blue-500" />
                            <h3 className="text-lg font-semibold">Serial Numbers</h3>
                        </div>
                        <div className="flex space-x-3">
                            {['sold', 'available'].map(filter => (
                                <div key={filter} className="flex items-center space-x-2">
                                    <label htmlFor={`${filter}-filter`} className="text-sm capitalize">{filter}</label>
                                    <select
                                        id={`${filter}-filter`}
                                        className="border p-1 rounded-md focus:outline-none focus:ring-0"
                                        value={filter === 'sold' ? soldFilter : availableFilter}
                                        onChange={handleFilterChange(filter === 'sold' ? setSoldFilter : setAvailableFilter)}
                                    >
                                        <option value={0}>Not {filter === 'sold' ? 'Sold' : 'Available'}</option>
                                        <option value={1}>{filter === 'sold' ? 'Sold' : 'Available'}</option>
                                    </select>
                                </div>
                            ))}
                        </div>
                    </div>

                    {isLoading ? <TableSkeleton columns={columns} /> : (
                        <table className="w-full border-collapse">
                            <thead>
                                <tr>
                                    <th className="border p-2 text-left">Serial Number</th>
                                    <th className="border p-2 text-left">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {serials?.length > 0 ? (
                                    serials.map((serial, index) => (
                                        <tr key={index}>
                                            <td className="border p-2">{serial?.serial_number}</td>
                                            <td className="border p-2 flex items-center space-x-2">
                                                <Icon icon={serial?.sold ? "mdi:check-circle" : "mdi:present-outline"} className={`text-${serial?.sold ? 'green' : 'yellow'}-500`} />
                                                <span>{serial?.sold ? 'Sold' : 'Available'}</span>
                                            </td>
                                        </tr>
                                    ))
                                ) : (
                                    <tr>
                                        <td colSpan="2" className="border p-2 text-center text-gray-500">No data available</td>
                                    </tr>
                                )}
                            </tbody>
                        </table>
                    )}

                    <Pagination
                        totalPages={Math.ceil(pagination?.total / pagination?.per_page)}
                        currentPage={pagination?.current_page}
                        handlePageChange={setApiParam}
                    />
                </div>
            </div>
        </Modal>
    );
};

export default LotDetailsModal;
