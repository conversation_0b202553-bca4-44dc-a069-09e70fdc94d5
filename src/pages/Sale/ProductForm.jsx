import Button from '@/components/ui/Button';
import InputField from '@/components/ui/form/InputField';
import InputSelect from '@/components/ui/form/InputSelect';
import Switch from '@/components/ui/Switch';
import { useGetApiQuery, useGetApiWithIdQuery } from '@/store/api/apihandler/commonSlice';
import { Icon } from '@iconify/react';
import { ErrorMessage, FieldArray, Form } from 'formik';
import React, { useEffect } from 'react';

const ProductForm = ({ values = {}, touched, errors, setFieldValue }) => {
    console.log("values", values, errors);
    const relatedProducts = useGetApiQuery(
        {
            url: `related-products?product_id=${values?.product_id}`,
            params: { pagination: 0, is_active: 1 },
        },
        { skip: !values?.product_id }
    );

    const filteredProduct = useGetApiQuery({
        url: "products",
        params: {
            pagination: 0,
            is_active: 1,
            ...Object.fromEntries(
                Object.entries({
                    brand_id: values?.brand_id || undefined,
                    category_id: values?.category_id || undefined,
                    sub_category_id: values?.sub_category_id || undefined
                }).filter(([_, v]) => v !== undefined && v !== "")
            )
        }
    });

    const { data: product } = useGetApiWithIdQuery(
        values?.product_id ? ["inventory-check", values?.product_id] : [],
        { skip: !values?.product_id }
    );

    const models = useGetApiQuery(
        { url: "models", params: { pagination: 0, is_active: 1, parent_id: values?.category_id } },
        { skip: !values?.category_id }
    );

    const allCategories = useGetApiQuery({
        url: "categories",
        params: { pagination: 0, is_active: 1 }
    });

    const allBrands = useGetApiQuery({
        url: "brands",
        params: { pagination: 0, is_active: 1 }
    });

    useEffect(() => {
        if (relatedProducts?.data?.length > 0) {
            const initialRelatedProducts = relatedProducts.data.map(product => ({
                id: product.id,
                qty: 1
            }));
            setFieldValue('related_products', initialRelatedProducts);
        }
    }, [relatedProducts.data, setFieldValue]);

    const brandOptions = allBrands?.data?.map((brand) => ({
        label: brand?.name,
        value: brand?.id,
        image: brand?.image
    }));

    const categories = allCategories?.data?.map((category) => ({
        label: category.name,
        value: category.id,
        image: category?.image
    }));

    const modelsOptions = models?.data?.map((model) => ({
        label: model?.name,
        value: model?.id,
        image: model?.image
    }));

    const filteredOptions = filteredProduct?.data?.map((product) => ({
        label: product?.name,
        value: product.id,
        image: product?.image
    }));

    const specificProduct = filteredProduct?.data?.find((product) => product?.id === values?.product_id);

    const handleRelatedProductQtyChange = (index, value, arrayHelpers) => {
        const newQty = parseInt(value) || 0;
        if (newQty >= 0) {
            const updatedProducts = [...(values.related_products || [])];
            if (updatedProducts[index]) {
                updatedProducts[index] = {
                    ...updatedProducts[index],
                    qty: newQty
                };
                setFieldValue('related_products', updatedProducts);
            }
        }
    };

    return (
        <div>
            <Form>
                <div className='grid grid-cols-3 gap-4'>
                    <div className='col-span-3 lg:col-span-1'>
                        <h6 className='mb-2'>Filter Your Product (optional)</h6>
                        <div className='grid grid-cols-1 gap-4 p-5 mb-2 bg-[#E9E9E961] border border-[#E6E6E6] rounded-lg'>
                            <div className='flex items-center'>
                                <div className="w-full">
                                    <div className='flex justify-between'>
                                        <label className="block text-gray-700 text-base font-medium">Select Brand</label>
                                    </div>
                                    <InputSelect
                                        name="brand_id"
                                        options={brandOptions}
                                        placeholder="Select a Brand"
                                        className="w-[100%]"
                                        isClearable={true}
                                    />
                                </div>
                            </div>
                            <div className="flex items-center my-4">
                                <hr className="flex-grow border-t border-slate-300 dark:border-slate-600" />
                                <span className="mx-2 text-slate-500 dark:text-slate-400">or</span>
                                <hr className="flex-grow border-t border-slate-300 dark:border-slate-600" />
                            </div>

                            <div className='flex items-center'>
                                <div className="w-full">
                                    <div className='flex justify-between'>
                                        <label className="block text-gray-700 text-base font-medium">Select Category</label>
                                    </div>
                                    <InputSelect
                                        name="category_id"
                                        options={categories}
                                        placeholder="Select a category"
                                        className="w-[100%]"
                                        isClearable={true}
                                    />
                                </div>
                            </div>
                            <div className='flex items-center'>
                                <div className="w-full">
                                    <div className='flex justify-between'>
                                        <label className="block text-gray-700 text-base font-medium">Select Model</label>
                                    </div>
                                    <InputSelect
                                        name="sub_category_id"
                                        options={modelsOptions}
                                        placeholder="Select a Model"
                                        className="w-[100%]"
                                        isClearable={true}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className='col-span-3 lg:col-span-2 bg-[#E9E9E961] border border-[#E6E6E6] rounded-lg p-5 mt-7'>
                        <div className="grid grid-cols-1 gap-4 mb-4">
                            <div className='flex items-center'>
                                <div className="w-full">
                                    <div className='flex justify-between'>
                                        <label className="block text-gray-700 text-base font-medium">
                                            Select Product
                                            <span className='text-red-500'>*</span>
                                        </label>
                                        <p className='text-blue-800 text-md'>
                                            {product?.qry} {product?.qry ? "remaining" : ""}
                                        </p>
                                    </div>
                                    <InputSelect
                                        name="product_id"
                                        options={filteredOptions}
                                        placeholder="Select a product"
                                        className="w-[100%]"
                                    />
                                    {touched.product_id && errors.product_id && (
                                        <div className="text-red-500 text-xs mt-2">{errors.product_id}</div>
                                    )}
                                </div>
                            </div>
                            <InputField
                                label="Unit Price"
                                name="unit_price"
                                type="number"
                                placeholder="Enter unit price"
                                required
                            />
                            <div>
                                <InputField
                                    label="Quantity"
                                    name="qty"
                                    type="number"
                                    placeholder="Enter Quantity"
                                    required
                                />
                                {product?.qry < values?.qty && (
                                    <p className='text-red-500 text-xs mt-2'>
                                        Quantity should be less than {product?.qry}
                                    </p>
                                )}
                            </div>
                            <div className='mt-10 flex gap-5'>
                                <div>
                                    <Switch
                                        label="Warranty Shown In Invoice"
                                        activeClass="bg-success-500"
                                        name="warranty_value"
                                        value={values?.warranty_value}
                                        onChange={() => {
                                            const newWarrantyValue = !values.warranty_value;

                                            if (!newWarrantyValue) {
                                                // Clear both warranty and guaranty fields when Warranty switch is off
                                                setFieldValue("warranty_period", "");
                                                setFieldValue("guarantee_period", "");
                                            }

                                            if (!newWarrantyValue && values.enable_guaranty) {
                                                setFieldValue("enable_guaranty", false);
                                            }

                                            setFieldValue("warranty_value", newWarrantyValue);
                                        }}
                                    />

                                </div>
                                <div>
                                    <Switch
                                        label="Enable Guarantee"
                                        activeClass="bg-success-500"
                                        name="enable_guaranty"
                                        value={values?.enable_guaranty}
                                        onChange={() => {
                                            const newGuarantyValue = !values.enable_guaranty;

                                            if (!newGuarantyValue) {
                                                // Clear only the guaranty field when Guaranty switch is off
                                                setFieldValue("guarantee_period", "");
                                            }

                                            if (newGuarantyValue) {
                                                setFieldValue("warranty_value", true);
                                            }

                                            setFieldValue("enable_guaranty", newGuarantyValue);
                                        }}
                                    />

                                </div>
                            </div>

                            {values?.warranty_value &&
                                <>
                                    <InputSelect
                                        label={"Warranty Type"}
                                        name="warranty_type"
                                        options={[
                                            { label: "Years", value: "years" },
                                            { label: "Months", value: "months" },]}
                                    />
                                    {/* <InputField
                                        label="Warranty Period"
                                        name="warranty_period"
                                        type="text"
                                        placeholder="Enter Warranty Period"
                                        required
                                    /> */}
                                    <div className="flex flex-col">
                                        <div className="flex items-center space-x-2">
                                            <label htmlFor="warranty_period" className="text-sm whitespace-nowrap">
                                                Buyer will get
                                            </label>
                                            <div className="flex flex-col items-start">
                                                <input
                                                    id="warranty_period"
                                                    name="warranty_period"
                                                    type="number"
                                                    placeholder="Warranty"
                                                    className="border rounded px-2 py-1 text-sm w-24 focus:outline-none focus:ring-2 focus:ring-blue-300"
                                                    min="1"
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        if (value === '' || (Number(value) > 0 && Number.isInteger(Number(value)))) {
                                                            setFieldValue("warranty_period", value);
                                                        }
                                                    }}
                                                    onBlur={(e) => {
                                                        const value = e.target.value;
                                                        if (value === '' || (Number(value) > 0 && Number.isInteger(Number(value)))) {
                                                            setFieldValue("warranty_period", value);
                                                        }
                                                    }}
                                                />
                                                <div className="text-red-500 text-xs">
                                                    {touched.warranty_period && errors.warranty_period ? errors.warranty_period : ''}
                                                </div>
                                            </div>
                                            <span className="text-sm whitespace-nowrap">
                                                {values?.warranty_type} warranty
                                            </span>
                                            {values?.warranty_value && values?.enable_guaranty && (
                                                <div className="flex flex-col items-start">
                                                    <input
                                                        id="guarantee_period"
                                                        name="guarantee_period"
                                                        type="number"
                                                        placeholder="Guarantee"
                                                        className="border rounded px-2 py-1 text-sm w-24 focus:outline-none focus:ring-2 focus:ring-blue-300"
                                                        min="1"
                                                        onChange={(e) => {
                                                            const value = e.target.value;
                                                            if (value === '' || (Number(value) > 0 && Number.isInteger(Number(value)))) {
                                                                setFieldValue("guarantee_period", value);
                                                            }
                                                        }}
                                                        onBlur={(e) => {
                                                            const value = e.target.value;
                                                            if (value === '' || (Number(value) > 0 && Number.isInteger(Number(value)))) {
                                                                setFieldValue("guarantee_period", value);
                                                            }
                                                        }}
                                                    />
                                                    {/* Validation message */}
                                                    <div className="text-red-500 text-xs mt-1">
                                                        {touched.guarantee_period && errors.guarantee_period ? errors.guarantee_period : ''}
                                                    </div>
                                                </div>
                                            )}
                                            {values?.enable_guaranty && <span className="text-sm whitespace-nowrap">
                                                and {values?.warranty_type} guarantee
                                            </span>}
                                        </div>
                                    </div>

                                    <InputField
                                        label="Warranty Note"
                                        name="warranty_period_value"
                                        type="string"
                                        placeholder="Ex: Keep the product in original condition"
                                    />
                                </>
                            }
                        </div>
                    </div>
                </div>
                {specificProduct?.has_related_products && (
                    <div className="mt-10">
                        <h6 className="text-lg font-bold text-gray-800 mb-4">Related Products</h6>
                        {relatedProducts?.data?.length > 0 ? (
                            <FieldArray
                                name="related_products"
                                render={arrayHelpers => (
                                    <div className="grid grid-cols-1 gap-6">
                                        {relatedProducts?.data?.map((item, index) => {
                                            const relatedProductIndex = values?.related_products?.findIndex(
                                                product => product.id === item.id
                                            );
                                            const isChecked = relatedProductIndex !== -1;

                                            return (
                                                <div
                                                    key={index}
                                                    className="flex items-center p-4 bg-gray-100 border shadow-lg rounded-lg hover:shadow-xl transition-shadow"
                                                >
                                                    <div className="mr-4 ml-3">
                                                        <input
                                                            type="checkbox"
                                                            className="w-4 h-4 rounded border-gray-300 text-blue-500 focus:ring-blue-400"
                                                            checked={isChecked}
                                                            onChange={(e) => {
                                                                if (e.target.checked) {
                                                                    arrayHelpers.push({
                                                                        id: item.id,
                                                                        qty: 1
                                                                    });
                                                                } else {
                                                                    const idx = values.related_products?.findIndex(
                                                                        p => p.id === item.id
                                                                    );
                                                                    if (idx !== -1) {
                                                                        arrayHelpers.remove(idx);
                                                                    }
                                                                }
                                                            }}
                                                        />
                                                    </div>

                                                    <div className="w-12 h-12 rounded-md overflow-hidden border border-gray-300 bg-gray-100 mr-4">
                                                        {item?.image ? (
                                                            <img
                                                                src={`${import.meta.env.VITE_MEDIA_URL}/${item?.image}`}
                                                                alt={item?.name || "Default Image"}
                                                                className="w-full h-full object-cover"
                                                            />
                                                        ) : (
                                                            <div className="flex items-center justify-center w-full h-full bg-gray-200">
                                                                <Icon
                                                                    icon="mdi:package-variant-closed"
                                                                    className="text-gray-500 text-4xl"
                                                                />
                                                            </div>
                                                        )}
                                                    </div>

                                                    <div className="flex-grow">
                                                        <p className="text-gray-800 font-semibold whitespace-normal break-words">
                                                            {item?.name}
                                                        </p>
                                                    </div>

                                                    <div className="ml-4 mr-4 w-32">
                                                        {isChecked && (
                                                            <input
                                                                type="number"
                                                                min="0"
                                                                value={values.related_products[relatedProductIndex]?.qty || ''}
                                                                onChange={(e) => {
                                                                    // Extract and sanitize the input
                                                                    let newValue = e.target.value.replace(/^0+(?!$)/, ''); // Remove leading zeros except for "0"
                                                                    newValue = newValue === '' ? 0 : parseInt(newValue, 10); // Convert to integer, default to 0

                                                                    // Update the quantity using the sanitized value
                                                                    handleRelatedProductQtyChange(relatedProductIndex, newValue, arrayHelpers);
                                                                }}
                                                                className="w-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                                placeholder="Qty"
                                                            />
                                                        )}
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                )}
                            />
                        ) : (
                            <div className="flex flex-col items-center justify-center mt-6 p-6 bg-gray-50 border border-gray-200 shadow-lg rounded-lg">
                                <Icon
                                    icon="mdi:package-variant-closed"
                                    className="w-14 h-14 text-gray-400 mb-4"
                                />
                                <p className="text-gray-700 text-base font-medium">
                                    No related products added.
                                </p>
                            </div>
                        )}
                    </div>
                )}

                <div className='flex justify-end'>
                    <Button
                        disabled={product?.qry < values?.qty}
                        type="submit"
                        className="bg-blue-800 text-white mt-5 px-4 py-2 rounded-md"
                    >
                        <div className="w-6 h-6 flex items-center justify-center rounded-full">
                            <Icon icon="formkit:add" className='w-8 h-8 mr-1' />
                        </div>
                        <span>Add Product</span>
                    </Button>
                </div>
            </Form>
        </div>
    );
};

export default ProductForm;