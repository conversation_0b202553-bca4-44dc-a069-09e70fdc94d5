import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { formattedDate } from "@/constant/data";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

const SalesList = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery({ url: "sales", params: apiParam }); 
  const navigate = useNavigate();

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Sl", field: "index" },
    { label: "Invoice Date", field: "invoice_date" },
    { label: "Customer Name", field: "client_name" },
    { label: "Invoice Number", field: "invoice_no" },
    { label: "Amount", field: "amount" },
    { label: "Paid Amount", field: "paid_amount" },
    { label: "Payment Status", field: "payment_status" },
    { label: "Due Amount", field: "due_amount" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "See Invoice",
      icon: "heroicons-outline:eye",
      onClick: (val) => {
        window.open(`/see-invoice/${data?.data[val]?.id}`, "_blank");
      },
    },
    {
      name: "See Challan",
      icon: "mdi:invoice",
      onClick: (val) => {
        window.open(`/see-challan/${data?.data[val]?.id}`, "_blank");
      },
    },
    {
      name: "Licenses",
      icon: "carbon:license-maintenance",
      onClick: (value) => {
        navigate(`/licensing/${data?.data[value]?.id}`);
      },
    },
    {
      name: "Accounts",
      icon: "arcticons:home-finance",
      onClick: (val) => {
        navigate(`/sales-accounts/${data?.data[val]?.id}`);
      },
    },
    {
      name: "Support",
      icon: "material-symbols-light:assignment-add",
      onClick: (val) => {
        navigate(`/support-ticket/${data?.data[val]?.id}`);
      },
    },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      index: index + 1,
      invoice_date: formattedDate(item.invoice_date),
      client_name: item.client?.name,
      vendor_id: item.vendor?.name,
      invoice_no: item.invoice_no,
      amount: (
        <div>
          <div className="text-sm mb-1">Sub Total : {item.sub_total} Tk</div>
          <div className="text-sm">Grand Total : {item.grand_total} Tk</div>
        </div>
      ),
      paid_amount: item.paid_amount,
      due_amount: item.due_amount,
      payment_status: (
        <Badge
          className={
            item.payment_status === "Paid"
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.payment_status === "Paid" ? "Paid" : "Due"}
        </Badge>
      ),
    };
  });

  console.log(tableData)

  return (
    <div>
      <BasicTablePage
        title="Sales Invoice List"
        columns={columns}
        actions={actions}
        goto={"Generate Invoice"}
        gotoLink={"/store-sale"}
        changePage={changePage}
        data={tableData}
        filter={filter}
        setFilter={setApiParam}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
        loading={isLoading || isFetching}
      />
    </div>
  );
};

export default SalesList;
