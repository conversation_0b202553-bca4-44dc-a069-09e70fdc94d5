import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { formattedDate } from "@/constant/data";

const ProductWiseSalesList = () => {
    const [apiParam, setApiParam] = useState(0);
    const [filter, setFilter] = useState("");
    const { data, isLoading, isFetching } = useGetApiQuery({ url: "sales-serial-products", params: apiParam });
    const navigate = useNavigate();

    const changePage = (value) => {
        setApiParam(value);
    };

    const columns = [
        { label: "Sl", field: "index" },
        { label: "Customer Name & Phone", field: "client_name_with_phone" },
        { label: "Invoice Number", field: "invoice_no" },
        { label: "Product Name & Serials", field: "product_name_with_serial" },
        { label: "Invoice Date", field: "invoice_date" },
        { label: "Payment Status", field: "payment_status" },
        { label: "Action", field: "" },
    ];

    const actions = [
        {
            name: "See Invoice",
            icon: "heroicons-outline:eye",
            onClick: (val) => {
                window.open(`/see-invoice/${data?.data[val]?.id}`, '_blank');
            },
        },
        {
            name: "See Challan",
            icon: "mdi:invoice",
            onClick: (val) => {
                window.open(`/see-challan/${data?.data[val]?.id}`, '_blank');
            },
        },
        {
            name: "Licenses",
            icon: "carbon:license-maintenance",
            onClick: (value) => {
                navigate(`/licensing/${data?.data[value]?.id}`);
            },
        },
        {
            name: "Accounts",
            icon: "arcticons:home-finance",
            onClick: (val) => {
                navigate(`/sales-accounts/${data?.data[val]?.id}`);
            },
        },
        {
            name: "Support",
            icon: "material-symbols-light:assignment-add",
            onClick: (val) => {
                navigate(`/support-ticket/${data?.data[val]?.id}`);
            },
        },
    ];

    const tableData = data?.data?.map((item, index) => {
        return {
            index: index + 1,
            invoice_date: formattedDate(item?.invoice_date),
            client_name_with_phone: (<div>
                <p>{item.client?.name}</p>
                <p className="mt-1">{item?.client?.phone}</p>
            </div>),
            invoice_no: item.invoice_no,
            product_name_with_serial: (
                <div className="product-info">
                    <label className="block text-sm font-medium text-gray-700">{item?.product_name}</label>
                    <div className="mt-1 text-sm text-gray-600 space-y-1">
                        {
                            item?.product_serial_numbers?.split(',')
                                .map((serial, index) => (
                                    <div key={index}>{serial.trim()}</div>
                                ))
                        }
                    </div>
                </div>
            ),
            payment_status: (
                <Badge
                    className={
                        item.payment_status === "Paid"
                            ? `bg-success-500 text-white`
                            : `bg-danger-500 text-white`
                    }
                >
                    {" "}
                    {item.payment_status === "Paid" ? "Paid" : "Due"}
                </Badge>
            ),
        };
    });

    return (
        <div>
            <BasicTablePage
                loading={isLoading || isFetching}
                title="Sale list by serial"
                columns={columns}
                actions={actions}
                goto={"Generate Invoice"}
                gotoLink={"/store-sale"}
                changePage={changePage}
                data={tableData}
                filter={filter}
                setFilter={setApiParam}
                currentPage={data?.current_page}
                totalPages={Math.ceil(data?.total / data?.per_page)}
            />
        </div>
    );
};

export default ProductWiseSalesList;
