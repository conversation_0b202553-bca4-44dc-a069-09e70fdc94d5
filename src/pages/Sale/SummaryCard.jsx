import React from 'react';
import InputSelect from '@/components/ui/form/InputSelect';
import InputField from '@/components/ui/form/InputField';
import FileUpload from '@/components/ui/form/FileUpload';
import TextAreaField from '@/components/ui/form/TextAreaField';
import Card from '@/components/ui/Card';

const paymentMethodOptions = [
    { label: 'Cash', value: 'Cash' },
    { label: 'Bank Transfer', value: 'BankTransfer' },
    { label: 'Cheque', value: 'Cheque' },
    { label: 'Online Payment', value: 'OnlinePayment' },
    { label: 'Bkash', value: 'Bkash' },
    { label: 'Ucash', value: 'Ucash' },
    { label: 'Nagad', value: 'Nagad' },
    { label: 'Rocket', value: 'Rocket' },
    { label: 'Mobile Banking', value: 'MobileBanking' },
    { label: 'Agent Banking', value: 'AgentBanking' },
    { label: 'Others', value: 'Others' },
];

const SummaryCard = ({
    values,
    setFieldValue,
    subtotal,
    discount,
    netPrice,
    handleDiscountChange,
    allProducts,
    errors,
    touched
}) => {
    return (
        <Card
            title="Summary"
            noborder={true}
            className=""
            titleClass="text-lg font-bold text-gray-800"
        >
            <div className='border-b border-gray-200'>
                <div className='flex justify-between mb-3'>
                    <div>
                        <p>Total Amount</p>
                    </div>
                    <div>
                        <p>৳{subtotal.toFixed(2)}</p>
                    </div>
                </div>
                <div className='flex justify-between mt-2 mb-4'>
                    <div className="w-1/2">
                        <p>Special Discount</p>
                    </div>
                    <div className="w-1/2">
                        <InputField
                            name="discount_price"
                            type='text'
                            placeholder="Enter discount"
                            value={discount}
                            onChange={handleDiscountChange}
                            className="w-full"
                        />
                    </div>
                </div>
            </div>

            <div className='border-b border-gray-200'>
                <div className='flex justify-between mt-2 mb-4'>
                    <div className="w-1/2">
                        <p>Payment Method</p>
                    </div>
                    <div className="w-1/2">
                        <InputSelect
                            name="payment_method"
                            options={paymentMethodOptions}
                            placeholder="Select Payment Method"
                            className="w-full"
                        />
                        {touched.payment_method && errors.payment_method && (
                            <div className="text-red-500 text-xs mt-2">{errors.payment_method}</div>
                        )}
                    </div>
                </div>
                {(values.payment_method !== 'Cash') && (
                    <div className='flex justify-between mt-2'>
                        <div className="w-1/2">
                            <p>Transaction ID</p>
                        </div>
                        <div className="w-1/2">
                            <InputField
                                name="transaction_id"
                                type='text'
                                placeholder="Transaction ID"
                                className="w-full"
                                required
                            />
                        </div>
                    </div>
                )}
                <div className='flex justify-between mt-2'>
                    <div className="w-1/2">
                        <p>Remarks</p>
                    </div>
                    <div className="w-1/2">
                        <TextAreaField
                            name="description"
                            type="text"
                            placeholder="Remarks"
                            rows={2}
                        />
                    </div>
                </div>
            </div>
            <div className='border-b border-gray-200'>
                <div className='flex justify-between mt-4'>
                    <div className="w-1/2">
                        <p>Attachment</p>
                    </div>
                    <div className="w-1/2">
                        <FileUpload
                            name="attachment"
                            label="Upload"
                            endpoint="file-upload"
                            valueKey="file"
                            accept="image/*,application/pdf"
                            showFileLink
                            required
                        />
                    </div>
                </div>
                <div className='flex justify-between mt-2 mb-4'>
                    <div className="w-1/2">
                        <InputSelect
                            name="vat"
                            options={[
                                { value: 'Without VAT & TAX', label: 'Without VAT & TAX' },
                                { value: 'With VAT & TAX', label: 'With VAT & TAX' },
                            ]}
                            placeholder="Select Vat Type"
                            className="w-full"
                        />
                        <div className="mt-2">
                            <input
                                type="checkbox"
                                id="is_vat_shown"
                                name="is_vat_shown"
                                checked={values.is_vat_shown === 1}
                                onChange={(e) => setFieldValue("is_vat_shown", e.target.checked ? 1 : 0)}
                                className="mr-2"
                            />
                            <label htmlFor="is_vat_shown" className="text-gray-500">Show in the invoice</label>
                        </div>
                    </div>
                    <div className="w-1/2 flex justify-end items-center">
                        <div>
                            <p>৳{(netPrice - (parseFloat(values.discount_price) || 0)).toFixed(2)}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div className='flex justify-between mt-4'>
                <div className="w-1/2">
                    <p>Advance Paid</p>
                </div>
                <div className="w-1/2">
                    <InputField
                        name="paid_amount"
                        type='number'
                        placeholder="Paid Amount"
                        className="w-full"
                    />
                </div>
            </div>
            <div className='flex justify-between mt-2'>
                <div>
                    <p>Payable Due Amount</p>
                </div>
                <div>
                    <p>৳{(netPrice - (parseFloat(values.paid_amount) || 0)).toFixed(2)}</p>
                </div>
            </div>
        </Card>
    );
};

export default SummaryCard;
