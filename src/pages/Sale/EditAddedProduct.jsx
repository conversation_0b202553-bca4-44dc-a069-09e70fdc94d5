import React from 'react';
import { Formik, Form } from 'formik';
import InputSelect from '@/components/ui/form/InputSelect';
import InputField from '@/components/ui/form/InputField';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import * as Yup from 'yup';
import EditAddedProductForm from './EditAddedProductForm';

const EditAddedProduct = ({ isOpen, onClose, product, productOptions, onSubmit }) => {
    const warrantyOptions = [
        { label: 'years', value: 1 },
        { label: 'months', value: 0 }
    ];

    // const validationSchema = Yup.object().shape({
    //     product_id: Yup.string().required('Product is required'),
    //     unit_price: Yup.number().min(1, 'Unit price must be at least 1').required('Unit price is required'),
    //     qty: Yup.number()
    //         .min(1, 'Quantity must be at least 1')
    //         .required('Quantity is required'),
    // });

    const getWarrantyValue = (warranty_period) => {
        return warranty_period === 'years' ? 1 : 0;
    };

    return (
        <Modal activeModal={isOpen} onClose={onClose} title="Edit Product" className='max-w-4xl'>
            <Formik
                initialValues={{
                    product_id: product?.product_id,
                    unit_price: product?.unit_price,
                    qty: product?.qty,
                    warranty_period: product?.warranty_period,
                    warranty_type: product?.warranty_type,
                    guarantee_period: product?.guarantee_period,
                    warranty_period_value: product?.warranty_period_value,
                }}
                // validationSchema={validationSchema}
                onSubmit={(values) => {
                    const updatedValues = {
                        ...values,
                        // warranty_period: values.warranty_period === 1 ? 'years' : 'months'
                    };
                    onSubmit(updatedValues);
                }}
                enableReinitialize
            >
                {({ values, handleChange }) => (
                    <EditAddedProductForm
                        values={values}
                        handleChange={handleChange}
                        productOptions={productOptions}
                        warrantyOptions={warrantyOptions} />
                )}
            </Formik>
        </Modal>
    );
};

export default EditAddedProduct;
