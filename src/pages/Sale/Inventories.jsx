import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { formattedDate } from "@/constant/data";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import { Icon } from "@iconify/react";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
// import DeleteStock from "./DeleteStock";

const Inventories = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery({ url: "inventories", params: apiParam });
  const navigate = useNavigate();

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Sl", field: "sl" },
    { label: "Product", field: "productName" },
    { label: "Entry Date", field: "created_at" },
    { label: "Stock Quantity", field: "total_stock_qty" },
    { label: "Sale Quantity", field: "total_sale_qty" },
    { label: "Available Qty", field: "qty" },
    { label: "View Serials", field: "view" },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      sl: index + 1,
      productName: item?.product?.name,
      total_stock_qty: (
        <Badge className="bg-primary-500 px-2 py-[3px] text-sm font-semibold text-slate-100  capitalize">
          {item.total_stock_qty ? item.total_stock_qty : 0}
        </Badge>
      ),
      created_at: formattedDate(item.created_at),
      total_sale_qty: (
        <Badge className="bg-yellow-300 px-2 py-[3px] text-sm font-semibold text-black  capitalize">
          {item.total_sale_qty ? item.total_sale_qty : 0}
        </Badge>
      ),
      qty: (
        <Badge className="bg-green-500 px-2 py-[3px] text-sm font-semibold text-slate-100  capitalize">
          {item.qty}
        </Badge>
      ),
      view: (
        <>
          {item?.product?.has_serials ? (
            <button
              onClick={() =>
                navigate(`/product-serials-list/${item?.product_id}`)
              }
              className="cursor-pointer"
            >
              <button className="flex items-center">
                <Badge className="bg-slate-500 px-2 py-[3px] text-sm font-semibold text-slate-100 capitalize">
                  <Icon icon="heroicons-outline:eye" className="w-5 h-5 mr-2" />
                  View Serials
                </Badge>
              </button>
            </button>
          ) : (
            <Badge className="bg-red-300 px-2 py-[3px] text-sm font-semibold text-black capitalize">
              Non Serial Product
            </Badge>
          )}
        </>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        loading={isLoading || isFetching}
        title="Inventory List"
        columns={columns}
        changePage={changePage}
        data={tableData}
        filter={filter}
        setFilter={setApiParam}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
    </div>
  );
};

export default Inventories;
