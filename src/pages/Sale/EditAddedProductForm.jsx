import Button from '@/components/ui/Button';
import InputField from '@/components/ui/form/InputField';
import InputSelect from '@/components/ui/form/InputSelect';
import { useGetApiWithIdQuery } from '@/store/api/apihandler/commonSlice';
import { Form } from 'formik';
import React from 'react';

const EditAddedProductForm = ({ values, handleChange, productOptions }) => {
    console.log(values)
    const { data: product } = useGetApiWithIdQuery(
        values?.product_id ? ["inventory-check", values?.product_id] : [],
        { skip: !values?.product_id }
    );
    const { data: productInfo } = useGetApiWithIdQuery(
        values?.product_id ? ["products", values?.product_id] : [],
        { skip: !values?.product_id }
    );

    return (
        <Form>
            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label className="block text-sm font-medium text-gray-700">Product</label>
                    <div className='text-sm text-black bg-gray-200 rounded-md p-2 mt-3'>
                        {productInfo?.name}
                    </div>
                </div>
                <InputField
                    label="Unit Price"
                    name="unit_price"
                    type='number'
                    placeholder="Enter unit price"
                    value={values.unit_price}
                    onChange={handleChange}
                />
                <div>
                    <InputField
                        label="Quantity"
                        name="qty"
                        type='number'
                        placeholder="Enter Quantity"
                        value={values.qty}
                        onChange={handleChange}
                    />
                    {product?.qry < values?.qty &&
                        <p className='text-red-500 text-xs mt-2'>Quantity should be less than {product?.qry}</p>}
                </div>
                <InputSelect
                    label={"Warranty Type"}
                    name="warranty_type"
                    options={[
                        { label: "Years", value: "years" },
                        { label: "Months", value: "months" },]}
                />
                <InputField
                    label="Warranty Time"
                    name="warranty_period"
                    type='string'
                    placeholder="Warranty Time"
                    value={values.warranty_period}
                    onChange={handleChange}
                />
                <InputField
                    label="Guarantee Time"
                    name="guarantee_period"
                    type='string'
                    placeholder="Guarantee Time"
                    value={values.guarantee_period}
                    onChange={handleChange}
                />
            </div>
            <div className='flex justify-end mt-4'>
                <Button disabled={product?.qry < values?.qty} text="Update Product" type="submit" className="bg-blue-800 text-white" />
            </div>
        </Form>
    );
};

export default EditAddedProductForm;