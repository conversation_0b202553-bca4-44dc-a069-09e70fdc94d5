import React from "react";
import { Icon } from "@iconify/react";
import NoImage from "@/assets/CRM/NoImage.png";

const ClientInfo = ({ client }) => {
  const clientDetails = [
    { label: "Name", value: client?.name, icon: "mdi:account" },
    { label: "Phone", value: client?.phone, icon: "mdi:phone" },
    { label: "Email", value: client?.email, icon: "mdi:email" },
    { label: "Address", value: client?.address, icon: "mdi:map-marker" },
    {
      label: "Company",
      value: client?.company_name,
      icon: "mdi:office-building",
    },
    {
      label: "Customer Source",
      value: client?.customer_source,
      icon: "mdi:account-arrow-right",
    },
    {
      label: "Source Details",
      value: client?.source_details,
      icon: "mdi:information-outline",
    },
    {
      label: "Created At",
      value: client ? new Date(client?.created_at).toLocaleDateString() : "",
      icon: "mdi:calendar",
    },
  ];

  return (
    <div className="w-full">
      {/* Image section */}
      <div className="w-full flex justify-center p-3 bg-gray-100 border border-gray-400 rounded-md">
        <div className="h-[270px] w-[300px] flex items-center justify-center">
          <img
            // src={`${import.meta.env.VITE_MEDIA_URL}/${client?.image}`}
            src={
              client?.image
                ? `${import.meta.env.VITE_MEDIA_URL}/${client?.image}`
                : NoImage
            }
            alt=""
            className="max-w-full max-h-full rounded-md shadow-lg"
          />
        </div>
      </div>

      {/* Client details */}
      <div className="mt-6 grid grid-cols-1 gap-4">
        {clientDetails.map((detail, index) => (
          <div
            key={index}
            className="flex justify-between items-center border border-gray-200 bg-white shadow-md rounded-lg p-4 border-l-4 border-blue-500"
          >
            {/* Icon and Label together */}
            <div className="flex items-center">
              <Icon
                icon={detail.icon}
                width={24}
                className="text-blue-500 mr-2"
              />
              <span className="text-gray-600 text-sm font-semibold">
                {detail.label}
              </span>
            </div>
            {/* Value on the right */}
            <span className="text-gray-600 text-sm font-semibold">
              {detail.value || "N/A"}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ClientInfo;
