import Card from "@/components/ui/Card";
import InputSelect from "@/components/ui/form/InputSelect";
import {
  useGetApiQuery,
  useGetApiWithIdQuery,
  usePostApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import React from "react";
import ClientInfo from "./ClientInfo";
import SalesAccountDetails from "./SalesAccountDetails";
import { useNavigate, useParams } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import TextAreaField from "@/components/ui/form/TextAreaField";
import InputFile from "@/components/ui/form/InputFile";
import * as Yup from "yup";
import FileUpload from "@/components/ui/form/FileUpload";
import Badge from "@/components/ui/Badge";
import { Icon } from "@iconify/react";
import { toast } from "react-toastify";

const SalesAccount = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();
  const { data: saleInfo } = useGetApiWithIdQuery(["sales", id]);
  console.log(saleInfo?.data);

  const initialValues = {
    sale_id: id,
    client_id: saleInfo?.client_id,
    paid_amount: "",
    payment_method: "Bkash",
    attachment: null,
    transaction_id: "",
    description: "",
  };

  const validationSchema = Yup.object({
    client_id: Yup.string().required("Client is required"),
    paid_amount: Yup.number()
      .required("Payable amount is required")
      .positive("Amount must be positive")
      .max(
        saleInfo?.due_amount,
        `Amount cannot exceed the due amount of ${saleInfo?.due_amount}`
      ),
    payment_method: Yup.string().required("Payment method is required"),
    // attachment: Yup.mixed().required("Attachment is required"),
    // transaction_id: Yup.string().required("Transaction ID is required"),
    // description: Yup.string().required("Description is required"),
  });

  const handleSubmit = async (values, { resetForm }) => {
    try {
      const response = await postApi({
        end_point: "accounts",
        body: values,
      }).unwrap();

      // navigate("/sales-list");
      resetForm();
      toast.success("Payment Successfully");
    } catch (err) {
      console.error("Submission failed:", err);
    }
  };

  return (
    <div>
      {saleInfo?.payment_status === "Due" ? (
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, touched, errors }) => (
            <Form>
              {console.log(values, errors)}
              <div className="grid grid-cols-3 gap-4">
                <div className="col-span-2">
                  <Card
                    title="Collect Payment"
                    className="w-full"
                    titleClass="text-lg font-bold text-gray-800"
                    secondTitle={
                      <div className="flex items-center">
                        <p className="mr-2 flex items-center">
                          {saleInfo?.payment_status === "Due" && (
                            <Icon
                              icon="material-symbols:cancel-outline"
                              width={24}
                              className="text-red-500 mr-2"
                            />
                          )}
                          {saleInfo?.payment_status} :{" "}
                          <span className="ml-2 mt-1">
                            {saleInfo?.due_amount}
                          </span>
                        </p>
                      </div>
                    }
                  >
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex justify-between items-center p-5 border border-gray-400 bg-gray-300 text-black-500 rounded-md h-[40px]">
                        <p>Purchase Id : </p>
                        <p>{saleInfo?.id}</p>
                      </div>
                      <div className="flex justify-between items-center p-5 border border-gray-400 bg-red-200 text-black-500 rounded-md h-[40px]">
                        <p>Payment Status : </p>
                        <p>{saleInfo?.payment_status}</p>
                      </div>
                      <div>
                        <label className="flex justify-between">
                          <p className="block text-gray-800 text-base font-semibold mb-2">
                            Payable Amount :{" "}
                          </p>
                          <div className="flex gap-2">
                            <p className="block text-blue-800 text-base font-semibold mb-2">
                              {saleInfo?.due_amount}
                            </p>
                            <span className="text-red-500">*</span>
                          </div>
                        </label>
                        <InputField
                          name="paid_amount"
                          type="text"
                          placeholder="Payable Amount"
                          className="w-full"
                        />
                      </div>
                      <InputSelect
                        name="payment_method"
                        label={"Payment Method"}
                        required
                        options={[
                          { label: "Cash", value: "Cash" },
                          { label: "Bank Transfer", value: "BankTransfer" },
                          { label: "Cheque", value: "Cheque" },
                          { label: "Online Payment", value: "OnlinePayment" },
                          { label: "Bkash", value: "Bkash" },
                          { label: "Ucash", value: "Ucash" },
                          { label: "Nagad", value: "Nagad" },
                          { label: "Rocket", value: "Rocket" },
                          { label: "Mobile Banking", value: "MobileBanking" },
                          { label: "Agent Banking", value: "AgentBanking" },
                          { label: "Others", value: "Others" },
                        ]}
                        placeholder="Select Payment Method"
                        className="w-full"
                      />
                      {touched.payment_method && errors.payment_method && (
                        <div className="text-red-500 text-xs mt-2">
                          {errors.payment_status}
                        </div>
                      )}
                      <FileUpload
                        title="Upload your attachment"
                        name="attachment"
                        label="Upload Attachment"
                        endpoint="file-upload"
                        valueKey="file"
                        accept="image/*"
                      />
                      <InputField
                        name="transaction_id"
                        label="Transaction ID"
                        type="text"
                        placeholder="Transaction ID"
                        className="w-full"
                        // required
                      />
                    </div>
                    <div className="w-full mt-3">
                      <TextAreaField
                        label="Description"
                        name="description"
                        type="text"
                        placeholder="Enter Description"
                      />
                    </div>

                    <div className="w-full flex justify-end">
                      <button
                        type="submit"
                        className="mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                      >
                        Submit
                      </button>
                    </div>
                  </Card>

                  {/* Account History details component start */}
                  <div className="mt-5 mb-2">
                    <Card
                      title="Account History"
                      className="w-full"
                      titleClass="text-lg font-bold text-gray-800"
                    >
                      <SalesAccountDetails
                        accountDetails={saleInfo?.sale_account_details}
                      />
                    </Card>
                  </div>
                  {/* Account History details component end */}
                </div>
                <div className="col-span-1">
                  <Card
                    title="Customer Information"
                    className="w-full"
                    titleClass="text-lg font-bold text-gray-800"
                  >
                    <ClientInfo client={saleInfo?.client} />
                  </Card>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      ) : (
        <div className="grid grid-cols-3 gap-4">
          <div className="col-span-2">
            <Card
              title="Collect Payment"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
              secondTitle={
                <div className="flex items-center">
                  <p className="mr-2 flex items-center">
                    {saleInfo?.payment_status === "Paid" && (
                      <Icon
                        icon="mdi:check-circle"
                        width={24}
                        className="text-green-500 mr-1"
                      />
                    )}
                    {saleInfo?.payment_status}
                  </p>
                </div>
              }
            >
              <div className="grid grid-cols-2 gap-4">
                {[
                  { label: "Invoice No", value: saleInfo?.invoice_no },
                  {
                    label: "Payment Status",
                    value: saleInfo?.payment_status,
                    bgColor: "bg-green-200",
                  },
                  { label: "Paid Amount", value: saleInfo?.paid_amount },
                  { label: "Due Amount", value: saleInfo?.due_amount },
                  { label: "Grand Total", value: saleInfo?.grand_total },
                  { label: "Sub Total", value: saleInfo?.sub_total },
                  {
                    label: "Billing Address",
                    value: saleInfo?.billing_address,
                  },
                  {
                    label: "Shipping Address",
                    value: saleInfo?.shipping_address,
                  },
                  // { label: "QR Code", value: saleInfo?.qr_code },
                ].map((info, index) => (
                  <div
                    key={index}
                    className={`flex justify-between items-center p-5 border border-gray-400 ${
                      info.bgColor || "bg-gray-100"
                    } text-black-500 rounded-md h-[40px]`}
                  >
                    <p>{info.label} :</p>
                    <p>{info.value || "N/A"}</p>
                  </div>
                ))}
              </div>
              <div className="w-full mt-4">
                <p className="block text-gray-800 text-base font-semibold mb-2">
                  Sale Details :
                </p>
                {saleInfo?.sale_details?.map((detail, index) => (
                  <div key={index} className="mb-4 p-4 border rounded-md">
                    <p className="text-sm font-semibold">
                      Product ID: {detail.product_id}
                    </p>
                    <p className="text-sm">Qty: {detail.qty}</p>
                    <p className="text-sm">Unit Price: {detail.unit_price}</p>
                    <p className="text-sm">
                      Serial Number: {detail.serial_number}
                    </p>
                  </div>
                ))}
              </div>
            </Card>

            {/* Account History details component start */}
            <div className="mt-5 mb-2">
              <Card
                title="Account History"
                className="w-full"
                titleClass="text-lg font-bold text-gray-800"
              >
                <SalesAccountDetails
                  accountDetails={saleInfo?.sale_account_details}
                />
              </Card>
            </div>
            {/* Account History details component end */}
          </div>

          <div className="col-span-1">
            <Card
              title="Customer Information"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <ClientInfo client={saleInfo?.client} />
            </Card>
          </div>
        </div>
      )}
    </div>
  );
};

export default SalesAccount;
