import React from "react";

const SalesAccountDetails = ({ accountDetails }) => {
  if (!accountDetails || accountDetails.length === 0) {
    return (
      <p className="text-danger-600 text-base font-bold text-center">
        No Account History Found.
      </p>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="table-auto w-full border-collapse border border-gray-300 text-base text-center">
        <thead className="bg-primary-200 text-primary-600 font-bold">
          <tr>
            <th className="border border-gray-300 px-4 py-2">#</th>
            <th className="border border-gray-300 px-4 py-2">Payment Method</th>
            <th className="border border-gray-300 px-4 py-2">Paid Amount</th>
            <th className="border border-gray-300 px-4 py-2">Transaction ID</th>
            <th className="border border-gray-300 px-4 py-2">Pay Date</th>
          </tr>
        </thead>

        <tbody>
          {accountDetails?.map((detail, index) => (
            <tr key={detail.id} className="hover:bg-gray-50">
              <td className="border border-gray-300 px-4 py-2">{index + 1}</td>
              <td className="border border-gray-300 px-4 py-2">
                {detail?.payment_method || "N/A"}
              </td>
              <td className="border border-gray-300 px-4 py-2">
                {detail?.paid_amount || "N/A"}
              </td>
              <td className="border border-gray-300 px-4 py-2 text-danger-500">
                {detail?.transaction_id || "N/A"}
              </td>
              <td className="border border-gray-300 px-4 py-2">
                {detail?.created_at
                  ? new Intl.DateTimeFormat("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    }).format(new Date(detail.created_at))
                  : "N/A"}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default SalesAccountDetails;
