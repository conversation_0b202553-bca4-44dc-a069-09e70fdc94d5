import React, { useRef } from "react";
import Modal from "@/components/ui/Modal";
import { useGetApiWithIdQuery } from "@/store/api/apihandler/commonSlice";
import bblogo from "../../../assets/images/logo/bb-logo.png";
import { numberToWords } from "@/pages/product/numberToWords";
import invoiceLogo from "../../../assets/images/invoiceLogo.jpeg";
import { useParams } from "react-router-dom";
import Button from "@/components/ui/Button";
import Loading from "@/components/Loading";

const SeeChallan = () => {
    const { id } = useParams();
    const { data: saleInfo, isLoading } = useGetApiWithIdQuery(["sales", id]);
    const printRef = useRef(null);
    if (isLoading) {
        return <Loading />;
    }

    const handlePrint = () => {
        const printContent = printRef.current.innerHTML;
        const originalContent = document.body.innerHTML;
        document.body.innerHTML = printContent;
        window.print();
        document.body.innerHTML = originalContent;
        window.location.reload();
    };

    const getName = (name) => {
        console.log(name)
        const words = name?.trim().split(" ");
        return words[0];
    };

    return (
        <>
            <div className="flex justify-center w-full bg-gray-200 p-3 sticky top-0 mb-3 z-50 shadow-lg">
                <Button className="bg-blue-800 text-white" onClick={handlePrint}>Print This Challan</Button>
            </div>
            <style>{`
                @media print {
                    .invoice-container {
                        width: 210mm;
                        height: 297mm;
                        font-size: 12px;
                        color: black;
                        margin: 0;
                        padding: 0;
                        overflow: hidden;
                        box-sizing: border-box;
                        line-height: 1;
                    }
                }
            `}</style>
            <div
                ref={printRef}
                className="invoice-container"
                style={{
                    width: "210mm",
                    minHeight: "297mm",
                    padding: "20px",
                    backgroundColor: "#fff",
                    border: "1px solid #000",
                    margin: "0 auto",
                    fontFamily: "Arial, sans-serif",
                    fontSize: "12px",
                    lineHeight: "1",
                    display: "flex",
                    flexDirection: "column",
                    boxSizing: "border-box",
                    color: "black",
                    position: "relative",
                }}
            >
                <img
                    src={invoiceLogo}
                    alt="Watermark"
                    style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        opacity: 0.3,
                        width: '60%',
                        height: 'auto',
                        zIndex: 0,
                        pointerEvents: 'none',
                    }}
                />

                <div style={{ position: 'relative', zIndex: 1, flex: "1 0 auto" }}>
                    <div>
                        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "10px", borderBottom: "2px solid #1B69B3", paddingBottom: "10px" }}>
                            <div style={{ marginBottom: "10px" }}><img src={bblogo} alt="Logo" style={{ width: "200px" }} /></div>
                            <div style={{ marginBottom: "10px", textAlign: "center", flex: 1, fontSize: "14px", lineHeight: "1" }}>
                                <p style={{ margin: "0", lineHeight: "1", fontSize: "13px", color: "black" }}>House # 13(5<sup>th</sup> Floor), Block-C,</p>
                                <p style={{ margin: "0", lineHeight: "1", fontSize: "13px", color: "black" }}>Main Road, Banasree, Rampura,</p>
                                <p style={{ margin: "0", lineHeight: "1", fontSize: "13px", color: "black" }}>Dhaka-1219, Bangladesh</p>
                            </div>
                            <div style={{ marginBottom: "10px", textAlign: "right", fontSize: "12px", lineHeight: "1", color: "black" }}>
                                <p style={{ margin: "0", lineHeight: "1" }}><span style={{ fontWeight: "bold" }}>Tel:</span> +88 02 8396601</p>
                                <p style={{ margin: "0", lineHeight: "1" }}><span style={{ fontWeight: "bold" }}>Cell:</span> +880 1836-128243</p>
                                <p style={{ margin: "0", lineHeight: "1" }}><span style={{ fontWeight: "bold" }}>Email:</span> <EMAIL></p>
                            </div>
                        </div>

                        <h1 style={{ fontSize: "22px", margin: 0, fontWeight: "bold", color: "black", textAlign: "center", marginBottom: "10px" }}>Challan</h1>
                        <div style={{ display: "flex", justifyContent: "space-between", marginBottom: "25px", fontSize: "12px", lineHeight: "1", color: "black" }}>
                            <div style={{ marginLeft: "45px" }}>
                                <p style={{ margin: "5px 0", lineHeight: "1.2", color: "black" }}>Ref: <span style={{ fontWeight: "bold" }}>Invoice/{saleInfo?.invoice_no}/{`${saleInfo?.client?.name ? getName(saleInfo?.client?.name) : ""}`}</span></p>
                                <span style={{ fontWeight: "bold" }}>To</span>
                                <p style={{ fontWeight: "bold", lineHeight: "1.2" }}>{saleInfo?.client?.name}</p>
                                <p style={{ lineHeight: "1.2", fontWeight: "bold" }}>{saleInfo?.client?.address}</p>
                                <p style={{ lineHeight: "1.2", fontWeight: "bold" }}><span style={{ fontWeight: "bold" }}>Concern Person:</span> {saleInfo?.client?.phone}</p>
                                {saleInfo?.client?.email && <p style={{ lineHeight: "1.2", fontWeight: "bold" }}><span style={{ fontWeight: "bold" }}>Email:</span> {saleInfo?.client?.email}</p>}
                            </div>
                            {/* <div><img style={{ width: "60px" }} src={`${import.meta.env.VITE_MEDIA_URL}/${saleInfo?.qr_code}`} /></div> */}
                            <div> <p style={{ marginTop: "3px", lineHeight: "1", color: "black", textAlign: "center" }}>Date: <span style={{ fontWeight: "bold" }}>{new Date(saleInfo?.created_at).toLocaleDateString("en-US", {
                                year: "numeric",
                                month: "long",
                                day: "numeric",
                            })}</span></p></div>
                        </div>
                        <table style={{ width: "100%", borderCollapse: "collapse", border: "1px solid #000", fontSize: "12px" }}>
                            <thead style={{ backgroundColor: "#B4C6E7", WebkitPrintColorAdjust: "exact", printColorAdjust: "exact", colorAdjust: "exact", colorAdjust: "exact" }}>
                                <tr>
                                    <th style={{ border: "1px solid #000", padding: "1px", WebkitPaddingStart: "1px", WebkitPaddingEnd: "1px", WebkitPaddingBefore: "1px", WebkitPaddingAfter: "1px", textAlign: "center", width: "5%", color: "black", fontSize: "12px" }}>SL</th>
                                    <th style={{ border: "1px solid #000", padding: "1px", WebkitPaddingStart: "1px", WebkitPaddingEnd: "1px", WebkitPaddingBefore: "1px", WebkitPaddingAfter: "1px", textAlign: "center", width: "70%", color: "black", fontSize: "12px" }}>Item Name & Descriptions</th>
                                    <th style={{ border: "1px solid #000", padding: "1px", WebkitPaddingStart: "1px", WebkitPaddingEnd: "1px", WebkitPaddingBefore: "1px", WebkitPaddingAfter: "1px", textAlign: "center", width: "10%", color: "black", fontSize: "12px" }}>Qty</th>
                                    <th style={{ border: "1px solid #000", padding: "1px", WebkitPaddingStart: "1px", WebkitPaddingEnd: "1px", WebkitPaddingBefore: "1px", WebkitPaddingAfter: "1px", textAlign: "center", width: "15%", color: "black", fontSize: "12px" }}>Total Qty</th>
                                </tr>
                            </thead>
                            <tbody>
                                {saleInfo?.sale_details?.map((detail, index) => {
                                    const hasRelatedProducts = detail?.related_products?.length > 0;
                                    const rowSpan = 1 + (hasRelatedProducts ? 1 + detail?.related_products?.length : 0);

                                    return (
                                        <React.Fragment key={detail?.id}>
                                            <tr>
                                                <td rowSpan={rowSpan} style={{ border: "1px solid #000", fontWeight: "bold", color: "black", padding: "4px", textAlign: "center", verticalAlign: "top" }}>
                                                    {index + 1 < 10 ? `0${index + 1}.` : index + 1}
                                                </td>
                                                <td style={{ border: "1px solid #000", padding: "4px" }}>
                                                    <span style={{ fontSize: "12px", color: "black", fontWeight: "bold" }}>{detail?.product?.name}</span><br />
                                                    {detail?.product?.is_description_shown_in_invoices && (
                                                        <div>
                                                            <div style={{ fontSize: "12px", color: "black", marginTop: "5px", lineHeight: "1" }}
                                                                dangerouslySetInnerHTML={{ __html: detail?.product?.short_description }}
                                                            />
                                                            {detail?.product?.brand !== null && <p style={{ marginTop: "3px", fontSize: "12px", color: "black", fontWeight: "bold", lineHeight: "1" }}>Brand : {detail?.product?.brand?.name}</p>}
                                                            {detail?.product?.category !== null && <p style={{ marginTop: "3px", fontSize: "12px", color: "black", fontWeight: "bold", lineHeight: "1" }}>Product Type : {detail?.product?.category?.name}</p>}
                                                            {detail?.product?.sub_category !== null && <p style={{ marginTop: "3px", fontSize: "12px", color: "black", fontWeight: "bold", lineHeight: "1" }}>Model : {detail?.product?.sub_category?.name}</p>}
                                                        </div>
                                                    )}
                                                    {detail?.description && (
                                                        <div style={{ fontSize: "12px", lineHeight: "1", color: "black" }}
                                                            dangerouslySetInnerHTML={{ __html: detail?.description }}
                                                        />
                                                    )}
                                                    {detail?.warranty_period && <p style={{ marginTop: "3px", color: "black", fontSize: "11px", fontWeight: "bold", lineHeight: "1" }}>
                                                        Warranty: {detail?.guarantee_period && detail?.guarantee_period + ' ' + detail?.warranty_type
                                                            + ' ' + 'replacement warranty & '}
                                                        {detail?.warranty_period} {detail?.warranty_type} warranty
                                                        {detail?.warranty_period_value && ` (${detail?.warranty_period_value})`}.
                                                    </p>}
                                                    {/* {detail?.warranty_period_value && <p style={{ marginTop: "3px", color: "black", fontSize: "12px", fontWeight: "bold", lineHeight: "1" }}>
                                                        Warranty: {detail?.warranty_period_value}
                                                    </p>} */}
                                                    {detail?.serials?.length > 0 && (
                                                        <p style={{ marginTop: "5px", fontSize: "12px", color: "black", fontWeight: "bold", lineHeight: "1" }}>
                                                            <span style={{ backgroundColor: "#FFFF00", color: "black", fontSize: "12px", WebkitPrintColorAdjust: "exact", printColorAdjust: "exact", colorAdjust: "exact", colorAdjust: "exact" }}>
                                                                Sl No: {detail?.serials.map(serial => serial.serial_number).join(', ')}
                                                            </span>
                                                        </p>
                                                    )}
                                                </td>
                                                <td style={{ border: "1px solid #000", color: "black", padding: "4px", textAlign: "center", fontSize: "12px" }}>
                                                    {detail?.qty} Set
                                                </td>
                                                <td rowSpan={rowSpan} style={{ border: "1px solid #000", padding: "4px", textAlign: "center", color: "black", fontSize: "12px" }}>
                                                    {detail?.qty} Set
                                                </td>
                                                {/* <td rowSpan={rowSpan} style={{ border: "1px solid #000", fontWeight: "bold", padding: "4px", textAlign: "center", color: "black", fontSize: "12px" }}>
                                                        {detail?.total_price}
                                                    </td> */}
                                            </tr>
                                            {hasRelatedProducts && (
                                                <>
                                                    <tr style={{ backgroundColor: "#B4C6E7" }}>
                                                        <td colSpan="2" style={{
                                                            WebkitPrintColorAdjust: "exact", printColorAdjust: "exact", colorAdjust: "exact", colorAdjust: "exact", border: "1px solid #000", padding: "1px",
                                                            padding: "1px", WebkitPaddingStart: "1px", WebkitPaddingEnd: "1px", WebkitPaddingBefore: "1px", WebkitPaddingAfter: "1px", textAlign: "center", fontWeight: "bold", color: "black", fontSize: "12px"
                                                        }}>
                                                            Product Related Accessories
                                                        </td>
                                                    </tr>
                                                    {detail?.related_products?.map((related, relIndex) => (
                                                        <tr key={relIndex}>
                                                            <td style={{ border: "1px solid #000", padding: "1px", color: "black", fontSize: "12px" }}>
                                                                {related?.name}
                                                            </td>
                                                            <td style={{ border: "1px solid #000", padding: "1px", textAlign: "center", color: "black", fontSize: "12px" }}>
                                                                {related?.qty} Nos
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </>
                                            )}
                                        </React.Fragment>
                                    );
                                })}

                                <tr style={{ backgroundColor: "#B4C6E7", WebkitPrintColorAdjust: "exact", printColorAdjust: "exact", colorAdjust: "exact", colorAdjust: "exact" }}>
                                    <td colSpan="3" style={{
                                        border: "1px solid #000", padding: "2px",
                                        padding: "2px", WebkitPaddingStart: "2px", WebkitPaddingEnd: "2px", WebkitPaddingBefore: "2px", WebkitPaddingAfter: "2px", textAlign: "right", fontWeight: "bold", lineHeight: "1", color: "black", fontSize: "12px"
                                    }}><span style={{ marginRight: "5px" }}>Total QTY</span></td>
                                    <td style={{
                                        border: "1px solid #000", padding: "2px",
                                        padding: "2px", WebkitPaddingStart: "2px", WebkitPaddingEnd: "2px", WebkitPaddingBefore: "2px", WebkitPaddingAfter: "2px", textAlign: "center", fontWeight: "bold", lineHeight: "1", color: "black", fontSize: "12px"
                                    }}>{saleInfo?.sale_details?.reduce((total, detail) => total + detail.qty, 0)} sets</td>

                                </tr>
                            </tbody>
                        </table>

                    </div>
                </div>
                <div style={{ textAlign: "center", flexShrink: 0, fontSize: "12px", lineHeight: "1" }}>
                    <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-end", marginTop: "20px", lineHeight: "1" }}>
                        <div style={{ textAlign: "left" }}>
                            <p style={{ marginBottom: "70px", fontWeight: "bold", color: "black", fontSize: "12px" }}>{saleInfo?.client?.name}</p>
                            {/* <p style={{ margin: "30px 0", color: "black", fontSize: "12px" }}>______________________</p> */}
                            <p style={{ margin: "30px 0", color: "black", fontSize: "12px" }}>Received Signature</p>
                        </div>
                        <div style={{ textAlign: "right" }}>
                            <p style={{ marginBottom: "70px", fontWeight: "bold", color: "black", fontSize: "12px" }}>For BacBon Limited</p>
                            {/* <p style={{ margin: "10px 0", color: "black", fontSize: "12px" }}>______________________</p> */}
                            <p style={{ lineHeight: "1", margin: "30px 0", color: "black", fontSize: "12px" }}>Authorized Signature</p>
                        </div>
                    </div>
                </div>
                <div
                    style={{
                        WebkitPrintColorAdjust: "exact",
                        printColorAdjust: "exact",
                        colorAdjust: "exact",
                        position: "absolute",
                        bottom: "0",
                        left: "0",
                        width: "100%",
                        height: "20px",
                        backgroundColor: "#1a73e8",
                        color: "white",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        fontSize: "12px",
                        lineHeight: "1",
                    }}
                >
                    <p style={{ color: "white", fontSize: "12px", marginLeft: "10px" }}>Email: <EMAIL></p>
                    <p style={{ color: "white", fontSize: "12px", marginRight: "10px" }}>Web: www.bacbonltd.com</p>
                </div>
            </div>
        </>
    );
};

export default SeeChallan;
