import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import Card from '@/components/ui/Card';
import { useGetApiQuery, usePostApiMutation } from '@/store/api/apihandler/commonSlice';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { initialValues, validationSchema } from './SaleCrud';
import AddedProductsTable from './AddedProductsTable';
import AddProductForm from './AddProductForm';
import EditAddedProduct from './EditAddedProduct';
import SellInformationCard from './SellInformationCard';
import Button from '@/components/ui/Button';
import SummaryCard from './SummaryCard';
import ConfirmationModal from '@/components/ui/form/ConfirmationModal';
import Modal from '@/components/ui/Modal';


const StoreSale = () => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [formData, setFormData] = useState(null);
    const [isConfirmationOpen, setIsConfirmationOpen] = useState(false);
    const openModal = () => {
        setIsModalOpen(true);
    };
    const closeModal = () => {
        setIsModalOpen(false);
    }


    const [postApi] = usePostApiMutation();
    const navigate = useNavigate();
    const allProducts = useGetApiQuery({ url: "products", params: { pagination: 0, is_active: 1 } });
    const [addedProducts, setAddedProducts] = useState([]);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [currentProductIndex, setCurrentProductIndex] = useState(null);
    const [discount, setDiscount] = useState(0);
    const productOptions = allProducts?.data?.map((product) => ({
        label: (
            <div className='flex justify-between items-center space-x-2'>
                <span>{product.name}</span>
                {
                    product?.image && (
                        <img
                            src={`${import.meta.env.VITE_MEDIA_URL}/${product.image}`}
                            className='w-8 h-8 rounded-full object-cover'
                            alt={product.name}
                        />
                    )
                }
            </div>
        ),
        value: product.id,
    }));


    const handleAddProduct = (values, resetForm) => {
        const isProductAlreadyAdded = addedProducts.some(product => product.product_id === values.product_id);


        if (isProductAlreadyAdded) {
            toast.error('You have already added this product.');
            return;
        }


        setAddedProducts([...addedProducts, {
            product_id: values.product_id,
            product_name: allProducts?.data?.find((product) => product.id === values.product_id)?.name,
            unit_price: values.unit_price,
            qty: values.qty,
            warranty_period: values.warranty_period,
            guarantee_period: values.guarantee_period ? values.guarantee_period : null,
            warranty_type: values.warranty_type,
            warranty_period_value: String(values.warranty_period_value),
            total_price: values.unit_price * values.qty,
            serials: [],
            related_products: values.related_products || [],
        }]);
    };



    const handleDeleteProduct = (index) => {
        const updatedProducts = addedProducts.filter((_, i) => i !== index);
        setAddedProducts(updatedProducts);
    };


    const handleEditProduct = (index) => {
        setCurrentProductIndex(index);
        setIsEditModalOpen(true);
    };


    const handleUpdateProduct = (values) => {
        const updatedProducts = [...addedProducts];
        updatedProducts[currentProductIndex] = {
            ...updatedProducts[currentProductIndex],
            product_id: values.product_id,
            product_name: allProducts?.data?.find((product) => product.id === values.product_id)?.name,
            unit_price: values.unit_price,
            qty: values.qty,
            warranty_type: values.warranty_type,
            warranty_period: values.warranty_period,
            guarantee_period: values.guarantee_period,
            warranty_period_value: String(values.warranty_period_value),
            total_price: values.unit_price * values.qty,
            // related_products: values.related_products || [],
        };
        setAddedProducts(updatedProducts);
        setIsEditModalOpen(false);
    };


    const subtotal = addedProducts.reduce((acc, product) => acc + (product.unit_price * product.qty), 0);


    const handleDiscountChange = (e) => {
        const value = e.target.value;
        if (value === '' || /^\d*$/.test(value)) {
            const discountValue = parseFloat(value) || 0;
            if (discountValue <= subtotal) {
                setDiscount(discountValue);
            } else {
                setDiscount(subtotal);
            }
        }
    };
    const netPrice = subtotal - discount;
    const handleUploadSerial = (productId, serials) => {
        const updatedProducts = addedProducts.map((product) => {
            if (product.product_id === productId) {
                return {
                    ...product,
                    serials: serials,
                };
            }
            return product;
        });
        setAddedProducts(updatedProducts);
    };
    const handleSubmit = async (values, { resetForm }) => {
        console.log(values, 'from form');


        const dueAmount = netPrice - (parseFloat(values.paid_amount) || 0);
        if (dueAmount < 0) {
            toast.warning("Due amount can't be negative");
            return;
        }


        if (addedProducts.length === 0) {
            toast.warning(`Please add at least one product`);
            return;
        }


        const missingSerials = addedProducts.some(product => {
            const productInfo = allProducts?.data?.find(item => item.id === product.product_id);
            return productInfo?.has_serials && product.serials.length !== product.qty;
        });


        if (missingSerials) {
            return; // Do not proceed if serials are missing
        }


        // const productWithMissingSerials = addedProducts.find(product => {
        // const productInfo = allProducts?.data?.find(item => item.id === product.product_id);
        // return productInfo?.has_serials && product.serials.length !== product.qty;
        // });


        // if (productWithMissingSerials) {
        // toast.warning(`Please upload the correct number of serials for the product: ${productWithMissingSerials.product_name}`);
        // return;
        // }


        const dataToSubmit = {
            ...(
                values.client_id
                    ? { client_id: values.client_id }
                    : {
                        client_info: {
                            name: values.client_name,
                            phone: values.phone,
                            address: values.billing_address,
                            shipping_address: values.shipping_address
                        }
                    }
            ),
            ...(values.is_checked ? { shipping_address: values.shipping_address } : {}),
            received_date: values.received_date,
            sub_total: subtotal,
            promo_code: values.promo_code,
            discount: discount,
            billing_address: values.billing_address,
            shipping_address: values.shipping_address,
            payment_method: values.payment_method,
            transaction_id: values.payment_method === 'Cash' ? '' : values.transaction_id,
            paid_amount: values.paid_amount,
            due_amount: netPrice - values.paid_amount,
            grand_total: netPrice,
            product_details: addedProducts.map(product => ({
                ...product,
                related_products: product.related_products || [],
            })),
            attachment: values.attachment || null,
            description: values.description,
            invoice_date: values.invoice_date ? values.invoice_date : null,
            vat: values.vat,
            is_vat_shown: values.is_vat_shown,
        };
        setFormData(dataToSubmit);
        setIsConfirmationOpen(true);
        // setIsSubmitting(true);
        setIsSubmitting(false);
    };
    const handleConfirmSubmit = async () => {
        try {
            const response = await postApi({
                end_point: "sales",
                body: formData,
            }).unwrap();
            toast.success("Submission successful");
            navigate("/sales-list");
            setFormData(null);
        } catch (err) {
            console.log("Submission failed");
        } finally {
            setIsSubmitting(false);
        }
    };
    return (
        <div>
            <Formik
                initialValues={initialValues}
                onSubmit={handleSubmit}
                validationSchema={validationSchema}
            >
                {({ values, resetForm, errors, touched, setFieldValue }) => (
                    <Form>
                        {console.log(values)}
                        <div className='grid grid-cols-1 lg:grid-cols-5 gap-5'>
                            <div className='lg:col-span-3'>
                                <SellInformationCard
                                    values={values}
                                    setFieldValue={setFieldValue}
                                    touched={touched}
                                    errors={errors}
                                />
                                <Card
                                    title="Added Products"
                                    className="w-full mt-5"
                                    titleClass="text-lg font-bold text-gray-800"
                                    noborder={true}
                                    headerslot={
                                        <Button onClick={openModal} type="button" className="bg-blue-800 text-white px-4 py-2 rounded-md flex items-center space-x-2">
                                            <div className="w-6 h-6 flex items-center justify-center rounded-full">
                                                <Icon icon="formkit:add" className='w-8 h-8' />
                                            </div>
                                            <span>Add Product</span>
                                        </Button>}
                                >
                                    <AddedProductsTable
                                        allProducts={allProducts}
                                        addedProducts={addedProducts}
                                        handleEditProduct={handleEditProduct}
                                        handleDeleteProduct={handleDeleteProduct}
                                        handleUploadSerial={handleUploadSerial}
                                    />
                                    {addedProducts.length > 0 && addedProducts?.some(product => {
                                        const productInfo = allProducts?.data?.find(item => item.id === product.product_id);
                                        return productInfo?.has_serials && product.serials.length !== product.qty;
                                    }) && (
                                            <div className="mt-4 p-4 bg-[#fdf0f0] border border-[#ec7070] rounded-md">
                                                <h6 className="text-red-600 font-semibold">
                                                    The following products are missing serials:
                                                </h6>
                                                <ul className="list-disc pl-6">
                                                    {addedProducts
                                                        .filter(product => {
                                                            const productInfo = allProducts?.data?.find(item => item.id === product.product_id);
                                                            return productInfo?.has_serials && product.serials.length !== product.qty;
                                                        })
                                                        .map(product => (
                                                            <li key={product.product_id} className="text-red-600">
                                                                {product.product_name} ({product.serials.length}/{product.qty} serials selected)
                                                            </li>
                                                        ))}
                                                </ul>
                                            </div>
                                        )}
                                    {addedProducts.length > 0 && addedProducts.some(product => {
                                        const productInfo = allProducts?.data?.find(item => item.id === product.product_id);
                                        return productInfo?.has_serials && product.serials.length > 0;
                                    }) && (
                                            <div className="mt-4 p-4 bg-green-100 border border-green-300 rounded-md">
                                                <h6 className="text-green-600 font-semibold">
                                                    The following products have selected serials:
                                                </h6>
                                                <ul className="list-disc pl-6">
                                                    {addedProducts
                                                        .filter(product => {
                                                            const productInfo = allProducts?.data?.find(item => item.id === product.product_id);
                                                            return productInfo?.has_serials && product.serials.length > 0;
                                                        })
                                                        .map(product => (
                                                            <li key={product.product_id} className="text-green-600">
                                                                <div className="font-bold">
                                                                    {product.product_name} ({product.serials.length}/{product.qty} serials selected)
                                                                </div>
                                                                {/* <ul className="list-disc pl-6">
                                                                    {product.serials.map((serial, idx) => (
                                                                        <li key={idx} className="text-green-500">{serial}</li>
                                                                    ))}
                                                                </ul> */}
                                                            </li>
                                                        ))}
                                                </ul>
                                            </div>
                                        )}

                                </Card>
                            </div>
                            <div className='lg:col-span-2'>
                                <SummaryCard
                                    addedProducts={addedProducts}
                                    values={values}
                                    setFieldValue={setFieldValue}
                                    subtotal={subtotal}
                                    discount={discount}
                                    netPrice={netPrice}
                                    handleDiscountChange={handleDiscountChange}
                                    allProducts={allProducts}
                                    errors={errors}
                                    touched={touched}
                                />
                                {/* <div className='flex justify-end'>
<button
type="submit"
className="bg-blue-800 text-white font-bold py-2 px-4 mt-4 rounded"
disabled={isSubmitting}
>
{isSubmitting ? "Submitting..." : "Submit"}
</button>
</div> */}
                                <div className="flex justify-end">
                                    <button
                                        type="submit"
                                        className="bg-blue-800 text-white font-bold py-2 px-4 mt-4 rounded"
                                        disabled={isSubmitting || addedProducts?.some(product => {
                                            const productInfo = allProducts?.data?.find(item => item?.id === product?.product_id);
                                            return productInfo?.has_serials && product?.serials?.length !== product?.qty;
                                        })}
                                    >
                                        {addedProducts.some(product => {
                                            const productInfo = allProducts?.data?.find(item => item.id === product.product_id);
                                            return productInfo?.has_serials && product.serials.length !== product.qty;
                                        })
                                            ? "Proceed to Submit"
                                            : isSubmitting
                                                ? "Submitting..."
                                                : "Submit"}
                                    </button>
                                </div>
                            </div>
                        </div>


                    </Form>
                )}
            </Formik>
            {isModalOpen && <AddProductForm
                isModalOpen={isModalOpen}
                closeModal={closeModal}
                handleAddProduct={handleAddProduct}
            />}
            {isConfirmationOpen && (
                <Modal
                    title="Confirmation"
                    activeModal={isConfirmationOpen}
                    onClose={() => {
                        if (!isSubmitting) {
                            setIsConfirmationOpen(false);
                        }
                    }}
                    className="max-w-xl"
                >
                    <div className="rounded-lg p-5">
                        <div className="flex items-center gap-3 mb-4">
                            <Icon icon="mdi:check-circle" className="text-green-500 text-xl" />
                            <h2 className="text-lg font-semibold text-gray-800">Confirmation</h2>
                        </div>
                        <p className="text-sm text-gray-600 mb-6">
                            Are you sure you want to submit this sale?
                        </p>
                        <div className="flex justify-end gap-3">
                            <button
                                onClick={() => {
                                    if (!isSubmitting) {
                                        setIsConfirmationOpen(false);
                                    }
                                }}
                                disabled={isSubmitting}
                                className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={async () => {
                                    setIsSubmitting(true);
                                    try {
                                        await handleConfirmSubmit();
                                        setIsConfirmationOpen(false);
                                    } finally {
                                        setIsSubmitting(false);
                                    }
                                }}
                                disabled={isSubmitting}
                                className={`px-4 py-2 ${isSubmitting ? "bg-gray-400" : "bg-green-500 hover:bg-green-600"
                                    } text-white rounded`}
                            >
                                {isSubmitting ? "Submitting..." : "Confirm"}
                            </button>
                        </div>
                    </div>
                </Modal>
            )}


            <EditAddedProduct
                isOpen={isEditModalOpen}
                onClose={() => setIsEditModalOpen(false)}
                product={addedProducts[currentProductIndex]}
                productOptions={productOptions}
                onSubmit={handleUpdateProduct}
            />
        </div>
    );
};


export default StoreSale;

