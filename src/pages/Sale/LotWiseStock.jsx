import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import { Icon } from "@iconify/react";
import { useNavigate } from "react-router-dom";
import LotDetailsModal from "./LotDetailsModal";
import { formattedDate } from "@/constant/data";

const LotWiseStock = () => {
    const [apiParam, setApiParam] = useState(0);
    const [filter, setFilter] = useState("");
    const [modalData, setModalData] = useState(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    const { data, isLoading, isFetching } = useGetApiQuery({ url: "inventory-lots", params: apiParam });
    const navigate = useNavigate();

    const changePage = (value) => {
        setApiParam(value);
    };

    const columns = [
        { label: "Sl", field: "sl" },
        { label: "Product", field: "productName" },
        { label: "Received Dates", field: "received_dates" },
        { label: "Entry Dates", field: "created_dates" },
        { label: "Latest Entry Date", field: "latest_created_at" },
        { label: "Lot Number", field: "invoice_number" },
        { label: "Stock Quantity", field: "qty" },
    ];

    const handleQtyClick = (productId, invoiceNumber) => {
        setModalData({ productId, invoiceNumber });
        setIsModalOpen(true);
    };

    const tableData = data?.data?.map((item, index) => {
        return {
            sl: index + 1,
            productName: item?.product_name,
            received_dates: (
                <div className="received-dates flex flex-wrap">
                    {item?.received_dates &&
                        item?.received_dates
                            .split(',')
                            .map((date, index) => (
                                <div key={index} className="bg-blue-100 px-2 py-[3px] rounded-lg mr-2 mb-2">
                                    {formattedDate(date.trim())}
                                </div>
                            ))}
                </div>
            ),
            latest_created_at: (
                <div className="bg-blue-100 px-2 py-[3px] rounded-lg my-1 whitespace-nowrap">
                    {formattedDate(item?.latest_created_at)}
                </div>
            ),
            created_dates: (
                <div className="created-dates flex flex-wrap">
                    {item?.created_dates &&
                        item?.created_dates
                            .split(',')
                            .map((date, index) => (
                                <div key={index} className="bg-blue-100 px-2 py-[3px] rounded-lg mr-2 mb-2">
                                    {formattedDate(date.trim())}
                                </div>
                            ))}
                </div>
            ),
            qty: (
                <Badge
                    className="bg-green-500 px-2 py-[3px] w-[130px] flex justify-between items-center text-sm font-semibold text-slate-100 capitalize cursor-pointer"
                    onClick={() => handleQtyClick(item?.product_id, item?.invoice_number)}
                >
                    <span>Qty : {item.qty} pcs</span>
                    <span className="mx-2 text-white">|</span>
                    <Icon
                        icon="akar-icons:eye"
                        className="text-white cursor-pointer hover:text-gray-100"
                        onClick={() => handleQtyClick(item?.product_id, item?.invoice_number)}
                    />
                </Badge>
            ),
            invoice_number: (
                <div className="flex items-center justify-center space-x-2 bg-gray-100 px-2 py-[3px] rounded-lg">
                    <Icon
                        icon="mdi:package-variant"
                        className="text-gray-600 cursor-pointer hover:text-gray-800"
                        width={25}
                    />
                    <span>{item?.invoice_number}</span>
                </div>
            ),
        };
    });

    return (
        <div>
            <BasicTablePage
                loading={isLoading || isFetching}
                title="Lot Wise Inventory"
                columns={columns}
                changePage={changePage}
                data={tableData}
                filter={filter}
                setFilter={setApiParam}
                currentPage={data?.current_page}
                totalPages={Math.ceil(
                    data?.total / data?.per_page
                )}
            />

            {isModalOpen && (
                <LotDetailsModal
                    modalData={modalData}
                    isOpen={isModalOpen}
                    onClose={() => setIsModalOpen(false)}
                />
            )}
        </div>
    );
};

export default LotWiseStock;
