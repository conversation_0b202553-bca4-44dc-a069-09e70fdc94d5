import * as Yup from 'yup';

export const initialValues = {
    client_id: null,
    client_name: '',
    phone: '',
    description: '',
    billing_address: '',
    shipping_address: '',
    paid_amount: '',
    payment_method: 'Cash',
    attachment: null,
    transaction_id: '',
    qr_code: '',
    is_checked: false,
    invoice_date: null,
    vat: 'Without VAT & TAX',
    is_vat_shown: 0,
    related_products: [],
    // warranty_value: false
};

export const validationSchema = Yup.object().shape({
    // client_id: Yup.string().required('Client ID is required'),
    client_name: Yup.string().required('Customer name is required'),
    phone: Yup.string()
        .matches(/^[0-9]{11}$/, "Enter a valid 11-digit phone number")
        .required("Phone number is required"),
    paid_amount: Yup.number()
        .required('Paid amount is required')
        .min(0, 'Paid amount can be either zero or a positive number'),
    payment_method: Yup.string().required('Payment method is required'),
    shipping_address: Yup.string().required('Shipping address is required'),
    billing_address: Yup.string().required('Billing address is required'),
    transaction_id: Yup.string().when('payment_method', {
        is: (value) => value !== 'Cash',
        then: Yup.string().required('Transaction ID is required'),
        otherwise: Yup.string().nullable(),
    }),
});

export const paymentMethodOptions = [
    { label: 'Cash', value: 'Cash' },
    { label: 'Bank Transfer', value: 'BankTransfer' },
    { label: 'Cheque', value: 'Cheque' },
    { label: 'Online Payment', value: 'OnlinePayment' },
    { label: 'Bkash', value: 'Bkash' },
    { label: 'Ucash', value: 'Ucash' },
    { label: 'Nagad', value: 'Nagad' },
    { label: 'Rocket', value: 'Rocket' },
    { label: 'Mobile Banking', value: 'MobileBanking' },
    { label: 'Agent Banking', value: 'AgentBanking' },
    { label: 'Others', value: 'Others' },
];