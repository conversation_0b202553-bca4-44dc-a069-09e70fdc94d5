// LicenseTable.jsx
import BasicTablePage from '@/components/partials/common-table/table-basic';
import { useGetApiQuery } from '@/store/api/apihandler/commonSlice';
import { Icon } from '@iconify/react';
import React, { useState, useEffect } from 'react';

const LicenseTable = ({ licenseTypeId, onSelectItem, resetSelection }) => {
    const { data: unsoldLicenses } = useGetApiQuery({ url: `unused-license/${licenseTypeId}`, params: 0 });
    const [selectedRow, setSelectedRow] = useState(null);

    const handleCheckboxChange = (item, index) => {
        setSelectedRow(index);
        onSelectItem(item); 
    };

    // Reset selection when "Save & Add More" is triggered
    useEffect(() => {
        setSelectedRow(null);
    }, [licenseTypeId]);

    const columns = [
        { label: "Select", field: "checkbox" },
        { label: "License Type", field: "license_type" },
        { label: "Key", field: "key" },
    ];

    const tableData = unsoldLicenses?.data?.map((item, index) => ({
        checkbox: (
            <input
                type="checkbox"
                className="w-5 h-5"
                checked={selectedRow === index}
                onChange={() => handleCheckboxChange(item, index)}
            />
        ),
        license_type: (
            <div className="flex items-center gap-3">
                <Icon icon={item?.license_type?.icon} />
                {item?.license_type?.name}
            </div>
        ),
        key: item?.key,
    }));

    return (
        <div>
            <BasicTablePage
                title="Key List"
                columns={columns}
                data={tableData}
            />
        </div>
    );
};

export default LicenseTable;
