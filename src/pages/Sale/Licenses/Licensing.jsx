import Card from '@/components/ui/Card';
import { useGetApiWithIdQuery, usePostApiMutation } from '@/store/api/apihandler/commonSlice';
import { Icon } from '@iconify/react';
import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import LicenseKeyModal from './LicenseKeyModal';
import { Formik, Form, Field } from 'formik';
import { toast } from 'react-toastify';
import ViewLicenseModal from './ViewLicenseModal';

const InfoItem = ({ label, value }) => (
    <div className="flex items-center justify-between p-3 bg-gray-100 rounded-lg">
        <span className="font-semibold">{label}</span>
        <span className="text-lg">{value}</span>
    </div>
);

const ProductDetailCard = ({ detail, setIsModalOpen, setIsKeyModalOpen, setItem }) => (
    <div className="p-4 mb-4 bg-gray-50 rounded-lg shadow">
        <div className="p-4 rounded-lg shadow-lg bg-gradient-to-r from-indigo-50 to-indigo-100">
            <div className="text-xl font-semibold text-indigo-800 mb-4 flex items-center space-x-2">
                <span className="text-indigo-600">
                    <Icon icon="mdi:label" width="24" height="24" />
                </span>
                <span>{detail.product?.name}</span>
            </div>
            <InfoItemWithIcon label="Quantity" value={detail.qty} icon="mdi:package-variant" />
            <InfoItemWithIcon label="Unit Price" value={`$${detail.unit_price}`} icon="mdi:cash" />
            <InfoItemWithIcon label="Total Price" value={`$${detail.total_price}`} icon="mdi:currency-usd" />
        </div>

        {/* Serials Table */}
        {detail.serials && detail.serials.length > 0 && (
            <div className="mt-4">
                <h5 className="font-semibold text-gray-800 mb-3">Serials</h5>
                <table className="w-full border-collapse border border-gray-200 rounded-lg">
                    <thead>
                        <tr className="bg-gradient-to-r from-indigo-50 to-indigo-100">
                            <th className="p-3 font-semibold text-left text-gray-700">Serial Number</th>
                            <th className="p-3 font-semibold text-left text-gray-700">Warranty</th>
                            <th className="p-3 font-semibold text-center text-gray-700">View Licenses</th>
                            <th className="p-3 font-semibold text-center text-gray-700">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {detail.serials.map((serial, index) => (
                            <tr key={index} className="border-t border-gray-200">
                                <td className="p-3">{serial.serial_number}</td>
                                <td className="p-3">{serial.warranty_period_value} months</td>
                                <td className="p-3 text-center">
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setIsKeyModalOpen(true);
                                            setItem({ serialId: serial?.id })
                                        }}
                                        className="px-4 py-2 bg-blue-800 text-white rounded-lg hover:bg-blue-600 transition"
                                    >
                                        <Icon icon="mdi:eye" />
                                    </button>
                                </td>
                                <td className="p-3 text-center">
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setIsModalOpen(true);
                                            setItem({ serialId: serial?.id, productId: serial?.product_id })
                                        }}
                                        className="px-4 py-2 bg-blue-800 text-white rounded-lg hover:bg-blue-600 transition"
                                    >
                                        Add Keys
                                    </button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        )}
    </div>
);

const InfoItemWithIcon = ({ label, value, icon }) => (
    <div className="flex justify-between items-center text-gray-700 py-2 border-b border-gray-200">
        <span className="flex items-center space-x-2">
            <Icon icon={icon} width="20" height="20" />
            <span>{label}:</span>
        </span>
        <span className="text-lg font-medium">{value}</span>
    </div>
);

const Licensing = () => {
    const { id } = useParams();
    const [postApi] = usePostApiMutation();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isKeyModalOpen, setIsKeyModalOpen] = useState(false);
    const [checkItem, setItem] = useState({});
    const { data: saleInfo } = useGetApiWithIdQuery(["sales", id]);

    const handleModal = () => {
        setIsModalOpen(false);
    };
    const handleViewModal = () => {
        setIsKeyModalOpen(false);
    }

    const handleSubmit = async (values, { resetForm }) => {
        const formBody = {
            sale_id: Number(id),
            product_id: checkItem?.productId,
            product_serial_id: checkItem?.serialId,
            licenses: values.license.map((license) => Number(license.id))
        };
        console.log(formBody, 'formBody');
        try {
            const response = await postApi({
                end_point: "license-assigns",
                body: formBody,
            }).unwrap();
            toast.success("Serial Added Successfully!");
            resetForm();
        } catch (err) {
            toast.error("Submission failed. Please try again.");
        }
    };



    if (!saleInfo) return <div>Loading...</div>;

    return (
        <Formik
            initialValues={{
                license: [],
                product_id: "",
                product_serial_id: ""
            }}
            onSubmit={handleSubmit}
        >
            {({ values, setFieldValue }) => (
                <Form className="p-6 space-y-6 bg-gray-50 min-h-screen">
                    {console.log(values, 'values')}
                    <Card title="Sale Information">
                        <div className="grid grid-cols-3 gap-4 text-gray-700">
                            <InfoItem label="Invoice No" value={saleInfo.invoice_no} />
                            <InfoItem label="Sub Total" value={saleInfo.sub_total} />
                            <InfoItem label="Grand Total" value={saleInfo.grand_total} />
                            <InfoItem label="Paid Amount" value={saleInfo.paid_amount} />
                            <InfoItem label="Due Amount" value={saleInfo.due_amount} />
                            <InfoItem label="Payment Status" value={saleInfo.payment_status} />
                        </div>
                    </Card>

                    {saleInfo.sale_details && saleInfo.sale_details.length > 0 && (
                        <div className="flex gap-6 mt-5">
                            <Card title="Product Details" className="flex-1">
                                {saleInfo.sale_details.map((detail, index) => (
                                    <ProductDetailCard
                                        key={index}
                                        detail={detail}
                                        setIsModalOpen={setIsModalOpen}
                                        setIsKeyModalOpen={setIsKeyModalOpen}
                                        setItem={setItem} />
                                ))}
                            </Card>

                            <div className="w-1/3">
                                <Card title="License Details">
                                    <>
                                        {values?.license?.length > 0 ? (
                                            <div className="mt-4 space-y-2">
                                                {values.license.map((license, index) => (
                                                    <div key={index} className="flex justify-between items-center p-3 bg-gray-100 rounded-lg">
                                                        <span className="font-medium">{index + 1}. {license.type}</span>
                                                        <span className="text-red-500 cursor-pointer" onClick={() => setFieldValue("license", values.license.filter((item) => item.id !== license.id))}><Icon icon="mdi:delete-outline" width={25} height={25} /></span>
                                                    </div>
                                                ))}
                                                <div className="flex justify-end mt-6">
                                                    <button type="submit" className="px-6 py-2 mt-2 bg-blue-800 text-white rounded-lg hover:bg-blue-700 transition">
                                                        Save License Keys
                                                    </button>
                                                </div>
                                            </div>
                                        ) : (
                                            <div className="bg-gray-100 rounded-md p-4 flex items-center justify-center space-x-2">
                                                <Icon icon="mdi:emoticon-sad-outline" className="text-gray-500 text-2xl" />
                                                <p className="text-gray-600">No License Key Added</p>
                                            </div>
                                        )}


                                    </>
                                </Card>
                            </div>
                        </div>
                    )}

                    {isModalOpen && (
                        <LicenseKeyModal activeModal={isModalOpen} onClose={handleModal} setFieldValue={setFieldValue} serial={checkItem} />
                    )}
                    {
                        isKeyModalOpen && (
                            <ViewLicenseModal activeModal={isKeyModalOpen} onClose={handleViewModal} checkItem={checkItem} />
                        )}
                </Form>
            )}
        </Formik>
    );
};

export default Licensing;
