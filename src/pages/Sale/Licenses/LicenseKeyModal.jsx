import Modal from '@/components/ui/Modal';
import { useGetApiQuery, useGetApiWithIdQuery } from '@/store/api/apihandler/commonSlice';
import { Formik, Form } from 'formik';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import LicenseTable from './LicenseTable';

const LicenseKeyModal = ({ activeModal, onClose, setFieldValue, serial }) => {
    const { data: licenseType } = useGetApiQuery({ url: "license-types", params: { is_active: 1, pagination: 0 } });
    const { data } = useGetApiWithIdQuery(["license-details", serial?.serialId]);

    const tabs = licenseType?.filter((type) =>
        !data?.some(value => value?.license?.license_type?.id === type?.id)
    );

    console.log(tabs);

    const [selectedLicenses, setSelectedLicenses] = useState([]);
    const [selectedLicenseTypeId, setSelectedLicenseTypeId] = useState(null);

    const onSelectItem = (item) => {
        setSelectedLicenses((prev) => {
            const updatedLicenses = prev.some((license) => license.license_type_id === item.license_type_id)
                ? prev.map((license) =>
                    license.license_type_id === item.license_type_id ? { ...item, type: item.license_type.name } : license
                )
                : [...prev, { id: item.id, type: item.license_type.name, license_type_id: item.license_type_id }];

            setFieldValue("license", updatedLicenses);
            return updatedLicenses;
        });
    };

    const handleSaveAndAddMore = () => {
        setFieldValue("license", selectedLicenses.map((license) => license.id));

        const currentIndex = tabs.findIndex((type) => type.id === selectedLicenseTypeId);
        if (currentIndex < tabs.length - 1) {
            setSelectedLicenseTypeId(tabs[currentIndex + 1].id);
        }
    };

    const handleSaveAndClose = () => {
        const licensesToSave = selectedLicenses.map((license) => ({
            id: license.id,
            type: license.type,
        }));

        setFieldValue("license", licensesToSave);
        setFieldValue("product_id", serial?.productId);
        setFieldValue("product_serial_id", serial?.serialId);

        console.log("Updated Form Values:", {
            license: licensesToSave,
            product_id: serial?.productId,
            product_serial_id: serial?.serialId,
        });

        onClose();
    };

    const handleLicenseTypeSelect = (id) => {
        setSelectedLicenseTypeId(id);
    };

    const isLastTab = tabs && selectedLicenseTypeId === tabs[tabs.length - 1]?.id;

    return (
        <Modal title="License Key" onClose={onClose} activeModal={activeModal} className="max-w-5xl min-h-[300px]">
            <Formik
                initialValues={{
                    license: selectedLicenses.map((license) => license.id),
                }}
            >
                {() => (
                    <Form>
                        {tabs?.length > 0 && <div className="p-4">
                            <h3 className="text-lg font-semibold mb-3">Select License Type</h3>
                            <div className="flex flex-wrap gap-4 mb-5">
                                {tabs?.map((type) => (
                                    <div
                                        key={type.id}
                                        className={`cursor-pointer flex items-center p-2 border rounded-lg ${selectedLicenseTypeId === type.id ? "bg-indigo-100" : "bg-gray-50"
                                            }`}
                                        // style={{
                                        //     minWidth: 'max-content',
                                        //     flexBasis: 'auto', 
                                        // }}
                                        onClick={() => handleLicenseTypeSelect(type.id)}
                                    >
                                        <Icon icon={type.icon || "mdi:tag"} className="mr-2" width="20" height="20" />
                                        <span>{type.name}</span>
                                    </div>
                                ))}
                            </div>
                        </div>}

                        <div className="w-full">
                            {selectedLicenseTypeId ? (
                                <div>
                                    <LicenseTable
                                        licenseTypeId={selectedLicenseTypeId}
                                        onSelectItem={onSelectItem}
                                    />
                                    <div className="flex justify-end gap-3 mt-3">
                                        <button
                                            type="button"
                                            onClick={handleSaveAndClose}
                                            className="btn bg-green-500 text-white"
                                        >
                                            Save & Close
                                        </button>
                                        <button
                                            type="button"
                                            onClick={handleSaveAndAddMore}
                                            className="btn bg-indigo-400 text-white"
                                            disabled={isLastTab}
                                        >
                                            Save & Add More
                                        </button>
                                    </div>
                                </div>
                            ) : (
                                <>
                                    {
                                        data?.length > 0 ? <>
                                            <div className="w-full mx-auto p-5 bg-gray-100 rounded-md text-green-600">
                                                {/* <p>Select a License Type to View Keys. The number of tabs can vary depending on the type of keys you added earlier.</p>
                                    <br /> */}
                                                <span className="text- mt-2 block">You've already added these licenses:</span>

                                                <div className="grid grid-cols-2 gap-4 mt-4">
                                                    {data?.map((item) => (
                                                        <div key={item.id} className="flex items-center p-4 bg-white rounded-lg shadow-md border border-gray-200">
                                                            <div className="w-full flex items-center justify-between">
                                                                <div className="flex items-center gap-2">
                                                                    <Icon icon={item?.license?.license_type?.icon} className="text-2xl text-blue-600" />
                                                                    <p className="text-gray-600 font-medium">{item?.license?.license_type?.name}</p>
                                                                </div>
                                                                <p className="text-lg font-semibold text-gray-800">License Key: {item.license.key}</p>
                                                            </div>
                                                        </div>

                                                    ))}
                                                </div>
                                            </div>
                                        </> : <>
                                            <div className="w-full mx-auto p-5 bg-gray-100 rounded-md text-green-600">
                                                <p>Select a License Type to View Keys. </p>
                                            </div>
                                        </>
                                    }

                                </>

                            )}
                        </div>
                    </Form>
                )}
            </Formik>
        </Modal>
    );
};

export default LicenseKeyModal;
