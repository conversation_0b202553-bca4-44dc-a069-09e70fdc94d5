import Modal from '@/components/ui/Modal';
import { useDeleteApiMutation, useGetApiWithIdQuery } from '@/store/api/apihandler/commonSlice';
import { Icon } from '@iconify/react';
import React from 'react';
import { toast } from 'react-toastify';

const DetailRow = ({ label, value }) => (
    <div className="px-3 py-2 bg-gray-100 rounded-md flex items-center justify-between">
        <span className="font-medium text-gray-600">{label}:</span>
        <p className="text-lg font-semibold text-gray-800">{value}</p>
    </div>
);

const ViewLicenseModal = ({ onClose, activeModal, checkItem }) => {
    const [deleteApi] = useDeleteApiMutation();
    const { data } = useGetApiWithIdQuery(["license-details", checkItem?.serialId]);

    const handleSerialDelete = async (id) => {
        try {
            const response = await deleteApi({
                end_point: "/license-assigns/" + id,
                body: {}
            });
            onClose();
            toast.success("Serial Deleted Successfully!");
        } catch (err) {
            toast.error(err);
        }
    }
    return (
        <Modal title="Assigned Keys" onClose={onClose} activeModal={activeModal} className={"max-w-4xl"}>
            <div className="p-6 bg-gray-50">

                {data?.length > 0 ?
                    <>
                        <h2 className="text-2xl font-semibold text-gray-700 mb-6">License Details</h2>
                        <div className="space-y-6">
                            {data?.map((item) => (
                                <div
                                    key={item.id}
                                    className="bg-white rounded-lg shadow-lg p-6 border border-gray-200"
                                >
                                    <div className="flex items-center justify-between mb-4">
                                        <h3 className="text-xl font-semibold text-gray-800 flex items-center space-x-2">
                                            <Icon icon={item.license.license_type.icon} width="24" height="24" />
                                            <span>{item.license.license_type.name}</span>
                                        </h3>
                                        <div>
                                            <button
                                                type="button"
                                                className="bg-red-300 p-1 rounded-md"
                                                onClick={() => handleSerialDelete(item.id)}
                                            >
                                                <Icon icon="mdi:delete-outline" width="24" height="24" className='text-red-500' />
                                            </button>
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-4">
                                        <DetailRow label="License Key" value={item.license.key} />
                                        <DetailRow label="Is Used" value={item.license.is_used ? "Yes" : "No"} />
                                        {/* <DetailRow label="Purchase Price" value={`$${item.license.purchase_price}`} />
                                        <DetailRow label="Sale Price" value={`$${item.license.sale_price}`} /> */}
                                    </div>
                                </div>
                            ))}
                        </div></>
                    :
                    <div className="w-full bg-gray-100 p-6 rounded-lg shadow-lg">
                        <Icon icon="mdi:emoticon-sad" width="48" height="48" className="mx-auto mb-4" />
                        <p className="text-center text-lg font-semibold text-gray-700">No License Key added.</p>
                    </div>
                }
            </div>
        </Modal>
    );
};

export default ViewLicenseModal;