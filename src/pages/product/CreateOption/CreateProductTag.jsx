import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import React from "react";
import { useDispatch } from "react-redux";
import Index from "../../tags/index";

const CreateProductTag = ({ showTagModal, setShowTagModal, data }) => {
  const dispatch = useDispatch();

  return (
    <Modal
      activeModal={showTagModal}
      onClose={() => setShowTagModal(false)}
      title="Add Product Tags"
      className="max-w-xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowTagModal(false)}
        />
      }
    >
      {/* Render the Index component inside the modal */}
      <div className="p-4">
        <Index />
      </div>
    </Modal>
  );
};

export default CreateProductTag;
