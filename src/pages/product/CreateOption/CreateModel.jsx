import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import React, { useState, useEffect } from "react";
import {
  usePostApiMutation,
  useGetApiQuery,
} from "@/store/api/apihandler/commonSlice";
import { useNavigate } from "react-router-dom";
import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import TextAreaField from "@/components/ui/form/TextAreaField";
import {
  categoryInitialValues,
  createValidationSchema,
  isActiveOptions,
} from "@/formHandlers/CategoryCrud";
import { Form, Formik, ErrorMessage } from "formik";
import { toast } from "react-toastify";
import ProgressBar from "@/components/ui/ProgressBar";

const CreateModel = ({ showModel, setShowModel }) => {
  const navigate = useNavigate();

  // Fetch all categories using API query
  const { data: categoriesData } = useGetApiQuery({ url: "categories" });

  // Filter only active categories
  const allCategories = categoriesData?.data.filter(
    (item) => item.is_active === true
  );

  // Convert categories to the required format for InputSelect
  const allCategoryOptions = allCategories?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();

  const handleSubmit = async (values, { resetForm }) => {
    const modifiedValues = { ...values };
    // console.log(values);

    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        }
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const response = await postApi({
        end_point: "categories",
        body: formData,
      }).unwrap();
      // console.log("Submission successful:", response);
      toast.success("Model Created Successfully!");
      // navigate("/create-product");
      setShowModel(false);
      resetForm();
    } catch (err) {
      // console.error("Submission failed:", err);
      setShowModel(false);
      toast.error("Failed in Model Created. Please try again.");
    }
  };

  return (
    <Modal
      activeModal={showModel}
      onClose={() => setShowModel(false)}
      title="Add New Model"
      className="max-w-5xl"
    >
      <>
        <Formik
          initialValues={categoryInitialValues}
          validationSchema={createValidationSchema()}
          onSubmit={handleSubmit}
        >
          {({ values, isSubmitting, setFieldValue }) => {
            return (
              <Form>
                <Card className="shadow-base2">
                  <div className="grid grid-cols-1 md:grid-cols-2  gap-4">
                    <InputField
                      label="Name"
                      name="name"
                      type="text"
                      required
                      placeholder="Enter name"
                    />

                    <div>
                      <InputSelect
                        label="Category"
                        name="parent_id"
                        options={allCategoryOptions}
                        placeholder="Select Category"
                        required
                      />
                      <ErrorMessage
                        name="parent_id"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>
                  </div>
                  <div className="mt-3">
                    <TextAreaField
                      label="Description"
                      name="description"
                      type="text"
                      placeholder="Enter description"
                    />
                  </div>

                  <div className="flex justify-end mt-5 mb-2 gap-3">
                    <Button
                      type="button"
                      className="btn text-center btn-danger"
                      onClick={() => {
                        setShowModel(false);
                        navigate("/create-product");
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="btn text-center btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Submitting..." : "Submit"}
                    </Button>
                  </div>
                </Card>
              </Form>
            );
          }}
        </Formik>
      </>
    </Modal>
  );
};

export default CreateModel;
