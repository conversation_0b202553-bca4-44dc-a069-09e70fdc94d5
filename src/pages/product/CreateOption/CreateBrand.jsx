import React, { useState, useEffect } from "react";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import { toast } from "react-toastify";
import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import {
  brandInitialValues,
  createValidationSchema,
  isActiveOptions,
} from "@/formHandlers/BrandCrud";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate } from "react-router-dom";
import ProgressBar from "@/components/ui/ProgressBar";

const CreateBrand = ({ showBrandModal, setShowBrandModal }) => {
  const navigate = useNavigate();
  const [postApi] = usePostApiMutation();

  const [progress, setProgress] = useState(0);

  // Calculate progress based on filled fields
  const calculateProgress = (values) => {
    const totalFields = 4; // Total number of fields (Name, Image, Description, Is Active)
    let filledFields = 0;

    // Check if fields are filled
    if (values.name) filledFields += 1;
    if (values.image) filledFields += 1;
    if (values.description) filledFields += 1;
    if (values.is_active !== undefined && values.is_active !== "")
      filledFields += 1;

    // Calculate percentage and ensure progress stays between 0 and 100
    const progressPercentage = Math.round((filledFields / totalFields) * 100);
    return progressPercentage;
  };

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        if (values.image instanceof File) {
          formData.append(key, values.image);
        } else if (Array.isArray(values.image)) {
          values.image.forEach((file) => formData.append(key, file));
        }
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const response = await postApi({
        end_point: "brands",
        body: formData,
      }).unwrap();
      toast.success("Product Brand Created Successfully!");
      setShowBrandModal(false);
      resetForm();
    } catch (err) {
      setShowBrandModal(false);
      toast.error("Failed in Product Brand Created. Please try again.");
    }
  };

  return (
    <Modal
      activeModal={showBrandModal}
      onClose={() => setShowBrandModal(false)}
      title="Add New Brand"
      className="max-w-5xl"
    >
      <div>
        <Formik
          initialValues={brandInitialValues}
          validationSchema={createValidationSchema()}
          onSubmit={handleSubmit}
        >
          {({ values, isSubmitting, setFieldValue }) => {
            // Update progress whenever values change
            useEffect(() => {
              setProgress(calculateProgress(values));
            }, [values]);

            return (
              <Form>
                {/* Progress bar */}
                {/* <ProgressBar
                  value={progress}
                  className="bg-success-500"
                  backClass="h-3 rounded-[999px]"
                  showValue
                  animate
                  striped
                /> */}

                <Card className="shadow-base2">
                  <div className="grid grid-cols-1 gap-4">
                    <InputField
                      label="Name"
                      name="name"
                      type="text"
                      required
                      placeholder="Enter brand name"
                    />
                    <TextAreaField
                      label="Description"
                      name="description"
                      type="text"
                      placeholder="Enter description"
                    />
                  </div>
                  {/* <div className="grid grid-cols-2 gap-4 my-3"> */}
                  {/* <InputFile
                      label="Image"
                      name="image"
                      type="file"
                      title="Upload your file"
                      accept="image/*"
                      required
                      onChange={(e) =>
                        setFieldValue("image", e.currentTarget.files[0])
                      }
                    /> */}
                  {/* <InputSelect
                      label="Is Active"
                      name="is_active"
                      options={isActiveOptions}
                      placeholder="Select status"
                      required
                    /> */}
                  {/* </div> */}

                  <div className="flex justify-end mt-5 mb-2 gap-3">
                    <Button
                      type="button"
                      className="btn text-center btn-danger"
                      onClick={() => {
                        setShowBrandModal(false);
                        navigate("/create-product");
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="btn text-center btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Submitting..." : "Submit"}
                    </Button>
                  </div>
                </Card>
              </Form>
            );
          }}
        </Formik>
      </div>
    </Modal>
  );
};

export default CreateBrand;
