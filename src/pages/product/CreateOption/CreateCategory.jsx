import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import React, { useState, useEffect } from "react";
import {
  usePostApiMutation,
  useGetApiQuery,
} from "@/store/api/apihandler/commonSlice";
import { useNavigate } from "react-router-dom";
import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import TextAreaField from "@/components/ui/form/TextAreaField";
import {
  categoryInitialValues,
  createValidationSchema,
  isActiveOptions,
} from "@/formHandlers/CategoryCrud";
import { Form, Formik } from "formik";
import { toast } from "react-toastify";
import ProgressBar from "@/components/ui/ProgressBar";

const CreateCategory = ({ showCategoryModal, setShowCategoryModal }) => {
  const navigate = useNavigate();

  // Fetch all categories using API query
  // const { data: categoriesData } = useGetApiQuery({ url: "categories" });

  // Filter only active categories
  // const allCategories = categoriesData?.data.filter(
  //   (item) => item.is_active === true
  // );

  // Convert categories to the required format for InputSelect
  // const allCategoryOptions = allCategories?.map((item) => ({
  //   label: item.name,
  //   value: item.id,
  // }));

  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();

  const [progress, setProgress] = useState(0);

  // Calculate progress based on filled fields
  const calculateProgress = (values) => {
    const totalFields = 6; // Total number of fields (Name, Image, Description, Is Active)
    let filledFields = 0;

    // Check if fields are filled
    if (values.name) filledFields += 1;
    if (values.image) filledFields += 1;
    if (values.parent_id) filledFields += 1;
    if (values.description) filledFields += 1;
    if (values.parent_id) filledFields += 1;
    if (values.is_active !== undefined && values.is_active !== "")
      filledFields += 1;

    // Calculate percentage and ensure progress stays between 0 and 100
    const progressPercentage = Math.round((filledFields / totalFields) * 100);
    return progressPercentage;
  };

  const handleSubmit = async (values, { resetForm }) => {
    const modifiedValues = { ...values };
    // console.log(values);

    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        }
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const response = await postApi({
        end_point: "categories",
        body: formData,
      }).unwrap();
      // console.log("Submission successful:", response);
      toast.success("Product Category Created Successfully!");
      // navigate("/create-product");
      setShowCategoryModal(false);
      resetForm();
    } catch (err) {
      // console.error("Submission failed:", err);
      setShowCategoryModal(false);
      // toast.error("Submission failed in Product Category. Please try again.");
    }
  };

  return (
    <Modal
      activeModal={showCategoryModal}
      onClose={() => setShowCategoryModal(false)}
      title="Add New Category"
      className="max-w-5xl"
    >
      <>
        <Formik
          initialValues={categoryInitialValues}
          validationSchema={createValidationSchema()}
          onSubmit={handleSubmit}
        >
          {({ values, isSubmitting, setFieldValue }) => {
            // Update progress whenever values change
            useEffect(() => {
              setProgress(calculateProgress(values));
            }, [values]);

            return (
              <Form>
                {/* Progress bar */}
                {/* <ProgressBar
                  value={progress}
                  className="bg-success-500"
                  backClass="h-3 rounded-[999px]"
                  showValue
                  animate
                  striped
                /> */}
                <Card className="shadow-base2">
                  <div className="grid grid-cols-1 gap-4">
                    <>
                      <InputField
                        label="Name"
                        name="name"
                        type="text"
                        required
                        placeholder="Enter name"
                      />
                      <TextAreaField
                        label="Description"
                        name="description"
                        type="text"
                        placeholder="Enter description"
                      />
                    </>

                    {/* <InputFile
                      label="image"
                      name="image"
                      type="file"
                      title="Upload your file"
                      accept="image/*"
                      required
                    /> */}
                    {/* <InputField
                      label="Parent Id"
                      name="parent_id"
                      type="number"
                      required
                      placeholder="Enter Id"
                    /> */}
                    {/* <InputSelect
                      label="Model"
                      name="parent_id"
                      options={allCategoryOptions}
                      placeholder="Select Model"
                    /> */}
                    {/* <InputSelect
                      label="Is Active"
                      name="is_active"
                      options={isActiveOptions}
                      placeholder="Select status"
                      required
                    /> */}
                  </div>
                  {/* <div className="mt-3">
                    <TextAreaField
                      label="Description"
                      name="description"
                      type="text"
                      placeholder="Enter description"
                    />
                  </div> */}

                  <div className="flex justify-end mt-5 mb-2 gap-3">
                    <Button
                      type="button"
                      className="btn text-center btn-danger"
                      onClick={() => {
                        setShowCategoryModal(false);
                        navigate("/create-product");
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="btn text-center btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Submitting..." : "Submit"}
                    </Button>
                  </div>
                </Card>
              </Form>
            );
          }}
        </Formik>
      </>
    </Modal>
  );
};

export default CreateCategory;
