import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import NoImage from "../../assets/CRM/NoImage.png";
import Badge from "@/components/ui/Badge";
// import { useLocation } from "react-router-dom";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import Tooltip from "@/components/ui/Tooltip";
import "suneditor/dist/css/suneditor.min.css";
import { numberToWords } from "./numberToWords";

const DetailsProduct = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  // const location = useLocation();
  // const { product } = location.state || {};

  // console.log(product);

  // Fetch product details by ID
  const { data: productData } = useGetApiQuery({ url: `products/${id}` });
  const product = productData;

  // Fetch all active brands
  const { data: brandsData } = useGetApiQuery({ url: "brands" });
  const allBrands = brandsData?.data?.filter((item) => item.is_active === true);
  const brand = allBrands?.find((item) => item.id === product?.brand_id);
  // console.log("br:", brand);
  const brandName = brand ? brand.name : "Unknown Brand";

  // Fetch all active categories
  const { data: categoriesData } = useGetApiQuery({ url: "categories" });
  const allCategories = categoriesData?.data?.filter(
    (item) => item.is_active === true
  );
  const category = allCategories?.find(
    (item) => item.id === product?.category_id
  );
  const categoryName = category ? category.name : "N/A Category";
  //console.log(categoryName);

  // Fetch subcategories based on the selected category...
  const getSubcategoryOptions = (categoryId) => {
    const selectedCategory = categoriesData?.data?.find(
      (item) => item.id === categoryId
    );
    if (selectedCategory) {
      return selectedCategory?.children.map((child) => ({
        value: child.id,
        label: child.name,
      }));
    }

    return [];
  };

  // console.log(product);

  if (!product) {
    return <div>No product details available.</div>;
  }

  const shortDescription =
    product?.description?.trim() || "No Short Description available.";

  return (
    <>
      <div className="grid gap-4 grid-cols-12 mt-5">
        <div className="xl:col-span-12 col-span-12 space-y-4">
          <Card>
            <div className="flex justify-between items-center">
              <div>
                <span className="font-bold text-lg">
                  Product Details of{" "}
                  <span className="text-primary-500">{product.name}</span>
                </span>
              </div>
              <div className="flex gap-3">
                <div>
                  <Tooltip
                    content="Back to Product List"
                    placement="top"
                    arrow
                    animation="scale"
                  >
                    <div
                      className="m-1"
                      onClick={() => navigate("/product-list")}
                    >
                      <Icon
                        icon="ic:round-arrow-back"
                        className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                      />
                    </div>
                  </Tooltip>
                </div>
                <div>
                  <Tooltip
                    content={`Edit ${product.name} Product`}
                    placement="top"
                    arrow
                    animation="scale"
                  >
                    <div
                      className="m-1"
                      onClick={() => navigate(`/edit-product/${product.id}`)}
                    >
                      <Icon
                        icon="mynaui:edit-one"
                        className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                      />
                    </div>
                  </Tooltip>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
      <div className="grid gap-4 grid-cols-12 mt-5">
        <div className="xl:col-span-5 col-span-12">
          <Card>
            <div className="post-image mb-6">
              <img
                src={
                  product.image
                    ? `${import.meta.env.VITE_MEDIA_URL}/${product.image}`
                    : NoImage
                }
                alt={product.name}
                className="w-full md:h-96 block object-cover"
              />
            </div>
          </Card>
        </div>

        <div className="xl:col-span-7 col-span-12 space-y-">
          <Card>
            {/* Product Name */}
            <>
              <h5 className="card-title text-primary-500 dark:text-white font-bold text-lg my-2">
                {product.name}
              </h5>
            </>
            {/* <div className="grid grid-cols-2 gap-2 ">
              <div>
                <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                  <span className="mr-1 whitespace-nowrap">Brand:</span>
                  <span className="text-slate-900 font-bold">{brandName}</span>
                </div>
              </div>

              <div>
                <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                  <span className="mr-1 whitespace-nowrap">Category:</span>
                  <span className="text-slate-900 font-bold">
                    {categoryName}
                  </span>
                </div>
              </div>

              <div>
                <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                  <span className="mr-1 whitespace-nowrap">Model:</span>
                  <span className="text-slate-900 font-bold">
                    {getSubcategoryOptions(product.category_id).find(
                      (subCategory) =>
                        subCategory.value === product.sub_category_id
                    )?.label || "N/A Model"}
                  </span>
                </div>
              </div>

              <div>
                <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                  <span className="mr-1 whitespace-nowrap">Unit:</span>
                  <span className="text-slate-900 font-bold">
                    {product.unit}
                  </span>
                </div>
              </div>

              <div className="col-span-3 my-2">
                <div className="text-xl font-semibold text-slate-900 dark:text-white my-2">
                  Key Features
                </div>
                <div
                  dangerouslySetInnerHTML={{
                    __html:
                      product.short_description ||
                      "No Short Description available.",
                  }}
                ></div>
              </div>
            </div> */}

            <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-2">
              <div>
                <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                  <span className="mr-1 whitespace-nowrap">Brand:</span>
                  <span className="text-slate-900 font-bold">{brandName}</span>
                </div>
              </div>

              {/* Display the product category name */}
              <div>
                <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                  <span className="mr-1 whitespace-nowrap">Category:</span>
                  <span className="text-slate-900 font-bold">
                    {categoryName}
                  </span>
                </div>
              </div>

              {/* Display the Model name */}
              <div>
                <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                  <span className="mr-1 whitespace-nowrap">Model:</span>
                  <span className="text-slate-900 font-bold">
                    {getSubcategoryOptions(product.category_id).find(
                      (subCategory) =>
                        subCategory.value === product.sub_category_id
                    )?.label || "N/A Model"}
                  </span>
                </div>
              </div>

              {/* Display the Unit name */}
              <div>
                <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                  <span className="mr-1 whitespace-nowrap">Unit:</span>
                  <span className="text-slate-900 font-bold">
                    {product.unit}
                  </span>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 gap-2 my-2">
              {/* Key Features Section */}
              <div className="col-span-2 my-2">
                <div className="text-xl font-semibold text-slate-900 dark:text-white my-2">
                  Key Features
                </div>
                <div
                  dangerouslySetInnerHTML={{
                    __html:
                      product.short_description ||
                      "No Short Description available.",
                  }}
                ></div>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-2 my-2">
              {/* Product Status */}
              <div>
                <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                  <span className="mr-1">Status:</span>
                  <Badge
                    className={
                      product.is_active
                        ? `text-success-500 font-bold text-base`
                        : `text-danger-500 font-bold text-base`
                    }
                  >
                    {product.is_active ? (
                      <>
                        Active
                        <Icon icon="icon-park-solid:correct" className="ml-2" />
                      </>
                    ) : (
                      <>
                        <Icon icon="maki:cross" className="mr-2" />
                        Inactive
                      </>
                    )}
                  </Badge>
                </div>
              </div>
              {/* Product Has Serials */}
              <div>
                <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                  <span className="mr-1">Has Serials:</span>
                  <Badge
                    className={
                      product.has_serials
                        ? `text-success-500 font-bold text-base`
                        : `text-danger-500 font-bold text-base`
                    }
                  >
                    {product.has_serials ? (
                      <>
                        Yes
                        <Icon icon="icon-park-solid:correct" className="ml-2" />
                      </>
                    ) : (
                      <>
                        <Icon icon="maki:cross" className="mr-2" />
                        No
                      </>
                    )}
                  </Badge>
                </div>
              </div>

              {/* Product Has Related */}
              <div>
                <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                  <span className="mr-1">Has Related:</span>
                  <Badge
                    className={
                      product.has_related_products
                        ? `text-success-500 font-bold text-base`
                        : `text-danger-500 font-bold text-base`
                    }
                  >
                    {product.has_related_products ? (
                      <>
                        Yes
                        <Icon icon="icon-park-solid:correct" className="ml-2" />
                      </>
                    ) : (
                      <>
                        <Icon icon="maki:cross" className="mr-2" />
                        No
                      </>
                    )}
                  </Badge>
                </div>
              </div>

              {/* Product Show In Invoices */}
              <div>
                <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                  <span className="mr-1">Shown In Invoices:</span>
                  <Badge
                    className={
                      product.is_description_shown_in_invoices
                        ? `text-success-500 font-bold text-base`
                        : `text-danger-500 font-bold text-base`
                    }
                  >
                    {product.is_description_shown_in_invoices ? (
                      <>
                        Yes
                        <Icon icon="icon-park-solid:correct" className="ml-2" />
                      </>
                    ) : (
                      <>
                        <Icon icon="maki:cross" className="mr-2" />
                        No
                      </>
                    )}
                  </Badge>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
      <div className="grid gap-4 grid-cols-12 mt-5">
        <div className="xl:col-span-12 col-span-12 space-y-4">
          <Card>
            <div className="flex justify-start mb-4">
              <div>
                <div className="text-xl font-semibold text-slate-900 dark:text-white">
                  Product Description
                </div>
              </div>
            </div>

            <div className="card-text mt-4">
              <div
                dangerouslySetInnerHTML={{
                  __html:
                    `<style> 
              table, th, td {
                border: 1px solid black; 
                border-collapse: collapse;
              }
              th, td {
                padding: 8px;
              }
             </style>` + (product.description || "No description available."),
                }}
              ></div>
            </div>
          </Card>
        </div>
      </div>
    </>
  );
};

export default DetailsProduct;
