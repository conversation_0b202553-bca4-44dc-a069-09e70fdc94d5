import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import { useDeleteApiMutation } from "@/store/api/apihandler/commonSlice";
import React from "react";
import { toast } from "react-toastify";
import { useDispatch } from "react-redux";

const deleteRelatedProduct = ({
  showDeleteModal,
  setShowDeleteModal,
  data,
}) => {
  const dispatch = useDispatch();
  const [deleteApi, { isLoading, isError, error, isSuccess }] =
    useDeleteApiMutation();

  const onSubmit = async () => {
    try {
      const response = await deleteApi({
        end_point: "/related-products/" + data?.id,
        body: {},
      });
      console.log(response.name);
      // If successful
      toast.success(`Related Product "${data?.name}" Deleted successfully!`);
      setShowDeleteModal(false);
    } catch (err) {
      // On error
      toast.error(
        `Failed to Related Product "${data?.name}". Please try again.`
      );
    }
  };

  return (
    <Modal
      activeModal={showDeleteModal}
      onClose={() => setShowDeleteModal(false)}
      title="Delete Related Product "
      className="max-w-2xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowDeleteModal(false)}
        />
      }
    >
      <h3 className="text-center">Confirm Deletion</h3>
      <p className="text-center text-slate-500 text-sm mt-4">
        Are you sure you want to delete the Related Product entry{" "}
        <span className="text-primary-500">
          <b>"{data?.name}"</b>
        </span>
        ? This action cannot be undone.
      </p>

      <div className="ltr:text-right rtl:text-left mt-5 gap-4">
        <Button
          type="button"
          className="btn text-center btn-primary mr-4"
          onClick={() => setShowDeleteModal(false)}
        >
          Cancel
        </Button>
        <Button
          isLoading={isLoading}
          type="button"
          className="btn text-center btn-danger"
          onClick={onSubmit}
        >
          Delete
        </Button>
      </div>
    </Modal>
  );
};

export default deleteRelatedProduct;
