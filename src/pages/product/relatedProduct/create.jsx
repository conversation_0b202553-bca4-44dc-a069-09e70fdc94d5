import React, { useState, useEffect } from "react";
import Card from "@/components/ui/Card";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import Button from "@/components/ui/Button";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import { initialValues, validationSchema } from "./formSetting";
import { toast } from "react-toastify";
import Switch from "@/components/ui/Switch";
import DropZone from "@/components/ui/form/DropZone";

const create = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [postApi] = usePostApiMutation();

  // Form submission handler
  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();
    formData.append("name", values.name);
    formData.append("image", values.image);
    formData.append("is_active", 1);
    formData.append("product_id", id);

    try {
      const response = await postApi({
        end_point: "related-products",
        body: formData,
        method: "POST",
      }).unwrap();
      // console.log(response);
      toast.success("Related Product Created successfully!");
      resetForm();
      navigate(`/related-product/${id}`);
    } catch (err) {
      toast.error("Related Product Failed. Please try again.");
    }
  };

  const headerSlotContent = (
    <Tooltip
      content="Back to Related Product List"
      placement="top"
      arrow
      animation="Interactive"
    >
      <div className="m-1" onClick={() => navigate(`/related-product/${id}`)}>
        <Icon
          icon="ion:arrow-back"
          className="w-6 h-6 text-gray-500 cursor-pointer hover:text-primary-500 m-1 hover:border-primary-500"
        />
      </div>
    </Tooltip>
  );

  return (
    <>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ setFieldValue, values, isSubmitting }) => {
          return (
            <Form>
              <Card
                headerslot={headerSlotContent}
                title="Add New Related Product"
                className="w-full"
                titleClass="text-lg font-bold text-gray-800"
              >
                <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                  <Card className="rounded-xl shadow-base2 flex flex-col items-center">
                    <span className="text-xl font-bold text-gray-600">
                      Product Thumbnail
                    </span>
                    <div className="my-4">
                      <DropZone
                        onDrop={(acceptedFile) =>
                          setFieldValue("image", acceptedFile)
                        }
                      />
                    </div>

                    <span className="text-[12px] text-gray-400 text-center">
                      Set the product thumbnail image. Only *.png, *.jpg and
                      *.jpeg image files are accepted
                    </span>
                  </Card>
                </div>

                <div className="grid  gap-4 my-4 ">
                  <InputField
                    label="Name"
                    name="name"
                    type="text"
                    required
                    placeholder="Enter Product Name"
                  />
                </div>

                {/* Submit and Cancel Buttons */}
                <div className="flex justify-end mt-5 mb-2 gap-3">
                  <Button
                    type="button"
                    className="btn text-center btn-danger"
                    onClick={() => navigate(`/related-product/${id}`)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="btn text-center btn-primary"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Submitting..." : "Submit"}
                  </Button>
                </div>
              </Card>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default create;
