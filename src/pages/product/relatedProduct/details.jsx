import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import Tooltip from "@/components/ui/Tooltip";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import NoImage from "../../../assets/CRM/NoImage.png";

const details = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Fetch data from API
  const { data: relatedProduct, isLoading } = useGetApiQuery({
    url: `related-products/${id}`,
  });

  if (isLoading) return <p>Loading...</p>;

  return (
    <>
      <div className="grid gap-4 grid-cols-12 my-5">
        <div className="xl:col-span-12 col-span-12 space-y-4">
          <Card>
            <div className="xl:col-span-12 col-span-12 space-y-4">
              <Card>
                <div className="flex justify-between items-center">
                  <div>
                    <span className="font-bold text-lg">
                      Related Product{" "}
                      <span className="text-primary-500">
                        {relatedProduct?.name}
                      </span>{" "}
                      Details
                    </span>
                  </div>
                  <div className="flex gap-3">
                    <div>
                      <Tooltip
                        content="Back to Related Product List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() =>
                            navigate(-1)
                          }
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </Card>
        </div>
      </div>
      <div className="grid grid-cols-12 gap-5 mb-5">
        <div className="lg:col-span-12 col-span-12">
          <Card>
            <div className="grid gap-4 grid-cols-1 mt-5">
              <Card className="shadow-base2">
                <Card>
                  <div className="post-image mb-6">
                    <img
                      src={
                        relatedProduct.image
                          ? `${import.meta.env.VITE_MEDIA_URL}/${
                              relatedProduct.image
                            }`
                          : NoImage
                      }
                      alt={relatedProduct.name}
                      className="w-full md:h-96 block object-cover"
                    />
                  </div>
                </Card>
              </Card>
            </div>
            <div className="grid gap-4 grid-cols-1 mt-5">
              <Card className="shadow-base2">
                <span className="font-bold text-slate-600 my-4">
                  Related Product Name:
                </span>
                <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                  <span className="flex items-center text-slate-700">
                    {relatedProduct?.name}
                  </span>
                </div>
              </Card>
            </div>
            <Card className="shadow-base2 my-4">
              <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                <span className="mr-1">Status:</span>
                <Badge
                  className={
                    relatedProduct.is_active
                      ? `text-success-500 font-bold text-base`
                      : `text-danger-500 font-bold text-base`
                  }
                >
                  {relatedProduct.is_active ? (
                    <>
                      Active
                      <Icon icon="icon-park-solid:correct" className="ml-2" />
                    </>
                  ) : (
                    <>
                      <Icon icon="maki:cross" className="mr-2" />
                      Inactive
                    </>
                  )}
                </Badge>
              </div>
            </Card>
          </Card>
        </div>
      </div>
    </>
  );
};

export default details;
