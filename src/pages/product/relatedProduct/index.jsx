import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import { useParams } from "react-router-dom";
import NoImage from "../../../assets/CRM/NoImage.png";
import DeleteRelatedProduct from "./deleteRelatedProduct";

const index = () => {
  const { id } = useParams();
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");

  const navigate = useNavigate();

  // Delete Modal State
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  const url = id ? `related-products?product_id=${id}` : "related-products";
  const { data, isLoading, isFetching } = useGetApiQuery({ url: url, params: apiParam });

  const changePage = (value) => setApiParam(value);

  const columns = [
    { label: "Image", field: "image" },
    { label: "Name", field: "name" },
    { label: "Status", field: "is_active" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "View",
      icon: "lets-icons:view-alt",
      onClick: (val) => {
        navigate(
          `/details-related-product/${data?.data[val]?.id}`,
        );
      },
    },
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(
          `/edit-related-product/${data?.data[val]?.id}`,
        );
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const tableData = data?.data?.map((item) => {
    return {
      id: item.id,
      image: (
        <Tooltip
          content={
            <>
              Details of <span className="font-bold">{item.name}</span> Product
            </>
          }
          placement="top"
          arrow
          animation="interactive"
        >
          <div className="inline-block">
            <img
              className="h-14 w-14 border-dashed border-slate-200 border hover:border-2 hover:border-primary-500"
              src={
                item.image
                  ? `${import.meta.env.VITE_MEDIA_URL}/${item.image}`
                  : NoImage
              }
              alt={item.name}
            />
          </div>
        </Tooltip>
      ),
      name: (
        <Tooltip
          content={`Details of ${item.name} Support Ticket`}
          placement="top"
          arrow
          animation="Interactive"
        >
          <button
            className="hover:underline hover:text-primary-600 text-primary-400 font-bold"
          >
            {item.name.length > 30 ? `${item.name.slice(0, 30)}...` : item.name}
          </button>
        </Tooltip>
      ),
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        loading={isLoading || isFetching}
        title="Related Product List"
        columns={columns}
        actions={actions}
        {...(id && {
          goto: (
            <div
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => navigate(`/create-related-product/${id}`)}
            >
              <Icon
                icon="oui:ml-create-single-metric-job"
                className="text-white font-bold"
              />
              Add New Related Product
            </div>
          ),
          gotoLink: `/create-related-product/${id}`,
        })}
        BackButton={
          <div className="flex justify-center items-center">
            <Tooltip
              content="Back to Product List"
              placement="top"
              arrow
              animation="Interactive"
            >
              <div
                className="border border-primary-300 hover:border-primary-500 p-2 text-primary-300 hover:text-primary-500 rounded-md"
                onClick={() => navigate(`/product-list`)}
              >
                <Icon icon="heroicons-outline:arrow-left" />
              </div>
            </Tooltip>
          </div>
        }
        changePage={changePage}
        filter={filter}
        setFilter={setApiParam}
        data={tableData}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
      <DeleteRelatedProduct
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </div>
  );
};

export default index;
