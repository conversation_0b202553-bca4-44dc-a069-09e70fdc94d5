import React from "react";
import Card from "@/components/ui/Card";
import {
  useGetApiWithIdQuery,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";
import { validationSchema } from "./formSetting";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import FileInputForProduct from "@/components/ui/form/FileInputForProduct";
import NoImage from "@/assets/CRM/NoImage.png";

const edit = () => {
  const { id, relatedId } = useParams();
  // console.log(relatedId);
  const navigate = useNavigate();
  const { data: relatedProduct } = useGetApiWithIdQuery([
    "related-products",
    id,
  ]);

  const [updateApi] = useUpdateApiMutation();

  const initialValues = {
    name: relatedProduct?.name || "",
    image: "",
    is_active: relatedProduct?.is_active ? true : false,
  };

  const handleSubmit = async (values, { resetForm, setFieldError }) => {
    let hasErrors = false;

    Object.keys(values).forEach((key) => {
      if (!values[key]) {
        setFieldError(key, "This field is required");
        hasErrors = true;
      } else {
        setFieldError(key, "");
      }
    });

    const formData = new FormData();
    formData.append("name", values.name);
    if (values.image) {
      formData.append("image", values.image);
    }
    formData.append("is_active", values.is_active ? 1 : 0);

    try {
      const data = {
        end_point: `related-products/${id}`,
        body: formData,
      };

      const response = await updateApi(data).unwrap();

      toast.success("Related Product Updated Successfully!");
      resetForm();
      navigate(-1);
    } catch (err) {
      toast.error("Failed to Update Related Product. Please try again.");
    }
  };

  return (
    <div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values, setFieldValue }) => (
          <Form>
            {/* Form fields */}
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-bold text-lg">
                        Edit of{" "}
                        <span className="text-primary-500">{values.name}</span>
                      </span>
                    </div>
                    <div>
                      <Tooltip
                        content="Back to Related Product List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() =>
                            navigate(`/related-product/${relatedId}`)
                          }
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12">
                <FileInputForProduct
                  label="image"
                  // initialImage={
                  //   import.meta.env.VITE_MEDIA_URL + `/${relatedProduct?.image}`
                  // }
                  initialImage={
                    relatedProduct?.image
                      ? import.meta.env.VITE_MEDIA_URL +
                        `/${relatedProduct.image}`
                      : NoImage
                  }
                  name="image"
                  type="file"
                  title="Upload your file"
                  accept="image/*"
                  required
                />
              </div>
              <div className="xl:col-span-8 col-span-12">
                <div className="grid gap-4">
                  <Card>
                    <>
                      <span className="font-bold text-slate-600 my-4">
                        Related Product Name:
                      </span>
                      <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                        <span className="flex items-center text-slate-700">
                          {values.name || "Untitled related product"}
                        </span>
                      </div>
                    </>
                  </Card>

                  <Card>
                    <div className="flex justify-center items-center bg-slate-100 rounded-lg px-3 py-2">
                      <span className="mr-1 font-bold text-slate-600">
                        Status:
                      </span>
                      <Badge
                        className={
                          values.is_active
                            ? `text-success-500 font-bold text-base`
                            : `text-danger-500 font-bold text-base`
                        }
                      >
                        {values.is_active ? (
                          <>
                            Active
                            <Icon
                              icon="icon-park-solid:correct"
                              className="ml-2"
                            />
                          </>
                        ) : (
                          <>
                            <Icon icon="maki:cross" className="mr-2" />
                            Inactive
                          </>
                        )}
                      </Badge>
                    </div>
                  </Card>
                </div>
              </div>

              <div className="xl:col-span-4 col-span-12 space-y-4">
                <Card>
                  <div className="grid grid-cols-1 my-3 gap-3">
                    <InputField
                      label="Related Product Name"
                      name="name"
                      type="text"
                      placeholder="Enter Support Name"
                      required
                    />
                  </div>

                  <div className="my-3">
                    <Switch
                      label="Is Active"
                      activeClass="bg-success-500"
                      name="is_active"
                      value={values.is_active}
                      onChange={() =>
                        setFieldValue("is_active", !values.is_active)
                      }
                    />
                  </div>
                </Card>
              </div>
            </div>

            {/* Submit and Cancel Buttons */}
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-end items-center gap-4">
                    <Button
                      type="button"
                      className="btn text-center btn-danger"
                      onClick={() => navigate(`/related-product/${relatedId}`)}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="btn text-center btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Updating..." : "Update"}
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default edit;
