import React, { useState, useEffect } from "react";
import Card from "@/components/ui/Card";
import FileInputForProduct from "@/components/ui/form/FileInputForProduct";
import InputSelect from "@/components/ui/form/InputSelect";
import {
  useGetApiWithIdQuery,
  useGetApiQuery,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import NoImage from "@/assets/CRM/NoImage.png";
import Switch from "@/components/ui/Switch";
import "react-quill/dist/quill.snow.css";
import { initialValues, upvalidationSchema } from "./formSetting";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import Tooltip from "@/components/ui/Tooltip";
import Button from "@/components/ui/Button";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import { toast } from "react-toastify";
import MultiSelectComponent from "@/components/ui/form/MultiselectComponent";
import { numberToWords } from "./numberToWords";
import BrokenImage from "../../assets/CRM/BrokenImage.svg";

const EditProduct = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [subCategoryOptions, setSubCategoryOptions] = useState([]);

  const { data: product } = useGetApiWithIdQuery(["products", id]);

  // Fetch all categories
  const { data: categoriesData, isLoading: isCategoriesLoading } =
    useGetApiQuery({ url: "categories" });

  // Fetch subcategories based on the selected category...
  const getSubcategoryOptions = (categoryId) => {
    const selectedCategory = categoriesData?.data?.find(
      (item) => item.id === categoryId
    );
    if (selectedCategory) {
      return selectedCategory?.children.map((child) => ({
        value: child.id,
        label: child.name,
      }));
    }

    return [];
  };

  // Fetch all brand
  const { data: brandData, isLoading: isBrandLoading } = useGetApiQuery({
    url: "brands",
  });

  //Unit Status label
  const unitStatus = [
    { value: "kg", label: "Kg" },
    { value: "litre", label: "Litre" },
    { value: "piece", label: "Piece" },
    { value: "box", label: "Box" },
    { value: "pack", label: "Pack" },
    { value: "bottle", label: "Bottle" },
    { value: "can", label: "Can" },
    { value: "dozen", label: "Dozen" },
    { value: "gram", label: "Gram" },
    { value: "milligram", label: "Milligram" },
    { value: "milliliter", label: "Milliliter" },
    { value: "ounce", label: "Ounce" },
    { value: "pint", label: "Pint" },
    { value: "pound", label: "Pound" },
    { value: "ton", label: "Ton" },
    { value: "yard", label: "Yard" },
  ];

  const [updateApi] = useUpdateApiMutation();

  const formattedObject = {
    name: product?.name || "",
    description: product?.description || "",
    short_description: product?.short_description || "",
    image: product?.image || "",
    sku: product?.sku || "",
    barcode: product?.barcode || "",
    regular_price: product?.regular_price || "",
    sale_price: product?.sale_price || "",
    is_active: product?.is_active ? 1 : 0,
    has_serials: product?.has_serials ? 1 : 0,
    is_description_shown_in_invoices: product?.is_description_shown_in_invoices
      ? 1
      : 0,
    has_related_products: product?.has_related_products ? 1 : 0,
    category_id: product?.category_id || "",
    brand_id: product?.brand_id || "",
    unit: product?.unit || "",
    sub_category_id: product?.sub_category_id || "",
  };

  const handleSubmit = async (values, { resetForm, setFieldError }) => {
    let hasErrors = false;

    Object.keys(values).forEach((key) => {
      if (!values[key]) {
        setFieldError(key, "This field is required");
        hasErrors = true;
      } else {
        setFieldError(key, "");
      }
    });

    // Convert boolean fields to integers
    const modifiedValues = {
      ...values,
      is_active: values.is_active ? 1 : 0,
      has_serials: values.has_serials ? 1 : 0,
      is_description_shown_in_invoices: values.is_description_shown_in_invoices
        ? 1
        : 0,
      has_related_products: values.has_related_products ? 1 : 0,
      sub_category_id: values.sub_category_id || 0,
    };

    const formData = new FormData();
    Object.keys(modifiedValues).forEach((key) => {
      if (key === "image") {
        if (modifiedValues.image instanceof File)
          formData.append(key, modifiedValues.image);
      } else {
        formData.append(key, modifiedValues[key]);
      }
    });
    // console.log(modifiedValues);
    try {
      const data = {
        end_point: "products/" + id,
        body: formData,
      };

      await updateApi(data).unwrap();
      resetForm();
      // Show success toast message
      toast.success("Product Updated successfully!");

      navigate("/product-list");
    } catch (err) {
      // console.error("Submission failed:", err);
      // Show error toast message
      toast.error("Failed to Product Updated. Please try again.");
    }
  };

  // short Description
  const editorOptionShort = {
    buttonList: [
      ["undo", "redo"],
      ["font", "fontSize", "formatBlock"],
      ["bold", "italic"],
      ["fontColor", "hiliteColor"],
      ["align", "list", "lineHeight"],
    ],
  };
  // Descriptions
  const editorOptions = {
    buttonList: [
      ["undo", "redo"],
      ["font", "fontSize", "formatBlock"],
      ["bold", "underline", "italic", "strike", "subscript", "superscript"],
      ["fontColor", "hiliteColor"],
      ["outdent", "indent"],
      ["align", "horizontalRule", "list", "lineHeight"],
      ["table", "link", "image"],
    ],
  };

  // Handle subcategories based on selected category
  useEffect(() => {
    if (product?.category_id && categoriesData) {
      const selectedCategory = categoriesData.data.find(
        (category) => category.id === product.category_id
      );
      if (selectedCategory && selectedCategory.children) {
        setSubCategoryOptions(
          selectedCategory.children.map((child) => ({
            label: child.name,
            value: child.id,
          }))
        );
      }
    }
  }, [product?.category_id, categoriesData]);

  return (
    <div>
      <Formik
        initialValues={formattedObject}
        validationSchema={upvalidationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values, setFieldValue }) => (
          <Form>
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-bold text-lg">
                        Edit of{" "}
                        <span className="text-primary-500">{values.name}</span>
                      </span>
                    </div>
                    <div>
                      <Tooltip
                        content="Back to Product List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() => navigate("/product-list")}
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </Card>
              </div>
            </div>

            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-8 col-span-12">
                <Card className="mb-3">
                  <div className="post-image mb-6">
                    <FileInputForProduct
                      label="image"
                      // initialImage={
                      //   import.meta.env.VITE_MEDIA_URL + `/${product?.image}`
                      // }
                      initialImage={
                        product?.image
                          ? import.meta.env.VITE_MEDIA_URL + `/${product.image}`
                          : NoImage
                      }
                      name="image"
                      type="file"
                      title="Upload your file"
                      accept="image/*"
                      required
                      imageWidth="250px"
                      imageHeight="250px"
                    />
                  </div>
                </Card>
                <Card>
                  {/* <div className="hidden md:block"> */}
                  <h5 className="card-title text-primary-500 dark:text-white font-bold text-lg my-2">
                    {values.name || "Untitled Product Name"}
                  </h5>
                  {/* </div> */}

                  <div className="grid grid-cols-2  gap-2 ">
                    <div className="hidden md:block">
                      <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                        <span className="mr-1">Status:</span>
                        <Badge
                          className={
                            values.is_active
                              ? `text-success-500 font-bold text-base`
                              : `text-danger-500 font-bold text-base`
                          }
                        >
                          {values.is_active ? <>In Stock</> : <>Stock Out</>}
                        </Badge>
                      </div>
                    </div>

                    {/* Display the product category name */}
                    <div className="hidden md:block">
                      <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                        <span className="mr-1 whitespace-nowrap">
                          Category:
                        </span>
                        <span className="text-slate-900 font-bold dark:text-white">
                          {categoriesData?.data.find(
                            (category) => category.id === values.category_id
                          )?.name || "No Category Selected"}
                        </span>
                      </div>
                    </div>

                    {/* Display the product brand name */}
                    <div className="hidden md:block">
                      <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                        <span className="mr-1 whitespace-nowrap">Brand:</span>
                        <span className="text-slate-900 font-bold">
                          {brandData?.data.find(
                            (brand) => brand.id === values.brand_id
                          )?.name || "No Brand Selected"}
                        </span>
                      </div>
                    </div>
                    {/* Display the product Unit name */}
                    <div className="hidden md:block">
                      <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                        <span className="mr-1 whitespace-nowrap">Unit:</span>
                        <span className="text-slate-900 font-bold">
                          {values.unit}
                        </span>
                      </div>
                    </div>
                    {/* Display the product Sub Category */}
                    <div className="hidden md:block">
                      <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                        <span className="mr-1 whitespace-nowrap">Model:</span>
                        <span className="text-slate-900 font-bold">
                          {getSubcategoryOptions(values.category_id).find(
                            (subCategory) =>
                              subCategory.value === values.sub_category_id
                          )?.label || "No Modal Selected"}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="col-span-3 my-4">
                    <div className="hidden md:block">
                      <div className="text-xl font-semibold text-slate-900 dark:text-white my-2">
                        Key Features
                      </div>
                      <div
                        dangerouslySetInnerHTML={{
                          __html:
                            values.short_description ||
                            "No Key Features available.",
                        }}
                      ></div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 my-2">
                    {/* Product Has Serials */}
                    <div className="hidden md:block">
                      <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                        <span className="mr-1">Has Serials:</span>
                        <Badge
                          className={
                            values.has_serials
                              ? `text-success-500 font-bold text-base`
                              : `text-danger-500 font-bold text-base`
                          }
                        >
                          {values.has_serials ? (
                            <>
                              Yes
                              <Icon
                                icon="icon-park-solid:correct"
                                className="ml-2"
                              />
                            </>
                          ) : (
                            <>
                              <Icon icon="maki:cross" className="mr-2" />
                              No
                            </>
                          )}
                        </Badge>
                      </div>
                    </div>
                    {/* Product Has Related */}
                    <div className="hidden md:block">
                      <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                        <span className="mr-1">Product Has Related :</span>
                        <Badge
                          className={
                            values.has_related_products
                              ? `text-success-500 font-bold text-base`
                              : `text-danger-500 font-bold text-base`
                          }
                        >
                          {values.has_related_products ? (
                            <>
                              Yes
                              <Icon
                                icon="icon-park-solid:correct"
                                className="ml-2"
                              />
                            </>
                          ) : (
                            <>
                              <Icon icon="maki:cross" className="mr-2" />
                              No
                            </>
                          )}
                        </Badge>
                      </div>
                    </div>
                    {/* Product Shown In Invoices : */}
                    <div className="hidden md:block">
                      <div className="flex justify-center items-center bg-slate-100 rounded-full px-3 py-2">
                        <span className="mr-1">Shown In Invoices :</span>
                        <Badge
                          className={
                            values.is_description_shown_in_invoices
                              ? `text-success-500 font-bold text-base`
                              : `text-danger-500 font-bold text-base`
                          }
                        >
                          {values.is_description_shown_in_invoices ? (
                            <>
                              Yes
                              <Icon
                                icon="icon-park-solid:correct"
                                className="ml-2"
                              />
                            </>
                          ) : (
                            <>
                              <Icon icon="maki:cross" className="mr-2" />
                              No
                            </>
                          )}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>

              <div className="xl:col-span-4 col-span-12 space-y-4">
                <Card>
                  <div className="grid grid-cols-1 gap-3">
                    {/* <FileInputForProduct
                      label="image"
                      initialImage={
                        product?.image
                          ? import.meta.env.VITE_MEDIA_URL + `/${product.image}`
                          : NoImage
                      }
                      name="image"
                      type="file"
                      title="Upload your file"
                      accept="image/*"
                    /> */}
                    <InputField
                      label="Product Name"
                      name="name"
                      type="text"
                      placeholder="Enter Product Name"
                      required
                    />
                    <div>
                      <label className="block text-gray-600 font-bold mb-2">
                        Key Features
                      </label>
                      <SunEditor
                        setOptions={editorOptionShort}
                        setContents={values.short_description || ""}
                        onChange={(value) =>
                          setFieldValue("short_description", value)
                        }
                        placeholder="Enter Features Description"
                        height="200px"
                      />
                    </div>
                  </div>

                  {/* Brand Select */}
                  <div className="my-2">
                    <InputSelect
                      label="Brand"
                      name="brand_id"
                      placeholder="Select Brand"
                      options={
                        isBrandLoading
                          ? []
                          : brandData?.data.map((brand) => ({
                              value: brand.id,
                              label: brand.name,
                            }))
                      }
                      value={values.brand_id}
                      onChange={(value) => setFieldValue("brand_id", value)}
                      required
                    />
                  </div>
                  {/* Category Select */}
                  <div className="my-2">
                    <InputSelect
                      label="Category"
                      name="category_id"
                      options={categoriesData?.data.map((category) => ({
                        value: category.id,
                        label: category.name,
                      }))}
                      value={values.category_id}
                      onChange={(value) => {
                        setFieldValue("category_id", value);
                        const selectedCategory = categoriesData.data.find(
                          (category) => category.id === value
                        );
                        setSubCategoryOptions(
                          selectedCategory?.children?.map((child) => ({
                            label: child.name,
                            value: child.id,
                          })) || []
                        );
                        setFieldValue("sub_category_id", "");
                      }}
                      required
                    />
                  </div>
                  {/* sub category */}
                  <div className="my-2">
                    <InputSelect
                      key={values.category_id}
                      className="w-full"
                      label="Model"
                      name="sub_category_id"
                      value={values.sub_category_id}
                      options={getSubcategoryOptions(values.category_id)}
                      placeholder="Select Model"
                    />
                  </div>

                  <div className="my-2">
                    <InputSelect
                      label="Unit"
                      name="unit"
                      placeholder="Select Unit"
                      options={unitStatus}
                      value={values.unit || ""}
                      onChange={(e) => setFieldValue("unit", e.target.value)}
                      required
                    />
                  </div>

                  <div className="my-4">
                    <Switch
                      label="Is Active"
                      activeClass="bg-success-500"
                      name="is_active"
                      value={values.is_active}
                      onChange={() =>
                        setFieldValue("is_active", !values.is_active)
                      }
                    />
                  </div>
                  <div className="my-3">
                    <Switch
                      label="Has Serials"
                      activeClass="bg-success-500"
                      value={values.has_serials}
                      name="has_serials"
                      onChange={() =>
                        setFieldValue("has_serials", !values.has_serials)
                      }
                    />
                  </div>
                  <div className="my-3">
                    <Switch
                      label="Has Related Products"
                      activeClass="bg-success-500"
                      value={values.has_related_products}
                      name="has_related_products"
                      onChange={() =>
                        setFieldValue(
                          "has_related_products",
                          !values.has_related_products
                        )
                      }
                    />
                  </div>
                  <div className="my-3">
                    <Switch
                      label="Is Feature Shown In Invoices"
                      activeClass="bg-success-500"
                      value={values.is_description_shown_in_invoices}
                      name="is_description_shown_in_invoices"
                      onChange={() =>
                        setFieldValue(
                          "is_description_shown_in_invoices",
                          !values.is_description_shown_in_invoices
                        )
                      }
                    />
                  </div>
                </Card>
              </div>
            </div>

            <div className="grid  my-5">
              <div className="xl:col-span-6 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-start mb-4">
                    <div>
                      <div className="text-xl text-slate-900 dark:text-white">
                        Product Description
                      </div>
                    </div>
                  </div>

                  <div className="card-text mt-4">
                    <div
                      dangerouslySetInnerHTML={{
                        __html:
                          `<style> 
              table, th, td {
                border: 1px solid black; 
                border-collapse: collapse;
              }
              th, td {
                padding: 8px;
              }
             </style>` + (values.description || "No description available."),
                      }}
                    ></div>
                  </div>
                </Card>
              </div>
              <div className="xl:col-span-6 col-span-12 space-y-4 mt-3">
                <Card>
                  <div>
                    <label className="block text-gray-600 font-bold mb-2">
                      Description
                    </label>
                    <SunEditor
                      setOptions={editorOptions}
                      setContents={values.description || ""}
                      onChange={(value) => setFieldValue("description", value)}
                      placeholder="Enter Description"
                      height="50px"
                    />
                  </div>
                </Card>
              </div>
            </div>

            {/* Submit and Cancel Buttons */}
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-end items-center gap-4">
                    <Button
                      type="button"
                      className="btn text-center btn-danger"
                      onClick={() => navigate("/product-list")}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="btn text-center btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Submitting..." : "Submit"}
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditProduct;
