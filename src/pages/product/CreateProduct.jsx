import React, { useState, useEffect } from "react";
import Card from "@/components/ui/Card";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik, ErrorMessage } from "formik";
import { useNavigate } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import InputSelect from "@/components/ui/form/InputSelect";
import Button from "@/components/ui/Button";
import DropZone from "@/components/ui/form/DropZone";
import Switch from "@/components/ui/Switch";
import { numberToWords } from "./numberToWords";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import { initialValues, validationSchema } from "./formSetting";
import ProgressBar from "@/components/ui/ProgressBar";
import Bar from "@/components/ui/ProgressBar/Bar";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import { toast } from "react-toastify";
import CreateTag from "./CreateOption/CreateProductTag";
import CreateCategory from "./CreateOption/CreateCategory";
import CreateBrand from "./CreateOption/CreateBrand";
import MultiSelectComponent from "@/components/ui/form/MultiselectComponent";
import CreateModel from "./CreateOption/CreateModel";

const CreateProduct = () => {
  const navigate = useNavigate();

  const [progress, setProgress] = useState(0);

  // State to store selected category
  // const [selectedCategory, setSelectedCategory] = useState(null);
  // const [selectedCategory, setSelectedCategory] = useState(null);
  // const [subCategoryOptions, setSubCategoryOptions] = useState([]);

  // product tags modals
  const [showTagModal, setShowTagModal] = useState(false);
  const [tagData, setTagData] = useState(null);

  // product category modals
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [categoryData, setCategoryData] = useState(null);

  // product brand modals
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [brandData, setBrandData] = useState(null);

  // product modal modals
  const [showModel, setShowModel] = useState(false);
  const [modelData, setModelData] = useState(null);

  // Fetch all categories using API query
  const { data: categoriesData } = useGetApiQuery({ url: "categories" });
  //console.log("sub", categoriesData);
  // Filter only active categories
  const allCategories = categoriesData?.data.filter(
    (item) => item.is_active === true
  );
  // Convert categories to the required format for InputSelect
  const allCategoryOptions = allCategories?.map((item) => ({
    label: item.name,
    value: item.id,
  }));
  //console.log(allCategoryOptions);

  // Fetch subcategories based on the selected category...
  const getSubcategoryOptions = (categoryId) => {
    const selectedCategory = categoriesData?.data?.find(
      (item) => item.id === categoryId
    );
    if (selectedCategory) {
      return selectedCategory?.children.map((child) => ({
        value: child.id,
        label: child.name,
      }));
    }

    return [];
  };

  // Fetch all brands (optional if needed for other fields)
  const { data: brandsData } = useGetApiQuery({ url: "brands" });
  const allBrands = brandsData?.data.filter((item) => item.is_active === true);
  const allBrandOptions = allBrands?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  const [postApi] = usePostApiMutation();

  //Unit Status label
  const unitStatus = [
    { value: "kg", label: "Kg" },
    { value: "litre", label: "Litre" },
    { value: "piece", label: "Piece" },
    { value: "box", label: "Box" },
    { value: "pack", label: "Pack" },
    { value: "bottle", label: "Bottle" },
    { value: "can", label: "Can" },
    { value: "dozen", label: "Dozen" },
    { value: "gram", label: "Gram" },
    { value: "milligram", label: "Milligram" },
    { value: "milliliter", label: "Milliliter" },
    { value: "ounce", label: "Ounce" },
    { value: "pint", label: "Pint" },
    { value: "pound", label: "Pound" },
    { value: "ton", label: "Ton" },
    { value: "yard", label: "Yard" },
  ];

  // Function to calculate form progress
  const calculateProgress = (values) => {
    const totalFields = Object.keys(initialValues).length;
    let filledFields = 0;

    // Loop through form values and count filled fields
    Object.keys(values).forEach((key) => {
      if (values[key] && values[key] !== "") {
        filledFields++;
      }
    });

    // Calculate the progress percentage
    return Math.round((filledFields / totalFields) * 100);
  };

  // Update progress whenever form values change
  useEffect(() => {
    setProgress(calculateProgress(initialValues));
  }, [initialValues]);

  // Form submission handler
  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    // Convert the selected tags (array of objects) into the required format
    // const selectedTags = values.tags.map((tag) => ({
    //   label: tag.label,
    //   value: tag.value,
    // }));

    // Convert boolean values to integers
    const transformedValues = {
      ...values,
      // tags: selectedTags,
      has_serials: values.has_serials ? 1 : 0,
      is_description_shown_in_invoices: values.is_description_shown_in_invoices
        ? 1
        : 0,
      has_related_products: values.has_related_products ? 1 : 0,
      is_active: values.is_active ? 1 : 0,
      sub_category_id: values.sub_category_id ? values.sub_category_id : 0,
    };

    // Append form values to FormData, excluding image if not available
    // Object.keys(transformedValues).forEach((key) => {
    //   if (key === "image" && transformedValues.image instanceof File) {
    //     formData.append(key, transformedValues.image);
    //   } else {
    //     if (key === "tags") {
    //       formData.append(key, JSON.stringify(transformedValues[key])); // Convert tags array to JSON string
    //     } else {
    //       formData.append(key, transformedValues[key]);
    //     }
    //   }
    // });

    // Append form values to FormData, excluding null or undefined fields
    Object.keys(transformedValues).forEach((key) => {
      if (key === "image") {
        // Check if image is available and is a File
        if (
          transformedValues.image &&
          transformedValues.image instanceof File
        ) {
          formData.append(key, transformedValues.image);
        }
      } else {
        // Only append if the value is not null or undefined
        if (
          transformedValues[key] !== null &&
          transformedValues[key] !== undefined
        ) {
          formData.append(key, transformedValues[key]);
        }
      }
    });

    try {
      const response = await postApi({
        end_point: "products",
        body: formData,
      }).unwrap();
      toast.success("Product Added Successfully!");
      navigate("/product-list");
      resetForm();
    } catch (err) {
      // console.error("Submission failed:", err);
      // toast.error("Product Added Failed. Please try again.");
    }
  };

  // short Description
  const editorOptionShort = {
    buttonList: [
      ["undo", "redo"],
      ["font", "fontSize", "formatBlock"],
      ["bold", "italic"],
      ["fontColor", "hiliteColor"],
      ["align", "list", "lineHeight"],
    ],
  };

  //Details Description
  const editorOptions = {
    buttonList: [
      ["undo", "redo"],
      ["font", "fontSize", "formatBlock"],
      ["bold", "underline", "italic", "strike", "subscript", "superscript"],
      ["fontColor", "hiliteColor"],
      ["outdent", "indent"],
      ["align", "horizontalRule", "list", "lineHeight"],
      ["table", "link", "image"],
    ],
  };

  const headerSlotContent = (
    <Tooltip
      content="Back to Product List"
      placement="top"
      arrow
      animation="Interactive"
    >
      <div className="m-1" onClick={() => navigate("/product-list")}>
        <Icon
          icon="ion:arrow-back"
          className="w-6 h-6 text-gray-500 cursor-pointer hover:text-primary-500 m-1 hover:border-primary-500"
        />
      </div>
    </Tooltip>
  );

  return (
    <>
      <Formik
        initialValues={initialValues}
        enableReinitialize
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ setFieldValue, values, isSubmitting }) => {
          useEffect(() => {
            setProgress(calculateProgress(values));
          }, [values]);

          return (
            <Form>
              {/* {console.log(values)} */}
              <Card
                headerslot={headerSlotContent}
                title="Add New Product"
                className="w-full"
                titleClass="text-lg font-bold text-gray-800"
              >
                <div className="grid grid-cols-12 gap-4">
                  <div className="col-span-12 md:col-span-4 space-y-4">
                    {/* Product Thumbnail */}
                    <Card className="rounded-xl p-3 shadow-base2">
                      <span className="text-xl font-bold text-gray-600">
                        Product Thumbnail
                      </span>
                      <div className="my-4">
                        <DropZone
                          onDrop={(acceptedFile) =>
                            setFieldValue("image", acceptedFile)
                          }
                        />
                      </div>

                      <span className="text-[12px] text-gray-400">
                        Set the product thumbnail image. Only *.png, *.jpg and
                        *.jpeg image files are accepted
                      </span>
                    </Card>

                    {/* Product Variations */}
                    <Card className="rounded-xl p-3 shadow-base2">
                      <span className="text-xl font-bold text-gray-600 my-2">
                        Product Variations
                      </span>
                      <div className="grid md:grid-cols-1 gap-4 my-3">
                        <div>
                          <div className="flex items-center justify-between">
                            <InputSelect
                              label="Brand"
                              className="w-full"
                              name="brand_id"
                              options={allBrandOptions}
                              placeholder="Select Brand"
                              required
                            />
                            <Tooltip
                              content="Add New Brand"
                              placement="top"
                              arrow
                              animation="Interactive"
                            >
                              <div
                                className="ml-3 mt-8 flex-shrink-0"
                                onClick={() => {
                                  setBrandData(null);
                                  setShowBrandModal(true);
                                }}
                              >
                                <Icon
                                  icon="oui:ml-create-single-metric-job"
                                  className="w-6 h-6 font-bold text-gray-400 cursor-pointer hover:text-primary-400"
                                />
                              </div>
                            </Tooltip>
                          </div>
                          <ErrorMessage
                            name="brand_id"
                            component="div"
                            className="text-red-500 text-sm mt-1"
                          />
                        </div>
                        <div className="">
                          <div className="flex items-center justify-between ">
                            <InputSelect
                              className="w-full"
                              label="Category"
                              name="category_id"
                              options={allCategoryOptions}
                              placeholder="Select Category"
                              required
                            />

                            <Tooltip
                              content="Add New Category"
                              placement="top"
                              arrow
                              animation="Interactive"
                            >
                              <div
                                className="ml-3 mt-8 flex-shrink-0"
                                onClick={() => {
                                  setCategoryData(null);
                                  setShowCategoryModal(true);
                                }}
                              >
                                <Icon
                                  icon="oui:ml-create-single-metric-job"
                                  className="w-6 h-6 font-bold text-gray-400 cursor-pointer hover:text-primary-400"
                                />
                              </div>
                            </Tooltip>
                          </div>
                          <ErrorMessage
                            name="category_id"
                            component="div"
                            className="text-red-500 text-sm mt-1"
                          />
                        </div>

                        <div className="">
                          <div className="flex items-center justify-between ">
                            <InputSelect
                              key={values.category_id}
                              className="w-full"
                              label="Model"
                              name="sub_category_id"
                              options={getSubcategoryOptions(
                                values.category_id
                              )}
                              placeholder="Select Model"
                              required
                            />

                            <Tooltip
                              content="Add New Model"
                              placement="top"
                              arrow
                              animation="Interactive"
                            >
                              <div
                                className="ml-3 mt-8 flex-shrink-0"
                                onClick={() => {
                                  setModelData(null);
                                  setShowModel(true);
                                }}
                              >
                                <Icon
                                  icon="oui:ml-create-single-metric-job"
                                  className="w-6 h-6 font-bold text-gray-400 cursor-pointer hover:text-primary-400"
                                />
                              </div>
                            </Tooltip>
                          </div>
                          <ErrorMessage
                            name="sub_category_id"
                            component="div"
                            className="text-red-500 text-sm mt-1"
                          />
                        </div>

                        {/* <div>
                          <InputSelect
                            key={values.category_id}
                            className="w-full"
                            label="Model"
                            name="sub_category_id"
                            options={getSubcategoryOptions(values.category_id)}
                            placeholder="Select Model"
                          />
                        </div> */}

                        <div className="mb-4">
                          <InputSelect
                            label="Unit"
                            id="unit"
                            name="unit"
                            value={values.unit}
                            onChange={(e) =>
                              setFieldValue("unit", e.target.value)
                            }
                            options={unitStatus}
                            placeholder="Select Unit"
                            required
                          />
                          <ErrorMessage
                            name="unit"
                            component="div"
                            className="text-red-500 text-sm mt-1"
                          />
                        </div>
                      </div>
                    </Card>
                  </div>
                  {/* Right Column */}
                  <div className="col-span-12 md:col-span-8 space-y-4">
                    <Card className="rounded-xl p-3 shadow-base2">
                      <span className="text-xl font-bold text-gray-600 my-2">
                        General Information
                      </span>
                      <div className="grid md:grid-cols-1 gap-4 my-3">
                        <InputField
                          label="Product Name"
                          name="name"
                          type="text"
                          required
                          placeholder="Enter Product Name"
                        />
                        <div className="mb-4">
                          <label className="block text-gray-600 font-bold mb-2">
                            Key Features
                          </label>
                          <SunEditor
                            setOptions={editorOptionShort}
                            value={values.short_description}
                            onChange={(value) =>
                              setFieldValue("short_description", value)
                            }
                            placeholder="Enter Features Description"
                            height="100px"
                          />
                          <ErrorMessage
                            name="short_description"
                            component="div"
                            className="text-red-500 text-sm mt-1"
                          />
                        </div>
                        <div className="mb-4">
                          <label className="block text-gray-600 font-bold mb-2">
                            Description
                          </label>
                          <SunEditor
                            setOptions={editorOptions}
                            value={values.description}
                            onChange={(value) =>
                              setFieldValue("description", value)
                            }
                            placeholder="Enter Description"
                            height="50px"
                          />
                          <ErrorMessage
                            name="description"
                            component="div"
                            className="text-red-500 text-sm mt-1"
                          />
                        </div>
                      </div>
                    </Card>

                    <Card className="rounded-xl p-3 shadow-base2">
                      <span className="text-xl font-bold text-gray-600 my-2">
                        Product Details
                      </span>
                      <div className="grid md:grid-cols-1 gap-4 my-3">
                        <Switch
                          label="Is Active"
                          activeClass="bg-success-500"
                          name="is_active"
                          value={values.is_active}
                          onChange={() =>
                            setFieldValue("is_active", !values.is_active)
                          }
                        />
                        <Switch
                          label="Has Serials"
                          activeClass="bg-success-500"
                          value={values.has_serials}
                          name="has_serials"
                          onChange={() =>
                            setFieldValue("has_serials", !values.has_serials)
                          }
                        />
                        <Switch
                          label="Has Related Products"
                          activeClass="bg-success-500"
                          value={values.has_related_products}
                          name="has_related_products"
                          onChange={() =>
                            setFieldValue(
                              "has_related_products",
                              !values.has_related_products
                            )
                          }
                        />
                        <Switch
                          label="Is Feature Shown In Invoices"
                          activeClass="bg-success-500"
                          value={values.is_description_shown_in_invoices}
                          name="is_description_shown_in_invoices"
                          onChange={() =>
                            setFieldValue(
                              "is_description_shown_in_invoices",
                              !values.is_description_shown_in_invoices
                            )
                          }
                        />
                      </div>
                    </Card>
                  </div>

                  {/* <div className="col-span-12 md:col-span-1 space-y-4 hidden md:block empty:top-2">
                    <Card className="rounded-xl shadow-base2 fixed top-1/2 transform -translate-y-1/2 mt-14 mb-20">
                      <div className="w-full h-96 flex items-end justify-center">
                        <div className="w-8 h-full bg-gray-300 rounded-[999px] overflow-hidden relative">
                          <div
                            className="absolute bottom-0 bg-slate-800 text-white transition-all duration-300 flex items-center justify-center"
                            style={{
                              height: `${progress}%`,
                              width: "100%",
                              background:
                                progress >= 80
                                  ? "linear-gradient(90deg, #32CD32, #00FF00)"
                                  : "#334155",
                            }}
                          >
                            <span
                              className="text-[10px] text-white absolute  transform"
                              style={{
                                top: `${100 - 50}%`,
                                transform: "translateY(0%)",
                              }}
                            >
                              {progress}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </div> */}
                </div>

                {/* Submit and Cancel Buttons */}
                <div className="flex justify-end mt-5 mb-2 gap-3">
                  <Button
                    type="button"
                    className="btn text-center btn-danger"
                    onClick={() => navigate("/product-list")}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="btn text-center btn-primary"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Submitting..." : "Submit"}
                  </Button>
                </div>
              </Card>
            </Form>
          );
        }}
      </Formik>

      {/* Create Product Tag */}
      <CreateTag
        showTagModal={showTagModal}
        setShowTagModal={setShowTagModal}
        data={tagData}
      />
      {/* Create Product Category */}
      <CreateCategory
        showCategoryModal={showCategoryModal}
        setShowCategoryModal={setShowCategoryModal}
        data={categoryData}
      />
      {/* Create Product Brand */}
      <CreateBrand
        showBrandModal={showBrandModal}
        setShowBrandModal={setShowBrandModal}
        data={brandData}
      />
      {/* Create Model modals */}
      <CreateModel
        showModel={showModel}
        setShowModel={setShowModel}
        data={modelData}
      />
    </>
  );
};

export default CreateProduct;
