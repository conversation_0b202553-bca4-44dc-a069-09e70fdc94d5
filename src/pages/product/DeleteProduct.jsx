import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import { useDeleteApiMutation } from "@/store/api/apihandler/commonSlice";
import React from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";

const DeleteProduct = ({ showDeleteModal, setShowDeleteModal, data }) => {
  const dispatch = useDispatch();
  const [deleteApi, { isLoading, isError, error, isSuccess }] =
    useDeleteApiMutation();
  // const onSubmit = async () => {
  //   const response = await deleteApi({
  //     end_point: "/products/" + data?.id,
  //     body: {},
  //   });
  //   console.log(response);
  //   setShowDeleteModal(false);
  // };
  const onSubmit = async () => {
    try {
      const response = await deleteApi({
        end_point: "/products/" + data?.id,
        body: {},
      });
      toast.success(`Product "${data?.name}" has been Successfully Deleted!`);
      setShowDeleteModal(false);
    } catch (err) {
      toast.error(
        `Failed to Deleted Product "${data?.name}". Please try again.`
      );
    }
  };
  return (
    <Modal
      activeModal={showDeleteModal}
      onClose={() => setShowDeleteModal(false)}
      title="Delete Product"
      className="max-w-2xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowDeleteModal(false)}
        />
      }
    >
      <h3 className="text-center">Are you sure?</h3>
      <p className="text-center text-slate-500 text-sm mt-4">
        You are going delete{" "}
        <span className="text-primary-500">
          <b>" {data?.name} "</b>{" "}
        </span>
        Product. Once you have done this, There is no going back.{" "}
      </p>

      <div className="ltr:text-right rtl:text-left mt-5 gap-4">
        <Button
          type="button"
          className="btn text-center btn-outline-primary mr-4"
          onClick={() => setShowDeleteModal(false)}
        >
          Cancel
        </Button>
        <Button
          isLoading={isLoading}
          type="button"
          className="btn text-center btn-danger"
          onClick={onSubmit}
        >
          Delete
        </Button>
      </div>
    </Modal>
  );
};

export default DeleteProduct;
