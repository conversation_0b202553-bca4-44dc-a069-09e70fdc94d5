export const numberToWords = (num) => {
    if (!num) return 'Zero Taka Only';

    const singleDigits = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
    const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
    const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
    const scales = ['Thousand', 'Lac', 'Crore'];

    // Remove commas and split integer and decimal parts
    const [integerPart, decimalPart] = num.toString().replace(/,/g, '').split('.');

    const convertHundreds = (n) => {
        let result = '';
        if (n > 99) {
            result += `${singleDigits[Math.floor(n / 100)]} Hundred `;
            n %= 100;
        }
        if (n > 19) {
            result += `${tens[Math.floor(n / 10)]} `;
            n %= 10;
        }
        if (n > 9) {
            result += `${teens[n - 10]} `;
        } else if (n > 0) {
            result += `${singleDigits[n]} `;
        }
        return result.trim();
    };

    const convertToWords = (n) => {
        let wordResult = '';
        const chunks = [];

        // Break number into chunks of 1000 for scales
        chunks.push(n % 1000);
        n = Math.floor(n / 1000);

        chunks.push(n % 100);
        n = Math.floor(n / 100);

        if (n > 0) {
            chunks.push(n);
        }

        for (let i = 0; i < chunks.length; i++) {
            if (chunks[i] > 0) {
                let scaleName = i === 0 ? '' : scales[i - 1];
                wordResult = `${convertHundreds(chunks[i])} ${scaleName} ${wordResult}`.trim();
            }
        }

        return wordResult.trim();
    };

    const integerWords = convertToWords(parseInt(integerPart, 10));
    let result = `${integerWords} Taka`;

    if (decimalPart && parseInt(decimalPart, 10) > 0) {
        const decimalWords = convertToWords(parseInt(decimalPart, 10));
        result += ` and ${decimalWords} Paisa`;
    }

    return `${result} Only.`;
};
