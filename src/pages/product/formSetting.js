import * as yup from "yup";

// Initial values for form fields
export const initialValues = {
    name: "",
    short_description: "",
    description: "",
    image: null,
    unit: "",
    barcode: "",
    sku: "",
    category_id: "",
    sub_category_id: '',
    brand_id: "",
    regular_price: "",
    sale_price: "",
    has_serials: false,
    is_description_shown_in_invoices: false,
    has_related_products: false,
    is_active: true,
};

// Validation schema for form fields
export const validationSchema = yup.object({
    name: yup
        .string()
        // .min(1, "At least 1 characters are required for the product name.")
        .max(255, "Product name should not exceed 255 characters")
        .required("The product name is required"),


    category_id: yup.string().required("The Category name must be specified"),

    brand_id: yup.string().required("The Brand name must be specified"),

    unit: yup
        .string()
        .required("The Unit name must be specified"),

    sub_category_id: yup
        .string()
        .required("The Modal must be specified"),

});


// Initial values for form fields
export const upinitialValues = {
    name: "",
    short_description: "",
    description: "",
    image: null,
    barcode: "",
    sku: "",
    regular_price: "",
    sale_price: "",
    has_serials: false,
    is_description_shown_in_invoices: false,
    has_related_products: false,
    is_active: false,
    unit: "",
    category_id: "",
};

// Validation schema for form fields
export const upvalidationSchema = yup.object({
    name: yup
        .string()
        .max(255, "Product name should not exceed 255 characters")
        .required("Product name is required"),
});
