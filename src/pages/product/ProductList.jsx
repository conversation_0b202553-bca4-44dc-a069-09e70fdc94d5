import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import DeleteProduct from "./DeleteProduct";
import NoImage from "@/assets/CRM/NoImage.png";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import { numberToWords } from "./numberToWords";

const ProductList = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery({ url: "products", params: apiParam });
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Image", field: "image" },
    { label: "Name", field: "name" },
    { label: "Related Product", field: "relatedProduct" },
    { label: "Has Serials", field: "has_serials" },
    { label: "Status", field: "is_active" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "View",
      icon: "lets-icons:view-alt",
      onClick: (val) => {
        navigate(`/details-product/${data?.data[val]?.id}`, {
          state: { product: data?.data[val] },
        });
      },
    },
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/edit-product/${data?.data[val]?.id}`, {
          state: { product: data?.data[val] },
        });
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      image: (
        <Tooltip
          content={
            <>
              Details of <span className="font-bold">{item.name}</span> Product
            </>
          }
          placement="top"
          arrow
          animation="interactive"
        >
          <div className="inline-block">
            <img
              onClick={() =>
                navigate(`/details-product/${item.id}`, {
                  state: { product: item },
                })
              }
              className="h-14 w-14 border-dashed border-slate-200 border hover:border-2 hover:border-primary-500"
              src={
                item.image
                  ? `${import.meta.env.VITE_MEDIA_URL}/${item.image}`
                  : NoImage
              }
              alt={item.name}
            />
          </div>
        </Tooltip>
      ),
      name: (
        <Tooltip
          content={
            <>
              Details of <span className="font-bold">{item.name}</span> Product
            </>
          }
          placement="top"
          arrow
          animation="Interactive"
        >
          <button
            onClick={() =>
              navigate(`/details-product/${item.id}`, {
                state: { product: item },
              })
            }
            className="hover:underline hover:text-primary-500"
          >
            {/* {item.name} */}
            {item.name.length > 30 ? `${item.name.slice(0, 30)}...` : item.name}
          </button>
        </Tooltip>
      ),

      relatedProduct: (
        <>
          {item?.has_related_products ? <Tooltip
            content={`Details of ${item.name} Related Product`}
            placement="top"
            arrow
            animation="Interactive"
          >
            <div
              onClick={() =>
                navigate(`/related-product/${item.id}`, {
                  state: { support: item },
                })
              }
              className="cursor-pointer  text-primary-300 hover:text-primary-600 flex gap-2 my-auto items-center"
            >
              <Icon
                icon="fluent-mdl2:product-variant"
                className="text-lg font-bold w-6 h-6"
              />
              <span className="hover:underline hover:font-semibold">View Related Products</span>
            </div>
          </Tooltip> : <Tooltip
            content={`Eligibility of related Product for ${item.name}`}
            placement="top"
            arrow
            animation="Interactive">
            <div
              className="cursor-pointer  text-red-300 hover:text-red-500 flex gap-2 my-auto items-center"
            >
              <Icon
                icon="ic:twotone-do-not-touch"
                className="text-lg font-bold w-6 h-6"
              />
              <span className="hover:underline hover:font-semibold">Not Eligible</span>
            </div>
          </Tooltip>}
        </>
      ),

      inventory: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_active ? "Stock In" : "Stock Out"}
        </Badge>
      ),

      has_serials: (
        <Badge
          className={
            item.has_serials
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.has_serials ? "YES" : "NO"}
        </Badge>
      ),
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        loading={isLoading || isFetching}
        title="Product List"
        columns={columns}
        actions={actions}
        goto={
          <div className="flex items-center gap-2">
            <Icon
              icon="oui:ml-create-single-metric-job"
              className="text-white font-bold"
            />
            Add New Product
          </div>
        }
        gotoLink={"/create-product"}
        changePage={changePage}
        filter={filter}
        setFilter={setApiParam}
        data={tableData}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
      <DeleteProduct
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </div>
  );
};

export default ProductList;
