import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import React from "react";

const details = ({
  showQueryTicketModal,
  setQueryTicketModal,
  queryTicketData,
}) => {
  return (
    <Modal
      activeModal={showQueryTicketModal}
      onClose={() => setQueryTicketModal(false)}
      title="Details for Customer Ticket"
      className="max-w-xl h-auto"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setQueryTicketModal(false)}
        />
      }
    >
      <div className="space-y-4">
        <div className="border bg-slate-100 border-slate-400 rounded-lg">
          <h6 className="text-base font-bold p-2 gap-3 flex my-0 items-center">
            Organization/Customer Name:
            <span className="bg-primary-200 p-1 rounded-lg text-primary-500">
              {queryTicketData?.organization_name}
            </span>
          </h6>
        </div>
        <div className="border bg-slate-100 border-slate-400 rounded-lg">
          <h6 className="text-base font-bold p-2 gap-3 flex my-0 items-center">
            Contact Person Name:
            <span className="bg-primary-200 p-1 rounded-lg text-primary-500">
              {queryTicketData?.contact_person_name}
            </span>
          </h6>
        </div>
        <div className="border bg-slate-100 border-slate-400 rounded-lg">
          <h6 className="text-base font-bold p-2 gap-3 flex my-0 items-center">
            Contact Number:
            <span className="bg-primary-200 p-1 rounded-lg text-primary-500">
              {queryTicketData?.contact_no}
            </span>
          </h6>
        </div>
        <div className="border bg-slate-100 border-slate-400 rounded-lg">
          <h6 className="text-base font-bold p-2 gap-3 flex my-0 items-center">
            User Type:
            <span className="bg-primary-200 p-1 rounded-lg text-primary-500">
              {queryTicketData?.user_type}
            </span>
          </h6>
        </div>
        <div className="border bg-slate-100 border-slate-400 rounded-lg">
          <h6 className="text-base font-bold p-2 gap-3 flex my-0 items-center">
            Service Type:
            <span className="bg-primary-200 p-1 rounded-lg text-primary-500">
              {queryTicketData?.service_type}
            </span>
          </h6>
        </div>
        <div className="border bg-slate-100 border-slate-400 rounded-lg">
          <h6 className="text-base font-bold p-2 gap-3 flex my-0 items-center">
            Invoice Number:
            <span className="bg-primary-200 p-1 rounded-lg text-primary-500">
              {queryTicketData?.invoice_number}
            </span>
          </h6>
        </div>
        <div className="border bg-slate-100 border-slate-400 rounded-lg">
          <h6 className="text-base font-bold p-2 gap-3 flex my-0 items-center">
            Ticket Subject:
            <span className="bg-primary-200 p-1 rounded-lg text-primary-500">
              {queryTicketData?.ticket_subject}
            </span>
          </h6>
        </div>
        <div className="border bg-slate-100 border-slate-400 rounded-lg">
          <h6 className="text-base font-bold p-2 gap-3 flex my-0 items-center">
            Product Details:
            <span className="bg-primary-200 p-1 rounded-lg text-primary-500">
              {queryTicketData?.product_details}
            </span>
          </h6>
        </div>
        <div className="border bg-slate-100 border-slate-400 rounded-lg">
          <h6 className="text-base font-bold p-2 gap-3 flex my-0 items-center">
            Description:
            <span className="bg-primary-200 p-1 rounded-lg text-primary-500">
              {queryTicketData?.ticket_details}
            </span>
          </h6>
        </div>
        <div className="border bg-slate-100 border-slate-400 rounded-lg">
          <h6 className="text-base font-bold p-2 gap-3 flex my-0 items-center">
            Additional Doc:
            <span className="bg-primary-200 p-1 rounded-lg text-primary-500">
              {queryTicketData?.additional_doc}
            </span>
          </h6>
        </div>
      </div>
    </Modal>
  );
};

export default details;
