import BasicTablePage from "@/components/partials/common-table/table-basic";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import QueryTicketDetails from "./details";
import Button from "@/components/ui/Button";

const index = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");

  //Query Ticket Details State
  const [showQueryTicketModal, setQueryTicketModal] = useState(false);
  const [queryTicketData, setQueryTicketData] = useState(null);

  const { data, isLoading, isFetching } = useGetApiQuery({
    url: "query-tickets",
    params: apiParam,
  });
  const navigate = useNavigate();

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Ticket Subject", field: "ticket_subject" },
    { label: "Invoice No", field: "invoice_number" },
    { label: "User Type", field: "user_type" },
    { label: "Service Type", field: "service_type" },
    { label: "Contact No", field: "contact_no" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "View",
      icon: "lets-icons:view-alt",
      onClick: (val) => {
        setQueryTicketData(data.data[val]);
        setQueryTicketModal(true);
      },
    },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      ticket_subject: (
        <Button
          className="text-primary-500 hover:underline"
          onClick={() => {
            setQueryTicketData(item);
            setQueryTicketModal(true);
          }}
        >
          {item.ticket_subject}
        </Button>
      ),
      invoice_number: item.invoice_number,
      user_type: item.user_type,
      service_type: item.service_type,
      contact_no: item.contact_no,
    };
  });

  return (
    <div>
      <BasicTablePage
        title="Query Ticket List"
        columns={columns}
        loading={isLoading || isFetching}
        actions={actions}
        changePage={changePage}
        filter={filter}
        setFilter={setApiParam}
        data={tableData}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
      <QueryTicketDetails
        showQueryTicketModal={showQueryTicketModal}
        setQueryTicketModal={setQueryTicketModal}
        queryTicketData={queryTicketData}
      />
    </div>
  );
};

export default index;
