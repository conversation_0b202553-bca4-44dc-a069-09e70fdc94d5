import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import {
  createValidationSchema,
  isActiveOptions,
} from "@/formHandlers/VendorCrud";
import {
  useGetApiWithIdQuery,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";

const EditVendor = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { data: vendor } = useGetApiWithIdQuery(["vendors", id]);
  const [updateApi] = useUpdateApiMutation();

  const headerSlotContent = (
    <Button
      onClick={() => navigate("/vendor-list")}
      className="btn btn-outline-primary"
    >
      Vendor List
    </Button>
  );

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        } else if (Array.isArray(values.image)) {
          // If multiple files are uploaded
          values.image.forEach((file) => formData.append(key, file));
        }
      } else {
        formData.append(key, values[key]);
      }
    });
    console.log(...formData);

    try {
      const data = {
        end_point: "vendors/" + id,
        body: formData,
      };

      const response = await updateApi(data).unwrap();
      toast.success("Vendor Updated Successfully!");
      resetForm();
      navigate("/vendor-list");
    } catch (err) {
      // console.error("Submission failed:", err);
      toast.error("Vendor Updated failed. Please try again.");
    }
  };

  return (
    <div>
      <Formik
        initialValues={{
          name: vendor?.name || "",
          image: vendor?.image || "",
          email: vendor?.email || "",
          company_name: vendor?.company_name || "",
          website: vendor?.website || "",
          address: vendor?.address || "",
          number: vendor?.number || "",
          description: vendor?.description || "",
          is_active: vendor?.is_active === true ? 1 : 0,
        }}
        validationSchema={createValidationSchema()}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values }) => (
          <Form>
            {/* {console.log(values, 'it is values')} */}
            <Card
              headerslot={headerSlotContent}
              title="Edit Vendor"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid md:grid-cols-2 max-sm:grid-cols-1 gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  placeholder="Enter name"
                  required
                />
                <InputField
                  label="Company"
                  name="company_name"
                  type="text"
                  required
                  placeholder="Enter Company name"
                />
                <InputField
                  label="Email"
                  name="email"
                  type="email"
                  placeholder="Company Email"
                />
                <InputField
                  label="Number"
                  name="number"
                  type="text"
                  placeholder="Company Number"
                  required
                  onInput={(e) => {
                    e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  }}
                />
                <InputField
                  label="Website"
                  name="website"
                  type="text"
                  placeholder="Company Website"
                />
                <InputField
                  label="Address"
                  name="address"
                  type="text"
                  placeholder="Company Address"
                />
                <InputFile
                  label="image"
                  name="image"
                  type="file"
                  title="Upload your file"
                  accept="image/*"
                />
                <InputSelect
                  label="Is Active"
                  name="is_active"
                  options={isActiveOptions}
                  placeholder="Select status"
                  required
                />
              </div>
              <div className="mt-3">
                <TextAreaField
                  label="Description"
                  name="description"
                  type="text"
                  placeholder="Enter description"
                />
              </div>

              <div className="w-full text-end">
                <Button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditVendor;
