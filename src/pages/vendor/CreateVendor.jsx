import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import {
  createValidationSchema,
  isActiveOptions,
  vendorInitialValues,
} from "@/formHandlers/VendorCrud";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate } from "react-router-dom";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";

const CreateVendor = () => {
  const navigate = useNavigate();
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();
  const headerSlotContent = (
    <Button
      onClick={() => navigate("/vendor-list")}
      className="btn text-center btn-outline-primary"
    >
      Vendor List
    </Button>
  );

  const handleSubmit = async (values, { resetForm }) => {
    const modifiedValues = { ...values };
    console.log(values);

    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        }
        // else if (Array.isArray(values.image)) {
        //   // If multiple files are uploaded
        //   values.image.forEach((file) => formData.append(key, file));
        // }
      } else {
        formData.append(key, values[key]);
      }
      //   formData.append({...formData,_method: "POST"})
    });

    try {
      const response = await postApi({
        end_point: "vendors",
        body: formData,
      }).unwrap();
      // console.log("Submission successful:", response);
      toast.success("Vendor Create Successfully!");
      navigate("/vendor-list");
      resetForm();
    } catch (err) {
      // console.error("Submission failed:", err);
      // toast.error("Vendor Submission failed. Please try again.");
    }
  };

  return (
    <div>
      <Formik
        initialValues={vendorInitialValues}
        validationSchema={createValidationSchema()}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Create Vendor"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid md:grid-cols-2 grid-cols-1 gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter name"
                />
                <InputField
                  label="Company"
                  name="company_name"
                  type="text"
                  placeholder="Enter Company name"
                  required
                />
                <InputField
                  label="Email"
                  name="email"
                  type="email"
                  placeholder="Company Email"
                />
                <InputField
                  label="Number"
                  name="number"
                  type="number"
                  placeholder="Company Number"
                  required
                  onInput={(e) => {
                    e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  }}
                />
                <InputField
                  label="Website"
                  name="website"
                  type="text"
                  placeholder="Company Website"
                />
                <InputFile
                  label="image"
                  name="image"
                  type="file"
                  title="Upload your file"
                  accept="image/*"
                />

                {/* <InputSelect
                  label="Is Active"
                  name="is_active"
                  options={isActiveOptions}
                  placeholder="Select status"
                  required
                /> */}
              </div>
              <div className="my-3">
                <InputField
                  label="Address"
                  name="address"
                  type="text"
                  placeholder="Company Address"
                />
              </div>
              <div className="my-3">
                <TextAreaField
                  label="Description"
                  name="description"
                  type="text"
                  placeholder="Enter description"
                />
              </div>

              <div className="w-full text-end">
                <Button
                  type="submit"
                  className="btn text-center btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateVendor;
