import BasicTablePage from "@/components/partials/common-table/table-basic";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import { useState } from "react";
import Badge from "@/components/ui/Badge";
import { useNavigate } from "react-router-dom";
import DeleteBrand from "./DeleteBrand";
import NoImage from "@/assets/CRM/NoImage.png";

const BrandList = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery({ url: "brands", params: apiParam });
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Name", field: "name" },
    { label: "Image", field: "image" },
    { label: "Status", field: "is_active" },
    { label: "Actions", field: "" },
  ];

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/edit-brand/${data?.data[val]?.id}`);
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      name: item.name,
      image: (
        <>
          <img
            className="w-10 h-10 rounded"
            src={
              item.image
                ? `${import.meta.env.VITE_MEDIA_URL}/${item.image}`
                : NoImage
            }
          />
        </>
      ),
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {" "}
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        title="Brand List"
        loading={isLoading || isFetching}
        columns={columns}
        actions={actions}
        goto={"Create New Brand"}
        gotoLink={"/create-brand"}
        changePage={changePage}
        filter={filter}
        setFilter={setApiParam}
        data={tableData}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
      <DeleteBrand
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </div>
  );
};

export default BrandList;
