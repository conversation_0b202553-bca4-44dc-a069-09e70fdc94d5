import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import {
  createValidationSchema,
  isActiveOptions,
} from "@/formHandlers/BrandCrud";
import {
  useGetApiWithIdQuery,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { calcLength } from "framer-motion";
import { useNavigate, useParams } from "react-router-dom";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import { toast } from "react-toastify";

const EditBrand = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { data: brand } = useGetApiWithIdQuery(["brands", id]);
  const [updateApi] = useUpdateApiMutation();

  const headerSlotContent = (
    <Button
      onClick={() => navigate("/brand-list")}
      className="btn btn-outline-primary"
    >
      Brand List
    </Button>
  );

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        } else if (Array.isArray(values.image)) {
          // If multiple files are uploaded
          values.image.forEach((file) => formData.append(key, file));
        }
      } else {
        formData.append(key, values[key]);
      }
    });
    // console.log(...formData);

    try {
      const data = {
        end_point: "brands/" + id,
        body: formData,
      };

      const response = await updateApi(data).unwrap();
      toast.success("Brand Updated Successfully!");
      resetForm();
      navigate("/brand-list");
    } catch (err) {
      // console.error("Submission failed:", err);
      toast.error("Brand Updated failed. Please try again.");
    }
  };

  return (
    <div>
      <Formik
        initialValues={{
          name: brand?.name || "",
          image: brand?.image || "",
          description: brand?.description || "",
          is_active: brand?.is_active === true ? 1 : 0,
        }}
        validationSchema={createValidationSchema()}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values }) => (
          <Form>
            {/* {console.log(values, 'it is values')} */}
            <Card
              headerslot={headerSlotContent}
              title="Edit Brand"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter Brand name"
                />
                <InputFile
                  label="image"
                  name="image"
                  type="file"
                  title="Upload your file"
                  accept="image/*"
                />

                <InputSelect
                  label="Is Active"
                  name="is_active"
                  options={isActiveOptions}
                  placeholder="Select status"
                  required
                />
              </div>
              <div className="my-4">
                <TextAreaField
                  label="Description"
                  name="description"
                  type="text"
                  placeholder="Enter description"
                />
              </div>

              <div className="w-full text-end my-5">
                <Button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditBrand;
