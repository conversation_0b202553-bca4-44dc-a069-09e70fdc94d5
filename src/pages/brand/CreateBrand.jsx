import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import TextAreaField from "@/components/ui/form/TextAreaField";
import {
  brandInitialValues,
  createValidationSchema,
  isActiveOptions,
} from "@/formHandlers/BrandCrud";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

const CreateBrand = () => {
  const navigate = useNavigate();
  const headerSlotContent = (
    <Button
      onClick={() => navigate("/brand-list")}
      className="btn btn-outline-primary"
    >
      Brand List
    </Button>
  );
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();

  const handleSubmit = async (values, { resetForm }) => {
    const modifiedValues = { ...values };
    console.log(values);

    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        } else if (Array.isArray(values.image)) {
          // If multiple files are uploaded
          values.image.forEach((file) => formData.append(key, file));
        }
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const response = await postApi({
        end_point: "brands",
        body: formData,
      }).unwrap();
      toast.success("Brand Create Successfully!");
      navigate("/brand-list");
      resetForm();
    } catch (err) {
      // console.error("Submission failed:", err);
      // toast.error("Brand Create failed. Please try again.");
    }
  };
  return (
    <div>
      <Formik
        initialValues={brandInitialValues}
        validationSchema={createValidationSchema()}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Create Brand"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid md:grid-cols-2 grid-cols-1 gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter menu name"
                />
                <InputFile
                  label="image"
                  name="image"
                  type="file"
                  title="Upload your file"
                  accept="image/*"
                />
              </div>

              <div className="my-5">
                <TextAreaField
                  label="Description"
                  name="description"
                  type="text"
                  placeholder="Enter description"
                />
              </div>

              <div className="w-full text-end my-5">
                <Button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateBrand;
