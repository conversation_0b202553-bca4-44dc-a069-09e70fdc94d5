import React from "react";
import { Formik, Form } from "formik";
import Card from "@/components/ui/Card";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { useNavigate } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import InputSelect from "@/components/ui/form/InputSelect";
import InputFile from "@/components/ui/form/InputFile";
import {
  createUserValidationSchema,
  isUserActiveOptions,
  userInitialValues,
} from "@/formHandlers/UserCrud";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";

const CreateUser = () => {
  const navigate = useNavigate();
  const headerSlotContent = (
    <Button
      onClick={() => navigate("/user-list")}
      className="btn btn-outline-primary"
    >
      User List
    </Button>
  );
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    for (let key in values) {
      if (key === "image") {
        if (values.image) {
          formData.append("image", values.image);
        }
      } else {
        formData.append(key, values[key]);
      }
    }

    try {
      const response = await postApi({
        end_point: "users",
        body: formData,
      }).unwrap();
      console.log(response);
      toast.success("User Created Successfully!");
      resetForm();
      navigate("/user-list");
    } catch (err) {
      // console.error("Submission failed:", err);
      // toast.error("User Created failed. Please try again.");
    }
  };

  return (
    <div className="w-full">
      <Formik
        initialValues={userInitialValues}
        validationSchema={createUserValidationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, values }) => (
          <Form>
            {/* {console.log(values)} */}
            <Card
              headerslot={headerSlotContent}
              title="Add User"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter Name"
                />
                <InputField
                  label="Email"
                  name="email"
                  type="email"
                  required
                  placeholder="Enter Email"
                />
                <InputField
                  label="Username"
                  name="username"
                  type="text"
                  required
                  placeholder="Enter Username"
                />
                <InputField
                  label="Phone Number"
                  name="number"
                  type="tel"
                  placeholder="Enter Phone Number"
                  onInput={(e) => {
                    e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  }}
                />
                <InputField
                  label="Password"
                  name="password"
                  type="string"
                  required
                  placeholder="Enter Password"
                />
                <InputField
                  label="Confirm Password"
                  name="password_confirmation"
                  type="string"
                  required
                  placeholder="Confirm Password"
                />
                <InputFile
                  label="Image"
                  name="image"
                  type="file"
                  title="Upload your file"
                  accept="image/*"
                  // required
                />
                <InputSelect
                  label="Is Active"
                  name="is_active"
                  options={isUserActiveOptions}
                  placeholder="Select status"
                  required
                />
              </div>

              <div className="w-full text-end my-5">
                <Button
                  type="submit"
                  className="btn text-center btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateUser;
