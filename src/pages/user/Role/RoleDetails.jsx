import React from "react";
import Modal from "@/components/ui/Modal";
import { Icon } from "@iconify/react";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";

const RoleDetails = ({ activeModal, onClose, selectedId }) => {
    const { data: roleData } = useGetApiQuery({ url: `roles/${selectedId}` });

    const permissions = roleData?.permissions || [];
    const users = roleData?.users || [];

    return (
        <Modal
            activeModal={activeModal}
            onClose={()=>onClose()}
            title="Role Details"
            className="max-w-4xl min-h-[350px]"
        >
            <div className="p-6 space-y-6">
                <div className="bg-gray-50 shadow rounded-lg p-6">
                    <h2 className="text-lg font-semibold text-gray-700">Role Information</h2>
                    <div className="mt-4">
                        <p className="text-sm text-gray-500">Role Name</p>
                        <p className="text-base font-medium text-gray-800">{roleData?.name || "N/A"}</p>
                    </div>
                    <div className="mt-4">
                        <p className="text-sm text-gray-500">Guard Name</p>
                        <p className="text-base font-medium text-gray-800">{roleData?.guard_name || "N/A"}</p>
                    </div>
                    <div className="mt-4">
                        <p className="text-sm text-gray-500">Created At</p>
                        <p className="text-base font-medium text-gray-800">
                            {roleData?.created_at ? new Date(roleData.created_at).toLocaleDateString() : "N/A"}
                        </p>
                    </div>
                </div>

                <div className="bg-gray-50 shadow rounded-lg p-6">
                    <h2 className="text-lg font-semibold text-gray-700">Permissions</h2>
                    {permissions.length > 0 ? (
                        <ul className="mt-4 grid grid-cols-2 gap-4">
                            {permissions.map((permission) => (
                                <li key={permission.id} className="flex items-center gap-2">
                                    <Icon icon="mdi:shield-check-outline" className="text-blue-500 w-5 h-5" />
                                    <span className="text-base font-medium text-gray-800">{permission.name}</span>
                                </li>
                            ))}
                        </ul>
                    ) : (
                        <p className="mt-4 text-sm text-gray-500">No permissions assigned to this role.</p>
                    )}
                </div>

                <div className="bg-gray-50 shadow rounded-lg p-6">
                    <h2 className="text-lg font-semibold text-gray-700">Assigned Users</h2>
                    {users.length > 0 ? (
                        <ul className="mt-4 space-y-4">
                            {users.map((user) => (
                                <li key={user.id} className="flex items-center space-x-4">
                                    <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                                        <Icon icon="mdi:account-circle-outline" className="text-gray-400 w-8 h-8" />
                                    </div>
                                    <div>
                                        <p className="text-base font-medium text-gray-800">{user.name}</p>
                                        <p className="text-sm text-gray-500">{user.email}</p>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    ) : (
                        <p className="mt-4 text-sm text-gray-500">No users assigned to this role.</p>
                    )}
                </div>
            </div>
        </Modal>
    );
};

export default RoleDetails;
