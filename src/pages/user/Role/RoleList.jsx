import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery, useDeleteApiMutation } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Icon } from "@iconify/react";
import CreateRole from "./CreateRole";
import { useSelector } from "react-redux";
import { setShowModal } from "@/features/commonSlice";
import { useDispatch } from "react-redux";
import ConfirmationModal from "@/components/ui/form/ConfirmationModal";
import { toast } from "react-toastify";
import EditRole from "./EditRole";
import RoleDetails from "./RoleDetails";

const RoleList = () => {
  const { showModal } = useSelector((state) => state.commonReducer);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [selectedId, setSelectedId] = useState(null);
  const dispatch = useDispatch();
  const closeCreateModal = () => {
    dispatch(setShowModal(false));
  };
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const changePage = (value) => {
    setApiParam(value);
  };
  const [deleteApi] = useDeleteApiMutation();
  const handleDelete = async (id) => {
    try {
      const response = await deleteApi({
        end_point: `roles/${id}`,
        body: { _method: "DELETE" },
      }).unwrap();
      toast.success(`Deleted role with ID: ${id}`, response);
    } catch (error) {
      console.error(`Failed to delete role with ID: ${id}`, error);
    }
  };
  const handleConfirmModalClose = () => {
    setIsConfirmModalOpen(false);
    setSelectedId(null);
  };
  const navigate = useNavigate();
  const { data, isLoading, isFetching } = useGetApiQuery({ url: "roles", params: apiParam });
  const columns = [
    { label: "Sl", field: "sl" },
    { label: "Role Name", field: "name" },
    { label: "Permissions", field: "permissions" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "Details",
      icon: "heroicons:eye",
      onClick: (val) => {
        setIsAssignModalOpen(true);
        setSelectedId(data?.data[val]?.id);
      },
    },
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        setIsEditModalOpen(true);
        setSelectedId(data?.data[val]?.id);
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setIsConfirmModalOpen(true);
        setSelectedId(data?.data[val]?.id);
      },
    },
  ];

  const tableData = data?.data?.map((role, index) => {
    return {
      sl: index + 1,
      name: role.name,
      permissions: (
        <>
          {role.permissions.map((perm) => (
            <Badge key={perm.id} className="bg-blue-700 text-white mr-1">
              {perm.name}
            </Badge>
          ))}
        </>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        title="Role List"
        loading={isLoading || isFetching}
        columns={columns}
        actions={actions}
        changePage={changePage}
        data={tableData}
        filter={filter}
        setFilter={setApiParam}
        createButton={'Create New Role'}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
      />
      {showModal && <CreateRole activeModal={showModal} onClose={closeCreateModal} />}
      {isAssignModalOpen && selectedId && <RoleDetails activeModal={isAssignModalOpen} onClose={setIsAssignModalOpen} selectedId={selectedId} />}
      {isEditModalOpen && selectedId && <EditRole activeModal={isEditModalOpen} onClose={setIsEditModalOpen} selectedId={selectedId} />}
      {isConfirmModalOpen && <ConfirmationModal
        isOpen={isConfirmModalOpen}
        onClose={handleConfirmModalClose}
        onConfirm={() => handleDelete(selectedId)}
        message="Are you sure you want to delete this?"
        icon="mdi:alert-circle-outline"
      />}
    </div>

  );
};

export default RoleList;