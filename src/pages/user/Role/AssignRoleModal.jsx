import Modal from '@/components/ui/Modal';
import { useGetApiQuery, usePostApiMutation } from '@/store/api/apihandler/commonSlice';
import { Icon } from '@iconify/react';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const AssignRoleModal = ({ activeModal, onClose, user }) => {
    const allRoles = useGetApiQuery({ url: "all-roles" });
    const { data: userData, refetch } = useGetApiQuery({ url: `users/${user?.id}` });
    const [selectedRoleId, setSelectedRoleId] = useState(null);
    const [postApi] = usePostApiMutation();

    useEffect(() => {
        if (userData && userData.roles && userData.roles.length > 0) {
            setSelectedRoleId(userData.roles[0].id);
        }
    }, [userData]);

    const handleRoleSelect = (roleId) => {
        setSelectedRoleId(roleId === selectedRoleId ? null : roleId);
    };

    const handleSubmit = async () => {
        if (selectedRoleId) {
            const dataToSubmit = {
                user_id: user.id,
                role_id: selectedRoleId,
            };
            const response = await postApi({
                end_point: 'roles/assign-user',
                body: dataToSubmit
            }).unwrap();
            onClose();
            setSelectedRoleId(null);
            toast.success('Role Assigned Successfully');
        } else {
            console.error("No role selected.");
        }
    };

    return (
        <Modal
            activeModal={activeModal}
            onClose={onClose}
            title="Assign Role"
            className={'max-w-5xl'}
        >
            <div className='grid grid-cols-2 gap-4'>
                <div key={user.id} className='col-span-1'>
                    <h2 className="text-lg font-semibold mb-3">User Details</h2>
                    <div className='flex justify-between items-center mb-3'>
                        <div className="bg-gray-50 shadow-lg p-4 rounded-lg w-full relative">
                            <div className="flex items-start relative">
                                <div className="relative">
                                    <img
                                        src={user.image ? `${import.meta.env.VITE_MEDIA_URL}/${user.image}` : 'https://picsum.photos/80'}
                                        alt={user.name || "Profile"}
                                        className="w-16 h-16 rounded-full object-cover"
                                    />
                                    <div className="absolute bottom-1 right-0 transform translate-x-1/4 translate-y-1/4">
                                        <Icon icon="mdi:check-decagram" className="text-blue-500 w-6 h-6" />
                                    </div>
                                </div>
                                <div className="ml-4">
                                    <h2 className="text-lg font-semibold flex items-center">
                                        <Icon icon="mdi:account-circle-outline" className="mr-2 text-gray-500" />
                                        {user.name || 'Unknown User'}
                                    </h2>
                                    <p className="text-gray-500 flex items-center">
                                        <Icon icon="mdi:briefcase-outline" className="mr-2 text-gray-500" />
                                        {user.designation || 'No designation available'}
                                    </p>
                                    <p className="text-gray-400 mt-2 flex items-center">
                                        <Icon icon="mdi:email-outline" className="mr-2 text-gray-500" />
                                        {user.email || 'No email available'}
                                    </p>
                                    <p className="text-gray-400 mt-1 flex items-center">
                                        <Icon icon="mdi:at" className="mr-2 text-gray-500" />
                                        @{user.username || 'No username available'}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h2 className="text-lg font-semibold mb-3">Roles</h2>
                        {allRoles?.data?.map((role) => (
                            <div key={role.id} className='mb-3 flex justify-between items-center bg-gray-50 shadow-lg p-4 rounded-lg'>
                                <div className="flex flex-col">
                                    <h2 className="text-lg font-semibold capitalize">{role.name}</h2>
                                    <p className="text-gray-400 mt-1">Created at: {new Date(role.created_at).toLocaleDateString()}</p>
                                </div>
                                <div className="ml-4">
                                    <input
                                        type="checkbox"
                                        className="form-checkbox h-5 w-5 text-blue-600"
                                        checked={selectedRoleId === role.id}
                                        onChange={() => handleRoleSelect(role.id)}
                                    />
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                <div className='col-span-1'>
                    <h2 className="text-lg font-semibold mb-3">Selected Role</h2>
                    <div className="bg-gray-50 shadow-lg p-4 rounded-lg mb-4">
                        <h2 className="text-lg font-semibold">Selected Role</h2>
                        {selectedRoleId ? (
                            <p className="text-gray-500">Role ID: {selectedRoleId}</p>
                        ) : (
                            <p className="text-red-500">No role selected</p>
                        )}
                        <button
                            onClick={handleSubmit}
                            className="mt-4 w-full bg-blue-600 text-white font-semibold py-2 rounded-md hover:bg-blue-700 transition duration-200"
                        >
                            Submit
                        </button>
                    </div>
                </div>
            </div>
        </Modal>
    );
};

export default AssignRoleModal;
