import React, { useState, useEffect } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/form/InputField";
import CustomSelect from "@/components/ui/form/CustomSelect";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";
import { useGetApiQuery, useUpdateApiMutation } from "@/store/api/apihandler/commonSlice";
import { Icon } from "@iconify/react";

const EditRole = ({ activeModal, onClose, selectedId }) => {
    const { data: roleData, refetch } = useGetApiQuery({ url: `roles/${selectedId}` });
    const [updateApi] = useUpdateApiMutation();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const initialValues = {
        name: roleData?.name || "",
        permissions: roleData?.permissions || [],
    };

    const validationSchema = Yup.object({
        name: Yup.string().required("Role name is required"),
        // permissions: Yup.array()
        //     .min(1, "At least one permission is required")
        //     .required("Permissions are required"),
    });

    const permissionOptions = [
        { id: 1, name: "Dashboard" },
        { id: 2, name: "Users" },
        { id: 3, name: "Roles" },
        { id: 4, name: "Customers" },
        { id: 5, name: "Products" },
        { id: 6, name: "Orders" },
        { id: 7, name: "Stock" },
        { id: 8, name: "Reports" },
        { id: 9, name: "Settings" },
    ];

    const handleSubmit = async (values, { resetForm }) => {
        try {
            setIsSubmitting(true);
            const payload = new FormData();
            payload.append("name", values.name);
            values.permissions.forEach(permission => {
                payload.append("permissions[]", permission);
            });

            const response = await updateApi({
                end_point: `roles/${selectedId}`,
                body: payload,
            }).unwrap();

            toast.success("Role updated successfully!");
            resetForm();
            onClose(false);
        } catch (error) {
            console.error("Failed to update role. Please try again.", error);
        } finally {
            setIsSubmitting(false);
        }
    };


    return (
        <Modal
            activeModal={activeModal}
            onClose={() => onClose(false)}
            title="Edit Role"
            className="max-w-4xl min-h-[270px]"
        >
            <Formik
                enableReinitialize
                initialValues={{ ...roleData,permissions: roleData?.permissions.map(perm => perm.id) }}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
            >
                {({ values, errors }) => (
                    <>
                        <Form id="edit-role-form">
                            {console.log(errors, values)}
                            <div className="grid grid-cols-2 gap-6">
                                <div>
                                    <InputField
                                        name="name"
                                        label="Role Name"
                                        placeholder="Enter role name"
                                        required
                                    />
                                </div>
                                <div>
                                    <CustomSelect
                                        name="permissions"
                                        label="Permissions"
                                        options={permissionOptions}
                                        valueKey="id"
                                        labelKey="name"
                                    // required
                                    />
                                </div>
                            </div>
                            <div className="flex justify-end">
                                <Button
                                    type="submit" form="edit-role-form" disabled={isSubmitting}
                                    className="flex items-center gap-2 mt-5 bg-blue-500 text-white rounded-lg py-2 px-4 hover:bg-blue-600"
                                >
                                    <Icon icon="mdi:information" className="text-white" />
                                    {isSubmitting ? "Saving..." : "Save"}
                                </Button>
                            </div>
                        </Form>
                    </>
                )}
            </Formik>
        </Modal>
    );
};

export default EditRole;