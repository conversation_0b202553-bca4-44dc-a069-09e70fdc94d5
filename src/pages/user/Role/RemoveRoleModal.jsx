import Modal from '@/components/ui/Modal';
import { useGetApiWithIdQuery } from '@/store/api/apihandler/commonSlice';
import React from 'react';

const RemoveRoleModal = ({ activeModal, onClose, user }) => {
    const { data: userInfo } = useGetApiWithIdQuery(["users", user?.id]);

    console.log(userInfo, 'userInfo');
    return (
        <Modal
            activeModal={activeModal}
            onClose={onClose}
            title="Remove Role"
            className={'max-w-4xl'}
        >

        </Modal>
    );
};

export default RemoveRoleModal;