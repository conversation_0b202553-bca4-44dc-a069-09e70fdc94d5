import React, { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/form/InputField";
import CustomSelect from "@/components/ui/form/CustomSelect";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { Icon } from "@iconify/react";

const CreateRole = ({ activeModal, onClose }) => {
    const [postApi] = usePostApiMutation();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const initialValues = {
        name: "",
        permissions: [],
    };

    const validationSchema = Yup.object({
        name: Yup.string().required("Role name is required"),
        // permissions: Yup.array()
        //     .min(1, "At least one permission is required")
        //     .required("Permissions are required"),
    });

    const permissionOptions = [
        { id: 1, name: "Dashboard" },
        { id: 2, name: "Users" },
        { id: 3, name: "Roles" },
        { id: 4, name: "Customers" },
        { id: 5, name: "Products" },
        { id: 6, name: "Orders" },
        { id: 7, name: "Stock" },
        { id: 8, name: "Reports" },
        { id: 9, name: "Settings" },
    ];

    const handleSubmit = async (values, { resetForm }) => {
        try {
            setIsSubmitting(true);
            const payload = {
                end_point: "roles",
                body: {
                    name: values.name,
                    permissions: values.permissions,
                },
            };

            await postApi(payload).unwrap();
            toast.success("Role created successfully!");
            resetForm();
            onClose();
        } catch (error) {
            console.error("Failed to create role. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Modal
            activeModal={activeModal}
            onClose={() => onClose()}
            title="Create Role"
            className="max-w-4xl min-h-[270px]"
        >
            <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
            >
                {({ values, errors }) => (
                    <>
                        <Form id="create-role-form">
                            {console.log(errors, values)}
                            <div className="grid grid-cols-2 gap-6">
                                <div>
                                    <InputField
                                        name="name"
                                        label="Role Name"
                                        placeholder="Enter role name"
                                        required
                                    />
                                </div>
                                <div>
                                    <CustomSelect
                                        name="permissions"
                                        label="Permissions"
                                        options={permissionOptions}
                                        valueKey="id"
                                        labelKey="name"
                                    // required
                                    />
                                </div>
                            </div>
                            <div className="flex justify-end">
                                <Button
                                    type="submit" form="create-role-form" disabled={isSubmitting}
                                    className="flex items-center gap-2 mt-5 bg-blue-500 text-white rounded-lg py-2 px-4 hover:bg-blue-600"
                                >
                                    <Icon icon="mdi:information" className="text-white" />
                                    {isSubmitting ? "Saving..." : "Save"}
                                </Button>
                            </div>
                        </Form>
                    </>
                )}
            </Formik>
        </Modal>
    );
};

export default CreateRole;
