import { useGetApiQuery, usePostApiMutation } from '@/store/api/apihandler/commonSlice';
import { Icon } from '@iconify/react';
import React, { useState } from 'react';
import { toast } from 'react-toastify';

const AssignUser = () => {
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedUserId, setSelectedUserId] = useState(null);
    const [selectedRoleId, setSelectedRoleId] = useState(null);
    const [postApi] = usePostApiMutation();

    const allUsers = useGetApiQuery({ url: "users", params: { pagination: 0, is_active: 1, search: searchQuery } });
    const allRoles = useGetApiQuery({ url: "all-roles" });

    const handleSearchChange = (e) => {
        setSearchQuery(e.target.value);
    };

    const handleUserSelect = (userId) => {
        setSelectedUserId(userId);
    };

    const handleRoleSelect = (roleId) => {
        setSelectedRoleId(roleId);
    };

    const selectedUser = allUsers?.data?.find((user) => user.id === selectedUserId);
    const selectedRole = allRoles?.data?.find((role) => role.id === selectedRoleId);

    const handleSubmit = async () => {
        const requestBody = {
            user_id: selectedUserId,
            role_id: selectedRoleId,
        };

        try {
            const response = await postApi({
                end_point: 'roles/assign-user',
                body: requestBody
            }).unwrap();
            console.log('Submission successful:', response);
            toast.success('Role Assigned Successfully');

            setSelectedUserId(null);
            setSelectedRoleId(null);
            setSearchQuery("");

        } catch (err) {
            console.error('Submission failed:', err);
        }
    };

    return (
        <div className="grid grid-cols-1 lg:grid-cols-8 md:grid-cols-1 gap-3">
            <div className="col-span-4">
                <div className="rounded-md bg-gray-50 p-4">
                    <div className="flex justify-between items-center mb-4">
                        <h5 className='ml-1 text-lg font-semibold'>Select User</h5>
                        <input
                            type="text"
                            value={searchQuery}
                            onChange={handleSearchChange}
                            placeholder="Search user..."
                            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    <div className='grid grid-cols-1 lg:grid-cols-2 gap-4'>
                        {allUsers?.data?.map((user) => (
                            <div key={user.id} className='col-span-1'>
                                <div className='flex justify-between items-center mb-3'>
                                    <div className="mr-4">
                                        <input
                                            type="checkbox"
                                            className="form-checkbox h-5 w-5 text-blue-600"
                                            checked={selectedUserId === user.id}
                                            onChange={() => handleUserSelect(user.id)}
                                        />
                                    </div>
                                    <div className="bg-white shadow-lg p-4 rounded-lg w-full relative">
                                        <div className="flex items-start relative">
                                            <div className="relative">
                                                <img
                                                    src={user.image ? `${import.meta.env.VITE_MEDIA_URL}/${user.image}` : 'https://picsum.photos/80'}
                                                    alt={user.name || "Profile"}
                                                    className="w-16 h-16 rounded-full object-cover"
                                                />
                                                <div className="absolute bottom-1 right-0 transform translate-x-1/4 translate-y-1/4">
                                                    <Icon icon="mdi:check-decagram" className="text-blue-500 w-6 h-6" />
                                                </div>
                                            </div>
                                            <div className="ml-4">
                                                <h2 className="text-lg font-semibold flex items-center">
                                                    <Icon icon="mdi:account-circle-outline" className="mr-2 text-gray-500" />
                                                    {user.name || 'Unknown User'}
                                                </h2>
                                                <p className="text-gray-500 flex items-center">
                                                    <Icon icon="mdi:briefcase-outline" className="mr-2 text-gray-500" />
                                                    {user.designation || 'No designation available'}
                                                </p>
                                                <p className="text-gray-400 mt-2 flex items-center">
                                                    <Icon icon="mdi:email-outline" className="mr-2 text-gray-500" />
                                                    {user.email || 'No email available'}
                                                </p>
                                                <p className="text-gray-400 mt-1 flex items-center">
                                                    <Icon icon="mdi:at" className="mr-2 text-gray-500" />
                                                    {user.username || 'No username available'}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            <div className="col-span-2">
                <div className="rounded-md bg-gray-50 p-4">
                    <h5 className='mb-5 text-lg font-semibold'>Select Role</h5>
                    {allRoles?.data?.map((role) => (
                        <div key={role.id} className='mb-3 flex justify-between items-center bg-white shadow-lg p-4 rounded-lg'>
                            <div className="flex flex-col">
                                <h2 className="text-lg font-semibold capitalize">{role.name}</h2>
                                <p className="text-gray-400 mt-1">Created at: {new Date(role.created_at).toLocaleDateString()}</p>
                            </div>
                            <div className="ml-4">
                                <input
                                    type="checkbox"
                                    className="form-checkbox h-5 w-5 text-blue-600"
                                    checked={selectedRoleId === role.id}
                                    onChange={() => handleRoleSelect(role.id)}
                                />
                            </div>
                        </div>
                    ))}
                </div>
            </div>
            <div className="col-span-2">
                <div className="rounded-md bg-gray-50 p-4">
                    <h5 className='mb-4 font-semibold text-lg'>Selected User & Role</h5>

                    {selectedUser && (
                        <div className="mb-5 flex items-center">
                            <img
                                src={selectedUser.image ? `${import.meta.env.VITE_MEDIA_URL}/${selectedUser.image}` : 'https://picsum.photos/80'}
                                alt={selectedUser.name || "Profile"}
                                className="w-16 h-16 rounded-full object-cover mr-4"
                            />
                            <div>
                                <h6 className="font-semibold">{selectedUser.name}</h6>
                                <p className="text-gray-500">Email: {selectedUser.email}</p>
                                <p className="text-gray-500">Username: @{selectedUser.username}</p>
                            </div>
                        </div>
                    )}

                    {selectedRole && (
                        <div className="mt-5">
                            <h6 className="font-semibold">Role: {selectedRole.name}</h6>
                            <p className="text-gray-400">Created at: {new Date(selectedRole.created_at).toLocaleDateString()}</p>
                        </div>
                    )}

                    {!selectedUser && !selectedRole && (
                        <p className="text-gray-400">No user or role selected</p>
                    )}

                    <button
                        className="mt-4 w-full bg-blue-600 text-white font-semibold py-2 rounded-md hover:bg-blue-700 transition duration-200"
                        onClick={handleSubmit}
                        disabled={!selectedUser || !selectedRole}
                    >
                        Submit
                    </button>
                </div>
            </div>
        </div>
    );
};

export default AssignUser;
