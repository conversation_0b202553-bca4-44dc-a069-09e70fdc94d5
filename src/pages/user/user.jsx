import BasicTablePage from "@/components/partials/common-table/table-basic";
import React, { useState } from "react";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import { useNavigate } from "react-router-dom";
import Badge from "@/components/ui/Badge";
import DeleteUser from "./DeleteUser";
import AssignRoleModal from "./Role/AssignRoleModal";
import RemoveRoleModal from "./Role/RemoveRoleModal";

const User = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery({ url: "users", params: apiParam });
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [removeRoleModal, setRemoveRoleModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  const openModal = () => {
    setShowRoleModal(true);
  };

  const closeModal = () => {
    setShowRoleModal(false);
  };

  const openRemoveRoleModal = () => {
    setRemoveRoleModal(true);
  };

  const closeRemoveRoleModal = () => {
    setRemoveRoleModal(false);
  };

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Name", field: "name" },
    { label: "Email", field: "email" },
    { label: "Phone", field: "number" },
    { label: "Username", field: "username" },
    { label: "Image", field: "image" },
    { label: "Status", field: "is_active" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/edit-user/${data?.data[val]?.id}`);
      },
    },
    {
      name: "Assign Role",
      icon: "heroicons-outline:clipboard-check",
      onClick: (val) => {
        setSelectedUser(data.data[val]);
        openModal();
      },
    },
    // {
    //     name: "Remove Role",
    //     icon: "icomoon-free:checkbox-unchecked",
    //     onClick: (val) => {
    //         setSelectedUser(data.data[val]);
    //         openRemoveRoleModal();
    //     },
    // },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (value) => {
        setDeleteData(data.data[value]);
        setShowDeleteModal(true);
      },
    },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      email: item.email,
      number: item.number,
      username: item.username,

      image: (
        <>
          {item.image ? (
            <img
              src={import.meta.env.VITE_MEDIA_URL + `/${item.image}`}
              className="w-10 h-10 rounded-full object-cover"
              alt="Item"
            />
          ) : (
            <img
              src="https://png.pngtree.com/element_our/20190528/ourmid/pngtree-no-photo-icon-image_1128432.jpg"
              className="w-10 h-10 rounded-full object-cover"
              alt="Placeholder"
            />
          )}
        </>
      ),
      name: item.name,
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {" "}
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        title="User List"
        loading={isLoading || isFetching}
        columns={columns}
        actions={actions}
        goto={"Create New User"}
        gotoLink={"/user-create"}
        changePage={changePage}
        data={tableData}
        filter={filter}
        setFilter={setApiParam}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
      />
      <DeleteUser
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />

      {showRoleModal && (
        <AssignRoleModal
          activeModal={showRoleModal}
          onClose={closeModal}
          user={selectedUser}
        />
      )}

      {/* {
                removeRoleModal &&
                <RemoveRoleModal
                    activeModal={removeRoleModal}
                    onClose={closeRemoveRoleModal}
                    user={selectedUser}
                />
            } */}
    </div>
  );
};

export default User;
