import React from "react";
import Card from "@/components/ui/Card";
import {
  useGetApiQuery,
  useUpdateApiMutation,
  usePostApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";
import * as yup from "yup";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import NoImage from "@/assets/CRM/NoImage.png";
import FileInputForProduct from "@/components/ui/form/FileInputForProduct";

// Validation schema
const validationSchema = yup.object({
  name: yup
    .string()
    .max(255, "Support Name should not exceed 255 characters")
    .required("Name is Required"),
  email: yup
    .string()
    .email("Enter a valid email")
    .required("Email is Required"),
});

const Index = () => {
  const navigate = useNavigate();
  const { data: profileData, isLoading } = useGetApiQuery({ url: "profile" });
  const [updateApi] = useUpdateApiMutation();
  const [postApi] = usePostApiMutation();

  // Ensure profileData is fetched before rendering the form
  if (isLoading) return <div>Loading...</div>;

  const initialValues = {
    name: profileData?.user?.name || "",
    username: profileData?.user?.username || "",
    email: profileData?.user?.email || "",
    number: profileData?.user?.number || "",
    designation: profileData?.user?.designation || "",
    details: profileData?.user?.details || "",
    is_active: profileData?.user?.is_active ?? true,
    image: profileData?.user?.image || "",
  };

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    // Add the '_method' field to simulate PUT or PATCH request
    formData.append("_method", "POST");

    // Append other form data fields
    Object.keys(values).forEach((key) => {
      if (key === "image" && values.image) {
        formData.append(key, values.image);
      } else if (key === "is_active") {
        formData.append(key, values.is_active ? 1 : 0);
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const response = await postApi({
        end_point: "update-profile",
        body: formData,
      }).unwrap();
      toast.success("Profile Updated Successfully!");
      resetForm();
    } catch (err) {
      toast.error("Profile Updated Failed. Please try again.");
    }
  };

  return (
    <div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values, setFieldValue }) => (
          <Form>
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-bold text-lg">
                        Profile Information
                      </span>
                    </div>
                    <div>
                      <Tooltip
                        content="Back to Dashboard"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1 cursor-pointer"
                          onClick={() => navigate("/dashboard")}
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </Card>
              </div>
            </div>

            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-3 col-span-12">
                <Card className="">
                  {/* <div className="flex justify-center items-center">
                    <img
                      src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTugu0kegXOT1Gh1sgDVHvYjkGW29w19Hl9gQ&s"
                      alt="Profile"
                      className="w-24 h-24 rounded-lg border-4 border-white shadow-xl sm:w-28 sm:h-28 lg:w-40 lg:h-40"
                    />
                  </div> */}

                  {/* <div className="relative flex justify-center items-center">
                    <img
                      src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTugu0kegXOT1Gh1sgDVHvYjkGW29w19Hl9gQ&s"
                      alt="Profile"
                      className="w-24 h-24 rounded-lg border-4 border-white shadow-xl sm:w-28 sm:h-28 lg:w-40 lg:h-40"
                    />
                    <div
                      className="absolute -top-2 right-7 bg-white rounded-full p-1 shadow-md cursor-pointer hover:bg-gray-100  border border-primary-300 hover:border-primary-400 hover:border hover:rounded-full"
                      onClick={() => {
                        // Handle edit action here
                        console.log("Edit icon clicked");
                      }}
                    >
                      <Icon
                        icon="flowbite:edit-outline"
                        className="w-4 h-4 text-primary-400 hover:text-primary-600 "
                      />
                    </div>
                  </div> */}

                  <div className="">
                    {/* <FileInputForProduct
                      label="image"
                      initialImage={
                        profileData?.user?.image
                          ? import.meta.env.VITE_MEDIA_URL +
                            `/${profileData?.user?.image}`
                          : NoImage
                      }
                      name="image"
                      type="file"
                      title="Upload your file"
                      accept="image/*"
                      required
                    /> */}

                    <FileInputForProduct
                      label="image"
                      initialImage={
                        profileData?.user?.image
                          ? import.meta.env.VITE_MEDIA_URL +
                            `/${profileData?.user?.image}`
                          : NoImage
                      }
                      name="image"
                      type="file"
                      title="Upload your file"
                      accept="image/*"
                      required
                      imageWidth="200px"
                      imageHeight="200px"
                    />
                  </div>

                  <div className="my-3 text-center">
                    <div className="text-xl font-bold text-gray-800 lg:text-xl">
                      {profileData?.user?.name}
                    </div>
                    <div className="text-sm text-gray-400 lg:text-sm read-only">
                      {profileData?.user?.designation || "Designation"}
                    </div>
                  </div>
                </Card>
              </div>

              <div className="xl:col-span-9 col-span-12">
                <Card>
                  <div className="grid md:grid-cols-2 sm:grid-cols-1 gap-4">
                    <InputField
                      label="Full Name"
                      name="name"
                      type="text"
                      placeholder="Enter Full Name"
                      required
                    />
                    <InputField
                      label="User Name"
                      name="username"
                      type="text"
                      placeholder="Enter User Name"
                      readOnly
                    />
                    <InputField
                      label="Email Address"
                      name="email"
                      type="email"
                      placeholder="Enter Email Address"
                      required
                    />
                    <InputField
                      label="Contact Number"
                      name="number"
                      type="text"
                      placeholder="Enter Contact Number"
                      required
                    />
                    <InputField
                      label="Designation"
                      name="designation"
                      type="text"
                      placeholder="Enter Designation"
                    />
                  </div>
                  <div className="grid md:grid-cols-1 sm:grid-cols-1 my-4">
                    <TextAreaField
                      label="Details"
                      name="details"
                      type="text"
                      placeholder="Enter Details"
                    />
                  </div>
                </Card>
              </div>
            </div>

            {/* Submit and Cancel Buttons */}
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-end items-center gap-4">
                    <Button
                      type="button"
                      className="btn text-center btn-danger"
                      onClick={() => {
                        navigate("/dashboard");
                        resetForm();
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="btn text-center btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Updating..." : "Update"}
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Index;
