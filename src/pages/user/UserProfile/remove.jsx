import React from "react";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import Icon from "@/components/ui/Icon";
import NoImage from "@/assets/CRM/NoImage.png";

const UserProfileCard = () => {
  const profileInformation = useGetApiQuery({
    url: "profile",
  });
  const profileData = profileInformation?.data?.user;

  console.log("Profile", profileData);
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#6CAFED] to-[#166FC0] px-4 rounded-lg">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6 relative sm:max-w-lg lg:max-w-xl">
        <div className="absolute top-0 left-0 right-0 -translate-y-1/2 mx-auto flex justify-center">
          <img
            src={
              profileData?.image
                ? `${import.meta.env.VITE_MEDIA_URL}/${profileData?.image}`
                : NoImage
            }
            alt={profileData?.name}
            className="w-32 h-32 rounded-full border-4 border-white shadow-lg sm:w-28 sm:h-28 lg:w-40 lg:h-40 bg-slate-300"
          />
        </div>
        <div className="mt-16 text-center">
          <div className="flex space-x-3 mx-auto items-center justify-center">
            <Icon
              icon="lets-icons:user-box-duotone"
              className="text-lg font-bold w-6 h-6 text-primary-500"
            />
            <span className="text-xl font-bold text-slate-800 lg:text-2xl">
              {profileData?.name || "Profile Name"}
            </span>
          </div>

          <p className="text-sm text-gray-400 sm:text-base lg:text-sm">
            {profileData?.username || "Username"}
          </p>
          <div className="my-4 text-gray-600 sm:text-base lg:text-lg space-y-2">
            <div className="flex space-x-3 mx-auto items-center justify-center">
              <Icon
                icon="fluent:person-settings-20-filled"
                className="text-sm font-bold w-5 h-5 text-slate-800"
              />
              <span className="text-sm font-bold text-slate-800 lg:text-sm">
                {profileData?.designation || "Designation"}
              </span>
            </div>
            <div className="flex space-x-3 mx-auto items-center justify-center">
              <Icon
                icon="line-md:email-filled"
                className="text-sm font-bold w-5 h-5 text-slate-800"
              />
              <span className="text-sm font-bold text-slate-800 lg:text-sm">
                {profileData?.email || "Email Address"}
              </span>
            </div>
            <div className="flex space-x-3 mx-auto items-center justify-center">
              <Icon
                icon="entypo:old-mobile"
                className="text-sm font-bold w-5 h-5 text-slate-800"
              />
              <span className="text-sm font-bold text-slate-800 lg:text-sm">
                {profileData?.number || "Contact Number"}
              </span>
            </div>
          </div>

          <div className="flex justify-aroun mt-6 text-sm text-gray-600 sm:text-base lg:mt-8 mx-4">
            <div className="text-left text-wrap">
              {profileData?.details || "Details"}
            </div>
          </div>
        </div>
        {/* <div className="absolute top-6 left-6 text-pink-500 cursor-pointer">
          <span className="flex items-center gap-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="w-5 h-5 sm:w-6 sm:h-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15 12H3m6 6l-6-6 6-6m6 12h6m-6-6h6m-6-6l6 6-6 6"
              />
            </svg>
            Connect
          </span>
        </div> */}
        <div className="absolute -top-5 -right-4 cursor-pointer">
          <span className="flex items-center">
            <Icon
              icon="mdi:account-edit-outline"
              className="text-xl font-bold w-8 h-8 text-slate-50 bg-primary-500 border border-slate-300 rounded-full p-1 m-2"
            />
          </span>
        </div>
      </div>
    </div>
  );
};

export default UserProfileCard;

// import React from "react";
// import Card from "@/components/ui/Card";
// import {
//   useGetApiQuery,
//   useUpdateApiMutation,
// } from "@/store/api/apihandler/commonSlice";
// import { Form, Formik } from "formik";
// import { useNavigate, useParams } from "react-router-dom";
// import InputField from "@/components/ui/form/InputField";
// import TextAreaField from "@/components/ui/form/TextAreaField";
// import Switch from "@/components/ui/Switch";
// import Button from "@/components/ui/Button";
// import { toast } from "react-toastify";
// import { validationSchema } from "./formSetting";
// import Tooltip from "@/components/ui/Tooltip";
// import Icon from "@/components/ui/Icon";
// import Badge from "@/components/ui/Badge";
// import NoImage from "@/assets/CRM/NoImage.png";

// const Index = () => {
//   const navigate = useNavigate();

//   const profileInformation = useGetApiQuery({
//     url: "profile",
//   });
//   const profileData = profileInformation?.data?.user;

//   const [updateApi] = useUpdateApiMutation();

//   // Initial values populated dynamically with the fetched data
//   const initialValues = {
//     name: profileData?.name || "",
//     username: profileData?.username || "",
//     email: profileData?.email || "",
//     number: profileData?.number || "",
//     designation: profileData?.designation || "",
//     details: profileData?.details || "",
//     is_active: profileData?.is_active ?? true,
//     image: null,
//   };

//   const handleSubmit = async (values, { resetForm }) => {
//     const formData = new FormData();

//     Object.keys(values).forEach((key) => {
//       if (key === "image") {
//         // Append the image field to formData
//         if (values.image instanceof File) {
//           formData.append(key, values.image);
//         }
//       } else if (key === "is_active") {
//         // Convert boolean to integer (1 or 0)
//         formData.append(key, values.is_active ? 1 : 0);
//       } else {
//         formData.append(key, values[key]);
//       }
//     });

//     try {
//       const data = {
//         end_point: "update-profile",
//         body: formData,
//       };

//       const response = await updateApi(data).unwrap();
//       resetForm();
//       //   toast.success("Support updated successfully!");
//       //   navigate("/support-type");
//     } catch (err) {
//       toast.error("Failed to update support. Please try again.");
//     }
//   };

//   // Handle loading and form display

//   return (
//     <div>
//       <Formik
//         initialValues={initialValues}
//         validationSchema={validationSchema}
//         onSubmit={handleSubmit}
//         enableReinitialize
//       >
//         {({ isSubmitting, values, setFieldValue }) => (
//           <Form>
//             <div className="grid gap-4 grid-cols-12 mt-5">
//               <div className="xl:col-span-12 col-span-12 space-y-4">
//                 <Card>
//                   <div className="flex justify-between items-center">
//                     <div>
//                       <span className="font-bold text-lg">
//                         Profile Information
//                         <span className="text-primary-500">{values.name}</span>
//                       </span>
//                     </div>
//                     <div>
//                       <Tooltip
//                         content="Back to Dashboard"
//                         placement="top"
//                         arrow
//                         animation="scale"
//                       >
//                         <div
//                           className="m-1"
//                           //   onClick={() => navigate("/support-type")}
//                         >
//                           <Icon
//                             icon="ic:round-arrow-back"
//                             className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
//                           />
//                         </div>
//                       </Tooltip>
//                     </div>
//                   </div>
//                 </Card>
//               </div>
//             </div>

//             <div className="grid gap-4 grid-cols-12 mt-5">
//               <div className="xl:col-span-3 col-span-12">
//                 <div className="grid gap-4">
//                   <Card className="">
//                     <div className="flex justify-center items-center">
// <img
//   src="https://img.studioflicks.com/wp-content/uploads/2021/08/03111951/Rashmika-Mandanna.jpg"
//   alt="Profile"
//   className="w-24 h-24 rounded-lg border-4 border-white shadow-2xl sm:w-28 sm:h-28 lg:w-40 lg:h-40"
// />
//                     </div>
//                     <div className="my-3 text-center">
//                       <div className="text-xl font-bold text-gray-800 lg:text-xl">
//                         {profileData?.name}
//                       </div>
//                       <div className="text-sm text-gray-400 lg:text-sm read-only">
//                         {profileData?.designation || "Designation"}
//                       </div>
//                     </div>
//                   </Card>
//                 </div>
//               </div>

//               <div className="xl:col-span-9 col-span-12">
//                 <Card>
//                   <div className="grid md:grid-cols-2 sm:grid-cols-1 gap-4">
//                     <InputField
//                       label="Full Name"
//                       name="name"
//                       type="text"
//                       placeholder="Enter Full Name"
//                       required
//                     />
//                     <InputField
//                       label="User Name"
//                       name="username"
//                       type="text"
//                       placeholder="Enter User Name"
//                       readOnly
//                     />
//                     <InputField
//                       label="Email Address"
//                       name="email"
//                       type="text"
//                       placeholder="Enter Email Address"
//                       required
//                     />
//                     <InputField
//                       label="Contact Number"
//                       name="number"
//                       type="text"
//                       placeholder="Enter Contact Number"
//                       //   required
//                     />
//                     <InputField
//                       label="Designation"
//                       name="designation"
//                       type="text"
//                       placeholder="Enter Designation"
//                       //   required
//                     />
//                   </div>
//                   <div className="grid md:grid-cols-1 sm:grid-cols-1 my-4">
//                     <TextAreaField
//                       label="Details"
//                       name="details"
//                       type="text"
//                       placeholder="Enter Details"
//                     />
//                   </div>
//                 </Card>
//               </div>
//             </div>

//             {/* Submit and Cancel Buttons */}
//             <div className="grid gap-4 grid-cols-12 mt-5">
//               <div className="xl:col-span-12 col-span-12 space-y-4">
//                 <Card>
//                   <div className="flex justify-end items-center gap-4">
//                     <Button
//                       type="button"
//                       className="btn text-center btn-danger"
//                       //   onClick={() => navigate("/support-type")}
//                     >
//                       Cancel
//                     </Button>
//                     <Button
//                       type="submit"
//                       className="btn text-center btn-primary"
//                       disabled={isSubmitting}
//                     >
//                       {isSubmitting ? "Updating..." : "Update"}
//                     </Button>
//                   </div>
//                 </Card>
//               </div>
//             </div>
//           </Form>
//         )}
//       </Formik>
//     </div>
//   );
// };

// export default Index;
===-----

// import React from "react";
// import Card from "@/components/ui/Card";
// import {
//   useGetApiQuery,
//   useUpdateApiMutation,
// } from "@/store/api/apihandler/commonSlice";
// import { Form, Formik } from "formik";
// import { useNavigate } from "react-router-dom";
// import InputField from "@/components/ui/form/InputField";
// import TextAreaField from "@/components/ui/form/TextAreaField";
// import Button from "@/components/ui/Button";
// import { toast } from "react-toastify";
// import * as yup from "yup";
// import Tooltip from "@/components/ui/Tooltip";
// import Icon from "@/components/ui/Icon";
// import NoImage from "@/assets/CRM/NoImage.png";

// // Validation schema
// const validationSchema = yup.object({
//   name: yup
//     .string()
//     .max(255, "Support Name should not exceed 255 characters")
//     .required("Name is Required"),
//   email: yup
//     .string()
//     .email("Enter a valid email")
//     .required("Email is Required"),
//   number: yup.string().required("Contact Number is Required"),
//   details: yup.string().nullable(),
// });

// const Index = () => {
//   const navigate = useNavigate();
//   const { data: profileData, isLoading } = useGetApiQuery({ url: "profile" });
//   const [updateApi] = useUpdateApiMutation();

//   // Ensure profileData is fetched before rendering the form
//   if (isLoading) return <div>Loading...</div>;

//   const initialValues = {
//     name: profileData?.user?.name || "",
//     username: profileData?.user?.username || "",
//     email: profileData?.user?.email || "",
//     number: profileData?.user?.number || "",
//     designation: profileData?.user?.designation || "",
//     details: profileData?.user?.details || "",
//     is_active: profileData?.user?.is_active ?? true,
//     image: profileData?.user?.image || "",
//   };

//   const handleSubmit = async (values, { resetForm }) => {
//     const formData = new FormData();

//     Object.keys(values).forEach((key) => {
//       if (key === "image" && values.image) {
//         // Append the image field to formData if it's selected
//         formData.append(key, values.image);
//       } else if (key === "is_active") {
//         // Convert boolean to integer (1 or 0)
//         formData.append(key, values.is_active ? 1 : 0);
//       } else {
//         formData.append(key, values[key]);
//       }
//     });

//     try {
//       const response = await updateApi({
//         end_point: "update-profile",
//         body: formData,
//       }).unwrap();
//       toast.success("Profile updated successfully!");
//       resetForm();
//       navigate("/profile"); // Navigate back to the profile page
//     } catch (err) {
//       toast.error("Failed to update profile. Please try again.");
//     }
//   };

//   return (
//     <div>
//       <Formik
//         initialValues={initialValues}
//         validationSchema={validationSchema}
//         onSubmit={handleSubmit}
//         enableReinitialize
//       >
//         {({ isSubmitting, values, setFieldValue }) => (
//           <Form>
//             <div className="grid gap-4 grid-cols-12 mt-5">
//               <div className="xl:col-span-12 col-span-12 space-y-4">
//                 <Card>
//                   <div className="flex justify-between items-center">
//                     <div>
//                       <span className="font-bold text-lg">
//                         Profile Information
//                         <span className="text-primary-500">{values.name}</span>
//                       </span>
//                     </div>
//                     <div>
//                       <Tooltip
//                         content="Back to Dashboard"
//                         placement="top"
//                         arrow
//                         animation="scale"
//                       >
//                         <div className="m-1">
//                           <Icon
//                             icon="ic:round-arrow-back"
//                             className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
//                             onClick={() => navigate("/profile")}
//                           />
//                         </div>
//                       </Tooltip>
//                     </div>
//                   </div>
//                 </Card>
//               </div>
//             </div>

//             <div className="grid gap-4 grid-cols-12 mt-5">
//               <div className="xl:col-span-3 col-span-12">
//                 <Card className="">
//                   <div className="flex justify-center items-center">
//                     <img
//                       //   src={profileData?.user?.image || NoImage}
//                       src={
//                         profileData?.image
//                           ? `${import.meta.env.VITE_MEDIA_URL}/${
//                               profileData?.image
//                             }`
//                           : NoImage
//                       }
//                       alt="Profile"
//                       className="w-24 h-24 rounded-lg border-4 border-white shadow-2xl sm:w-28 sm:h-28 lg:w-40 lg:h-40"
//                     />
//                   </div>
//                   <div className="my-3 text-center">
//                     <div className="text-xl font-bold text-gray-800 lg:text-xl">
//                       {profileData?.user?.name}
//                     </div>
//                     <div className="text-sm text-gray-400 lg:text-sm read-only">
//                       {profileData?.user?.designation || "Designation"}
//                     </div>
//                   </div>
//                 </Card>
//               </div>

//               <div className="xl:col-span-9 col-span-12">
//                 <Card>
//                   <div className="grid md:grid-cols-2 sm:grid-cols-1 gap-4">
//                     <InputField
//                       label="Full Name"
//                       name="name"
//                       type="text"
//                       placeholder="Enter Full Name"
//                       required
//                     />
//                     <InputField
//                       label="User Name"
//                       name="username"
//                       type="text"
//                       placeholder="Enter User Name"
//                       readOnly
//                     />
//                     <InputField
//                       label="Email Address"
//                       name="email"
//                       type="email"
//                       placeholder="Enter Email Address"
//                       required
//                     />
//                     <InputField
//                       label="Contact Number"
//                       name="number"
//                       type="text"
//                       placeholder="Enter Contact Number"
//                       required
//                     />
//                     <InputField
//                       label="Designation"
//                       name="designation"
//                       type="text"
//                       placeholder="Enter Designation"
//                     />
//                   </div>
//                   <div className="grid md:grid-cols-1 sm:grid-cols-1 my-4">
//                     <TextAreaField
//                       label="Details"
//                       name="details"
//                       type="text"
//                       placeholder="Enter Details"
//                     />
//                   </div>
//                 </Card>
//               </div>
//             </div>

//             {/* Submit and Cancel Buttons */}
//             <div className="grid gap-4 grid-cols-12 mt-5">
//               <div className="xl:col-span-12 col-span-12 space-y-4">
//                 <Card>
//                   <div className="flex justify-end items-center gap-4">
//                     <Button
//                       type="button"
//                       className="btn text-center btn-danger"
//                       onClick={() => navigate("/profile")}
//                     >
//                       Cancel
//                     </Button>
//                     <Button
//                       type="submit"
//                       className="btn text-center btn-primary"
//                       disabled={isSubmitting}
//                     >
//                       {isSubmitting ? "Updating..." : "Update"}
//                     </Button>
//                   </div>
//                 </Card>
//               </div>
//             </div>
//           </Form>
//         )}
//       </Formik>
//     </div>
//   );
// };

// export default Index;

// import React from "react";
// import Card from "@/components/ui/Card";
// import {
//   useGetApiQuery,
//   useUpdateApiMutation,
//   usePostApiMutation,
// } from "@/store/api/apihandler/commonSlice";
// import { Form, Formik } from "formik";
// import { useNavigate } from "react-router-dom";
// import InputField from "@/components/ui/form/InputField";
// import TextAreaField from "@/components/ui/form/TextAreaField";
// import Button from "@/components/ui/Button";
// import { toast } from "react-toastify";
// import * as yup from "yup";
// import Tooltip from "@/components/ui/Tooltip";
// import Icon from "@/components/ui/Icon";
// import NoImage from "@/assets/CRM/NoImage.png";

// // Validation schema
// const validationSchema = yup.object({
//   name: yup
//     .string()
//     .max(255, "Support Name should not exceed 255 characters")
//     .required("Name is Required"),
//   email: yup
//     .string()
//     .email("Enter a valid email")
//     .required("Email is Required"),
//   number: yup.string().required("Contact Number is Required"),
//   details: yup.string().nullable(),
// });

// const Index = () => {
//   const navigate = useNavigate();
//   const { data: profileData, isLoading } = useGetApiQuery({ url: "profile" });
//   const [updateApi] = useUpdateApiMutation();
//   const [postApi] = usePostApiMutation();

//   // Ensure profileData is fetched before rendering the form
//   if (isLoading) return <div>Loading...</div>;

//   const initialValues = {
//     name: profileData?.user?.name || "",
//     username: profileData?.user?.username || "",
//     email: profileData?.user?.email || "",
//     number: profileData?.user?.number || "",
//     designation: profileData?.user?.designation || "",
//     details: profileData?.user?.details || "",
//     is_active: profileData?.user?.is_active ?? true,
//     image: profileData?.user?.image || "",
//   };

//   const handleSubmit = async (values, { resetForm }) => {
//     const formData = new FormData();

//     Object.keys(values).forEach((key) => {
//       if (key === "image" && values.image) {
//         // Append the image field to formData if it's selected
//         formData.append(key, values.image);
//       } else if (key === "is_active") {
//         // Convert boolean to integer (1 or 0)
//         formData.append(key, values.is_active ? 1 : 0);
//       } else {
//         formData.append(key, values[key]);
//       }
//     });

//     // try {
//     //   const response = await updateApi({
//     //     end_point: "update-profile",
//     //     body: formData,
//     //   }).unwrap();
//     //   toast.success("Profile updated successfully!");
//     //   resetForm();
//     //   navigate("/profile");
//     // } catch (err) {
//     //   toast.error("Failed to update profile. Please try again.");
//     // }

//     try {
//       const response = await postApi({
//         end_point: "update-profile",
//         body: formData,
//       }).unwrap();
//       toast.success("Product Added Successfully!");
//       navigate("/product-list");
//       resetForm();
//     } catch (err) {
//       // console.error("Submission failed:", err);
//       // toast.error("Product Added Failed. Please try again.");
//     }
//   };

//   return (
//     <div>
//       <Formik
//         initialValues={initialValues}
//         validationSchema={validationSchema}
//         onSubmit={handleSubmit}
//         enableReinitialize
//       >
//         {({ isSubmitting, values, setFieldValue }) => (
//           <Form>
//             <div className="grid gap-4 grid-cols-12 mt-5">
//               <div className="xl:col-span-12 col-span-12 space-y-4">
//                 <Card>
//                   <div className="flex justify-between items-center">
//                     <div>
//                       <span className="font-bold text-lg">
//                         Profile Information
//                         <span className="text-primary-500">{values.name}</span>
//                       </span>
//                     </div>
//                     <div>
//                       <Tooltip
//                         content="Back to Dashboard"
//                         placement="top"
//                         arrow
//                         animation="scale"
//                       >
//                         <div className="m-1">
//                           <Icon
//                             icon="ic:round-arrow-back"
//                             className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
//                             onClick={() => navigate("/profile")}
//                           />
//                         </div>
//                       </Tooltip>
//                     </div>
//                   </div>
//                 </Card>
//               </div>
//             </div>

//             <div className="grid gap-4 grid-cols-12 mt-5">
//               <div className="xl:col-span-3 col-span-12">
//                 <Card className="">
//                   <div className="flex justify-center items-center">
//                     <img
//                       src={
//                         profileData?.image
//                           ? `${import.meta.env.VITE_MEDIA_URL}/${
//                               profileData?.image
//                             }`
//                           : NoImage
//                       }
//                       alt="Profile"
//                       className="w-24 h-24 rounded-lg border-4 border-white shadow-2xl sm:w-28 sm:h-28 lg:w-40 lg:h-40"
//                     />
//                   </div>
//                   <div className="my-3 text-center">
//                     <div className="text-xl font-bold text-gray-800 lg:text-xl">
//                       {profileData?.user?.name}
//                     </div>
//                     <div className="text-sm text-gray-400 lg:text-sm read-only">
//                       {profileData?.user?.designation || "Designation"}
//                     </div>
//                   </div>
//                 </Card>
//               </div>

//               <div className="xl:col-span-9 col-span-12">
//                 <Card>
//                   <div className="grid md:grid-cols-2 sm:grid-cols-1 gap-4">
//                     <InputField
//                       label="Full Name"
//                       name="name"
//                       type="text"
//                       placeholder="Enter Full Name"
//                       required
//                     />
//                     <InputField
//                       label="User Name"
//                       name="username"
//                       type="text"
//                       placeholder="Enter User Name"
//                       readOnly
//                     />
//                     <InputField
//                       label="Email Address"
//                       name="email"
//                       type="email"
//                       placeholder="Enter Email Address"
//                       required
//                     />
//                     <InputField
//                       label="Contact Number"
//                       name="number"
//                       type="text"
//                       placeholder="Enter Contact Number"
//                       required
//                     />
//                     <InputField
//                       label="Designation"
//                       name="designation"
//                       type="text"
//                       placeholder="Enter Designation"
//                     />
//                   </div>
//                   <div className="grid md:grid-cols-1 sm:grid-cols-1 my-4">
//                     <TextAreaField
//                       label="Details"
//                       name="details"
//                       type="text"
//                       placeholder="Enter Details"
//                     />
//                   </div>
//                 </Card>
//               </div>
//             </div>

//             {/* Submit and Cancel Buttons */}
//             <div className="grid gap-4 grid-cols-12 mt-5">
//               <div className="xl:col-span-12 col-span-12 space-y-4">
//                 <Card>
//                   <div className="flex justify-end items-center gap-4">
//                     <Button
//                       type="button"
//                       className="btn text-center btn-danger"
//                       onClick={() => navigate("/profile")}
//                     >
//                       Cancel
//                     </Button>
//                     <Button
//                       type="submit"
//                       className="btn text-center btn-primary"
//                       disabled={isSubmitting}
//                     >
//                       {isSubmitting ? "Updating..." : "Update"}
//                     </Button>
//                   </div>
//                 </Card>
//               </div>
//             </div>
//           </Form>
//         )}
//       </Formik>
//     </div>
//   );
// };

// export default Index;