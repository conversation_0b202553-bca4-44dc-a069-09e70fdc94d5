import React from "react";
import { Formik, Form } from "formik";
import Card from "@/components/ui/Card";
import {
  useGetApiWithIdQuery,
  usePostApiMutation,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { useNavigate, useParams } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import InputSelect from "@/components/ui/form/InputSelect";
import InputFile from "@/components/ui/form/InputFile";
import {
  createUserValidationSchema,
  isUserActiveOptions,
  userInitialValues,
} from "@/formHandlers/UserCrud";
import Button from "@/components/ui/Button";
import * as Yup from "yup";
import { toast } from "react-toastify";

export const createUserUpdateValidationSchema = () => {
  return Yup.object().shape({
    name: Yup.string()
      .required("Name is required")
      .min(3, "Name must be at least 3 characters"),
    email: Yup.string()
      .email("Invalid email format")
      .required("Email is required"),
    username: Yup.string()
      .required("Username is required")
      .min(3, "Username must be at least 3 characters"),
    is_active: Yup.string().required("Status is required"),

    password: Yup.string()
      .min(6, "Password must be at least 6 characters")
      .notRequired(), // Password is not required by default

    password_confirmation: Yup.string()
      .oneOf([Yup.ref("password"), null], "Passwords must match")
      .when("password", {
        is: (password) => password && password.length > 0, // Only validate password_confirmation if password has a value
        then: Yup.string().required("Confirm Password is required"),
        otherwise: Yup.string().notRequired(),
      }),
  });
};

const EditUser = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { data: user } = useGetApiWithIdQuery(["users", id]);
  const [updateApi] = useUpdateApiMutation();
  const headerSlotContent = (
    <Button
      onClick={() => navigate("/user-list")}
      className="btn btn-outline-primary"
    >
      User List
    </Button>
  );
  console.log(user);

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    for (let key in values) {
      if (key === "image") {
        if (values.image instanceof File) {
          formData.append("image", values.image);
        }
      } else {
        formData.append(key, values[key]);
      }
    }

    try {
      const data = {
        end_point: "users/" + id,
        body: formData,
      };

      const response = await updateApi(data).unwrap();
      toast.success("User Updated Successfully!");
      resetForm();
      navigate("/user-list");
    } catch (err) {
      // console.error("Submission failed:", err);
      toast.error("User Updated failed. Please try again.");
    }
  };

  const formattedObject = {
    id: user?.id,
    name: user?.name,
    email: user?.email,
    username: user?.username,
    password: user?.password,
    password_confirmation: user?.password_confirmation,
    number: user?.number ? user?.number : "",
    image: user?.image,
    is_active: user?.is_active ? 1 : 0,
  };

  return (
    <div className="w-full">
      <Formik
        initialValues={formattedObject ? formattedObject : {}}
        validationSchema={createUserUpdateValidationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values }) => (
          <Form>
            {console.log(values)}
            <Card
              headerslot={headerSlotContent}
              title="Edit User"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter Name"
                />
                <InputField
                  label="Email"
                  name="email"
                  type="email"
                  required
                  placeholder="Enter Email"
                />
                <InputField
                  label="Username"
                  name="username"
                  type="text"
                  required
                  placeholder="Enter Username"
                />
                <InputField
                  label="Phone Number"
                  name="number"
                  type="tel"
                  // required
                  placeholder="Enter Phone Number"
                  onInput={(e) => {
                    e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  }}
                />
                <InputField
                  label="Password"
                  name="password"
                  type="string"
                  //   required
                  placeholder="Enter Password"
                />
                <InputField
                  label="Confirm Password"
                  name="password_confirmation"
                  type="string"
                  placeholder="Confirm Password"
                />
                <InputFile
                  label="Image"
                  name="image"
                  type="file"
                  title="Upload your file"
                  accept="image/*"
                  // required
                />
                <InputSelect
                  label="Is Active"
                  name="is_active"
                  options={isUserActiveOptions}
                  placeholder="Select status"
                  required
                />
              </div>

              <div className="w-full text-end my-5">
                <Button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditUser;
