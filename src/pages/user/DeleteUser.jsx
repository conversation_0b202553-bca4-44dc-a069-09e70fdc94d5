import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { useDispatch, useSelector } from "react-redux";
import { useDeleteApiMutation } from "@/store/api/apihandler/commonSlice";
import { toast } from "react-toastify";

const DeleteUser = ({ showDeleteModal, setShowDeleteModal, data }) => {
  const dispatch = useDispatch();
  const [deleteApi, { isLoading, isError, error, isSuccess }] =
    useDeleteApiMutation();
  // const onSubmit = async () => {
  //   const response = await deleteApi({
  //     end_point: "/users/" + data?.id,
  //     body: {},
  //   });
  //   console.log(response);
  //   setShowDeleteModal(false);
  // };
  const onSubmit = async () => {
    try {
      const response = await deleteApi({
        end_point: "/users/" + data?.id,
        body: {},
      });

      // If successful
      toast.success(`User "${data?.name}" Deleted Successfully!`);
      setShowDeleteModal(false);
    } catch (err) {
      // On error
      toast.error(`Failed to User "${data?.name}". Please try again.`);
    }
  };
  return (
    <Modal
      activeModal={showDeleteModal}
      onClose={() => setShowDeleteModal(false)}
      title="Delete User"
      className="max-w-2xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowDeleteModal(false)}
        />
      }
    >
      <h3 className="text-center">Are you sure?</h3>
      <p className="text-center text-slate-500 text-sm mt-4">
        You are going delete <b>"{data?.name}"</b> user. Once you have done
        this, there is no going back.{" "}
      </p>

      <div className="ltr:text-right rtl:text-left mt-5 gap-4">
        <Button
          // isLoading={isLoading}
          type="button"
          className="btn text-center btn-outline-primary mr-4"
          onClick={() => setShowDeleteModal(false)}
        >
          Cancel
        </Button>
        <Button
          isLoading={isLoading}
          type="button"
          className="btn text-center btn-danger"
          onClick={onSubmit}
        >
          Delete
        </Button>
      </div>
    </Modal>
  );
};

export default DeleteUser;
