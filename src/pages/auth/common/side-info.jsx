import React from "react";
import useDarkMode from "@/hooks/useDarkMode";
import { Link } from "react-router-dom";
import LogoWhite from "@/assets/images/logo/logo-white.svg";
import Logo from "@/assets/images/logo/bb-logo.png";
import Illustration from "@/assets/images/auth/ils1.svg";
import LoginIcon from "@/assets/CRM/coverLogin.svg";
const SideInfo = () => {
  const [isDark] = useDarkMode();

  return (
    <div className="left-column relative z-[1]">
      <div className="max-w-[520px] pt-20 ltr:pl-20 rtl:pr-20">
        <Link to="/">
          <img src={isDark ? LogoWhite : Logo} alt="" className="mb-10" />
        </Link>
        <div className="text-base whitespace-nowrap">
          <span className="text-bold text-4xl">
            Transform Your Customer Connections
          </span>
        </div>
      </div>
      <div className="absolute left-0 2xl:bottom-[-160px] bottom-[-130px] h-full w-full z-[-1]">
        <img src={LoginIcon} alt="" className="h-full w-full object-contain" />
      </div>
    </div>
  );
};

export default SideInfo;
