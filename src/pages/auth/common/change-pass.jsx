import React, { useState } from "react";
import * as yup from "yup";
import { Form, Formik } from "formik";
import InputField from "@/components/ui/form/InputField";
import { Icon } from "@iconify/react";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { toast } from "react-toastify";

// Validation Schema
const resetFormValidationSchema = yup.object({
  old_password: yup.string().required("Old password is required"),
  password: yup
    .string()
    .required("Please enter a new password")
    .min(8, "Password must be at least 8 characters long")
    .matches(
      /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/,
      "Password must contain letters and numbers"
    ),
  password_confirmation: yup
    .string()
    .oneOf([yup.ref("password"), null], "Passwords must match")
    .required("Please confirm your new password"),
});

const ChangePass = () => {
  // State for visibility toggles
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [postApi] = usePostApiMutation();

  // Handle form submission
  const handleSubmit = async (values, { resetForm }) => {
    console.log(values);

    try {
      const response = await postApi({
        end_point: "change-password",
        body: values,
      }).unwrap();
      // console.log("Submission successful:", response);
      toast.success("New Password Successfully Updated");
      navigate("/login");
      resetForm();
    } catch (err) {
      // toast.error("New Password Updated failed. Please try again.");
      // toast.error(err?.data?.errors);
      console.error("Submission failed:", err);
    }
  };

  // Toggle password visibility
  const toggleVisibility = (type) => {
    switch (type) {
      case "old_password":
        setShowOldPassword(!showOldPassword);
        break;
      case "password":
        setShowNewPassword(!showNewPassword);
        break;
      case "password_confirmation":
        setShowConfirmPassword(!showConfirmPassword);
        break;
      default:
        break;
    }
  };

  return (
    <Formik
      initialValues={{
        old_password: "",
        password: "",
        password_confirmation: "",
      }}
      validationSchema={resetFormValidationSchema}
      onSubmit={handleSubmit}
    >
      {({ isSubmitting }) => (
        <Form>
          <div className="grid gap-4">
            <div className="relative">
              <InputField
                label="Old Password"
                name="old_password"
                type={showOldPassword ? "text" : "password"}
                placeholder="Enter old password"
                required
              />
              <span
                className="absolute right-3 top-10 cursor-pointer"
                onClick={() => toggleVisibility("old_password")}
              >
                <Icon
                  icon={
                    showOldPassword ? "mdi:eye-off-outline" : "mdi:eye-outline"
                  }
                  className="text-gray-500 text-sm"
                  width={17}
                  height={17}
                />
              </span>
            </div>

            {/* New Password Field */}
            <div className="relative">
              <InputField
                label="New Password"
                name="password"
                type={showNewPassword ? "text" : "password"}
                placeholder="Enter new password"
                required
              />
              <span
                className="absolute right-3 top-10 cursor-pointer"
                onClick={() => toggleVisibility("password")}
              >
                <Icon
                  icon={
                    showNewPassword ? "mdi:eye-off-outline" : "mdi:eye-outline"
                  }
                  className="text-gray-500"
                  width={17}
                  height={17}
                />
              </span>
            </div>

            {/* Confirm Password Field */}
            <div className="relative">
              <InputField
                label="Confirm Password"
                name="password_confirmation"
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm password"
                required
              />
              <span
                className="absolute right-3 top-10 cursor-pointer"
                onClick={() => toggleVisibility("password_confirmation")}
              >
                <Icon
                  icon={
                    showConfirmPassword
                      ? "mdi:eye-off-outline"
                      : "mdi:eye-outline"
                  }
                  className="text-gray-500"
                  width={17}
                  height={17}
                />
              </span>
            </div>
          </div>

          <button
            type="submit"
            className="bg-blue-500 text-white font-bold py-2 px-4 w-full mt-4 rounded"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Submitting..." : "Submit"}
          </button>
        </Form>
      )}
    </Formik>
  );
};

export default ChangePass;
