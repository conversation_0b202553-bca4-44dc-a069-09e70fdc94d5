import React, { useState } from "react";
import { Formik, Form } from "formik";
import * as yup from "yup";
import Button from "@/components/ui/Button";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { useLoginMutation } from "@/store/api/auth/authApiSlice";
import { setUser } from "@/store/api/auth/authSlice";
import { toast } from "react-toastify";
import InputField from "@/components/ui/form/InputField";
import { Icon } from "@iconify/react";

const schema = yup
  .object({
    email_or_username: yup.string().required("Username or Email is Required"),
    password: yup.string().required("Password is Required"),
  })
  .required();

const LoginForm = () => {
  const [login, { isLoading }] = useLoginMutation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const onSubmit = async (values, { setSubmitting }) => {
    try {
      const response = await login(values);
      if (response.error) throw new Error(response.error.data.message);
      if (response.data.error) throw new Error(response.data.error);
      if (!response.data.token) throw new Error("Invalid credentials");

      const user = response.data;
      dispatch(setUser(user));
      localStorage.setItem("user", JSON.stringify(user));
      localStorage.setItem("crm_token", JSON.stringify(response.data.token));
      navigate(response?.data?.role === "user" ? "/user/dashboard" : "/dashboard");
      toast.success("Login Successful");
    } catch (error) {
      console.log(error);
      // toast.error(error.message || "Login failed");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Formik
      initialValues={{ email_or_username: "", password: "" }}
      validationSchema={schema}
      onSubmit={onSubmit}
    >
      {({ isSubmitting }) => (
        <Form className="space-y-5">
          <InputField
            name="email_or_username"
            label="Email or Username"
            type="text"
            placeholder="Email or Username"
            required
          />

          <div className="relative">
            <InputField
              name="password"
              label="Password"
              type={showPassword ? "text" : "password"}
              placeholder="Password"
              required
            />
            <span
              className="absolute right-3 top-10 cursor-pointer"
              onClick={() => setShowPassword(!showPassword)}
            >
              <Icon
                icon={showPassword ? "mdi:eye-off-outline" : "mdi:eye-outline"}
                className="text-gray-500"
                width={20}
                height={20}
              />
            </span>
          </div>

          <Button
            type="submit"
            text="Login"
            className="btn btn-dark block w-full text-center"
            isLoading={isLoading || isSubmitting}
          />
        </Form>
      )}
    </Formik>
  );
};

export default LoginForm;
