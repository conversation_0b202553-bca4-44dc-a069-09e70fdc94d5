import React from "react";
import { Link } from "react-router-dom";
import Lock from "./common/lock";
import useDarkMode from "@/hooks/useDarkMode";

// image import
import Logo from "@/assets/images/logo/bb-logo.png";
import SideInfo from "./common/side-info";
import UserImage from "@/assets/images/all-img/user-big.png";
const LockScreen = () => {
  const [isDark] = useDarkMode();
  return (
    <div className="loginwrapper">
      <div className="lg-inner-column">
        <SideInfo />
        <div className="right-column relative">
          <div className="inner-content h-full flex flex-col bg-white dark:bg-slate-800">
            <div className="auth-box2 flex flex-col justify-center h-full">
              <div className="mobile-logo text-center mb-6 lg:hidden block">
                <Link to="/">
                  <img src={Logo} alt="" className="mx-auto" />
                </Link>
              </div>
              <div className="text-center mb-10">
                <h4 className="font-medium mb-4">Lock Screen</h4>
                <div className="text-slate-500 dark:text-slate-400 text-base">
                  Enter your password to unlock the screen!
                </div>
              </div>
              <div className="author-bio text-center mb-8">
                <div className="h-14 w-14 mx-auto rounded-full">
                  <img
                    src={UserImage}
                    alt=""
                    className="w-full h-full object-cover block"
                  />
                </div>
                <div className="text-slate-900 dark:text-white text-base font-medium mt-4">
                  Kathryn Murphy
                </div>
              </div>
              <Lock />

              <div className="md:max-w-[345px] mx-auto font-normal text-slate-500 dark:text-slate-400 mt-12 uppercase text-sm">
                Not you ? return
                <Link
                  to="/"
                  className="text-slate-900 dark:text-white font-medium hover:underline"
                >
                  Sign In
                </Link>
              </div>
            </div>
            <div className="auth-footer text-center">
              Copyright 2024, BacBon Ltd All Rights Reserved.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LockScreen;
