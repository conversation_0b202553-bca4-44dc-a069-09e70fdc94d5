import React from 'react';
import { useParams } from 'react-router-dom';
import { useGetApiWithIdQuery } from '@/store/api/apihandler/commonSlice';
import { Icon } from '@iconify/react';
import SerialTable from './SerialTable';
import Loading from '@/components/Loading';

const ViewStock = () => {
    const { id } = useParams();
    const { data: apiResponse, isLoading } = useGetApiWithIdQuery(["stocks-edit", id]);

    const stock = apiResponse;

    if (isLoading) {
        return <Loading/>;
    }

    if (!stock) {
        return <div className="text-center text-lg font-semibold mt-10">No stock data found.</div>;
    }

    const { vendor, stock_entry_detail } = stock;

    return (
        <div className="p-6 w-full">
            <div className="bg-white shadow rounded-lg p-6">
                <h1 className="text-2xl font-semibold mb-6">Stock Details</h1>
                <div className="flex items-center mb-6">
                    {vendor.image ? (
                        <img
                            src={vendor.image}
                            alt={vendor.name}
                            className="w-16 h-16 rounded-full border"
                        />
                    ) : (
                        <Icon
                            icon="mdi:account-circle-outline"
                            className="w-16 h-16 text-gray-400"
                        />
                    )}
                    <div className="ml-4">
                        <h2 className="text-lg font-medium">{vendor.name}</h2>
                        <p className="text-gray-600">{vendor.number}</p>
                    </div>
                </div>
                <div>
                    <h3 className="text-lg font-semibold mb-4">Stock Items</h3>
                    {stock_entry_detail.map((entry) => (
                        <div
                            key={entry.id}
                            className="bg-gray-50 border rounded-lg p-4 mb-4"
                        >
                            <div className="mb-2">
                                <h5 className="font-medium text-gray-700">
                                    Product: {entry.product.name}
                                </h5>
                                <p className="text-sm text-gray-500">
                                    {entry.description}
                                </p>
                            </div>
                            <div className="mb-2">
                                <p className="text-sm">
                                    Quantity: <span className="font-medium">{entry.qty}</span>
                                </p>
                                {/* <p className="text-sm">
                                    Unit Price: <span className="font-medium">{entry.unit_price}</span>
                                </p>
                                <p className="text-sm">
                                    Total Price: <span className="font-medium">{entry.total_price}</span>
                                </p> */}
                            </div>
                            {entry?.product?.has_serials ?
                                <SerialTable productId={entry?.product?.id}/> :
                                <div>
                                    Non Serial Product
                                </div>}
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default ViewStock;
