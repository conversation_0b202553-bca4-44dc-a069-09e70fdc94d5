import React, { useRef, useState } from 'react';
import { Icon } from '@iconify/react';
import Modal from '@/components/ui/Modal';
import { Formik, Form, FieldArray } from 'formik';
import * as Yup from 'yup';
import InputField from '@/components/ui/form/InputField';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';
import ExcelDropZone from '@/components/ui/form/ExcelDropZone';
import { useSelector } from 'react-redux';
import { saveSerials, setSerialFormSubmitted } from '@/store/api/apihandler/serialSlice';
import { useDispatch } from 'react-redux';
import { useGetApiWithIdQuery } from '@/store/api/apihandler/commonSlice';

const validationSchema = Yup.object().shape({
    serials: Yup.array()
        .of(
            Yup.string()
                .trim()
                .required('Serial is required')
        )
});

const AddedProductsTable = ({ allProducts, addedProducts, handleEditProduct, handleDeleteProduct, handleUploadSerial }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const { data: serialInfo } = useGetApiWithIdQuery(["product-serial-check", selectedProduct?.product_id], {
        skip: !selectedProduct?.product_id
    });
    console.log(serialInfo, 'selectedprodut');
    const [serialsInitialValues, setSerialsInitialValues] = useState([]);
    const [activeTab, setActiveTab] = useState('excel');
    const [uploadedFile, setUploadedFile] = useState(null);
    const [excelSerials, setExcelSerials] = useState([]);
    const [error, setError] = useState('');
    const [duplicates, setDuplicates] = useState([]);
    const dispatch = useDispatch();
    const formikRef = useRef(null);

    const productSerials = useSelector((state) => state.serials.productSerials);
    const submissionStatus = useSelector((state) => state.serials.submissionStatus);
    console.log(productSerials);
    console.log(submissionStatus);

    const handleOpenModal = (addedProduct) => {
        setSelectedProduct(addedProduct);

        const existingSerials = productSerials[addedProduct.product_id] || addedProduct.serials || [];
        const newQty = addedProduct.qty;

        let serialsToSet = [];

        if (newQty > existingSerials.length) {
            serialsToSet = [...existingSerials, ...Array(newQty - existingSerials.length).fill("")];
        } else if (newQty < existingSerials.length) {
            serialsToSet = existingSerials.slice(0, newQty);
        } else {
            serialsToSet = [...existingSerials];
        }

        setSerialsInitialValues(serialsToSet);
        setIsModalOpen(true);
    };

    const handleCloseModal = (formValues) => {
        let serialsToSave = formValues?.serials || serialsInitialValues;

        // Check if the user uploaded serials via Excel
        if (excelSerials.length > 0 && duplicates.length === 0) {
            serialsToSave = excelSerials;
        }
        console.log(serialsToSave, 'Serials to save');

        if (selectedProduct) {
            dispatch(saveSerials({
                productId: selectedProduct.product_id,
                serials: serialsToSave,
                qty: selectedProduct.qty
            }));
        }
        resetModalState();
    };

    const resetModalState = () => {
        setIsModalOpen(false);
        setSelectedProduct(null);
        setSerialsInitialValues([]);
        setUploadedFile(null);
        setExcelSerials([]);
        setError('');
    };
    const handleModalCloseExternally = () => {
        const formValues = formikRef.current?.values || {};
        handleCloseModal(formValues);
    };


    const handleExcelUpload = (file) => {
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];

                const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                const serialsFromExcel = rows.slice(1)
                    .map((row) => row[1]) // Assuming serials are in the second column
                    .filter((serial) => serial !== undefined && serial !== null && serial !== '');

                if (serialsFromExcel.length !== selectedProduct.qty) {
                    setError(`Serial count mismatch: Expected ${selectedProduct.qty}, but found ${serialsFromExcel.length}.`);
                    setUploadedFile(null);
                    return;
                }

                const foundDuplicates = serialsFromExcel.filter((item, index) => serialsFromExcel.indexOf(item) !== index);
                const databaseMatches = serialsFromExcel.filter(serial => serialInfo?.includes(serial));

                setDuplicates([...new Set([...foundDuplicates, ...databaseMatches])]);
                setUploadedFile(file);
                setExcelSerials(serialsFromExcel);
                setError('');
            };
            reader.readAsArrayBuffer(file);
        } else {
            setUploadedFile(null);
            setExcelSerials([]);
            setError('');
        }
    };


    const handleSubmitExcelSerials = () => {
        if (excelSerials.length !== selectedProduct.qty) {
            toast.error(`Serial count must match the quantity (${selectedProduct.qty})`);
            return;
        }
        dispatch(saveSerials({
            productId: selectedProduct.product_id,
            serials: excelSerials,
            qty: selectedProduct.qty
        }));

        dispatch(setSerialFormSubmitted({ productId: selectedProduct.product_id, isSubmitted: true }));
        handleUploadSerial(selectedProduct.product_id, excelSerials);
        handleCloseModal();
    };

    const handleFormSubmit = (values) => {
        if (values.serials.length !== selectedProduct.qty) {
            toast.error(`Serial count must match the quantity (${selectedProduct.qty})`);
            return;
        }
        dispatch(saveSerials({
            productId: selectedProduct.product_id,
            serials: values.serials,
            qty: selectedProduct.qty
        }));

        dispatch(setSerialFormSubmitted({ productId: selectedProduct.product_id, isSubmitted: true }));
        handleUploadSerial(selectedProduct.product_id, values.serials);
        resetModalState();
    };

    const handleDownload = () => {
        const fileUrl = "../../../public/demo.xlsx";
        const link = document.createElement("a");
        link.href = fileUrl;
        link.download = "demo.xlsx";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <div className='overflow-x-auto'>
            <table className="table-auto w-full text-left">
                <thead>
                    <tr>
                        <th className="border-r text-[#1F2F70] font-medium border-white px-4 py-2 bg-[#E3E9FE]">Product Name</th>
                        <th className="border-r text-[#1F2F70] font-medium border-white px-4 py-2 bg-[#E3E9FE]">Quantity</th>
                        <th className="border-r text-[#1F2F70] font-medium border-white px-4 py-2 bg-[#E3E9FE]">Upload Serial</th>
                        <th className="border text-[#1F2F70] font-medium border-white px-4 py-2 bg-[#E3E9FE]">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {addedProducts.length === 0 ? (
                        <tr>
                            <td colSpan="8" className="text-center py-4 text-neutral-500">
                                No products added yet.
                            </td>
                        </tr>
                    ) : (
                        addedProducts.map((addedProduct, index) => {
                            const productInfo = allProducts?.data?.find(product => product.id === addedProduct.product_id);

                            return (
                                <tr key={index}>
                                    <td className="border px-4 py-2">{addedProduct.product_name}</td>
                                    <td className="border px-4 py-2">{addedProduct.qty}</td>
                                    <td className="border px-4 py-2">
                                        {productInfo?.has_serials ? (
                                            productSerials[addedProduct.product_id]?.length === addedProduct.qty &&
                                                productSerials[addedProduct.product_id]?.every(serial => serial.trim() !== "") &&
                                                submissionStatus[addedProduct.product_id] ? (
                                                <button type='button' className='bg-[#E3E9FE] text-[#1F2F70] p-1 rounded-md flex items-center space-x-1' onClick={() => handleOpenModal(addedProduct)}>
                                                    <Icon icon="mdi:check" className="w-5 h-5 text-green-500" />
                                                    <span>See Uploaded Serials</span>
                                                </button>
                                            ) : (
                                                <button
                                                    type="button"
                                                    className="bg-[#E3E9FE] text-[#1F2F70] p-1 rounded-md hover:text-green-700 flex items-center space-x-1"
                                                    onClick={() => handleOpenModal(addedProduct)}
                                                >
                                                    <Icon icon="mdi:plus" className="w-5 h-5" />
                                                    <span>Upload Serial</span>
                                                </button>
                                            )
                                        ) : (
                                            <div className='p-1 bg-gray-200 rounded-md text-center'>N/A</div>
                                        )}
                                    </td>
                                    <td className="border px-4 py-2">
                                        <div className='flex space-x-2 items-center'>
                                            <button type="button" className="text-blue-500 hover:text-blue-700" onClick={() => handleEditProduct(index)}>
                                                <Icon icon="mdi:pencil" className="w-5 h-5" />
                                            </button>
                                            <button type="button" onClick={() => handleDeleteProduct(index)} className="text-red-500 hover:text-red-700">
                                                <Icon icon="mdi:trash-can" className="w-5 h-5" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            );
                        })
                    )}
                </tbody>
            </table>

            {isModalOpen && selectedProduct && (
                <Modal
                    activeModal={isModalOpen}
                    onClose={handleModalCloseExternally}
                    title="Upload Serial"
                    className='max-w-4xl'
                >
                    <div className="tabs">
                        <div className='grid grid-cols-2 gap-3'>
                            <div className='col-span-1'>
                                <button
                                    className={`px-4 w-full py-2 ${activeTab === 'manual' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
                                    onClick={() => setActiveTab('manual')}
                                >
                                    Upload Serial Manually
                                </button>
                            </div>
                            <div className='col-span-1'>
                                <button
                                    className={`px-4 w-full py-2 ${activeTab === 'excel' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
                                    onClick={() => setActiveTab('excel')}
                                >
                                    Upload via Excel
                                </button>
                            </div>
                        </div>

                        {activeTab === 'manual' ? (
                            <Formik
                                key={selectedProduct.product_id}
                                initialValues={{ serials: serialsInitialValues }}
                                validationSchema={validationSchema}
                                onSubmit={handleFormSubmit}
                                innerRef={formikRef}
                                enableReinitialize={true}
                            >
                                {({ values, handleChange, errors, touched }) => {

                                    const serials = values.serials.filter(serial => serial.trim() !== '');

                                    const matchingSerials = serials.filter(serial => serialInfo?.includes(serial));
                                    const hasMatches = matchingSerials?.length > 0;

                                    const duplicateSerialOccurrence = serials.reduce((acc, serial, index) => {
                                        if (!acc[serial]) acc[serial] = [];
                                        acc[serial].push(index + 1);
                                        return acc;
                                    }, {});

                                    const duplicateFields = Object.keys(duplicateSerialOccurrence)
                                        .filter((serial) => duplicateSerialOccurrence[serial].length > 1)
                                        .reduce((acc, serial) => {
                                            acc[serial] = duplicateSerialOccurrence[serial];
                                            return acc;
                                        }, {});

                                    const hasDuplicates = Object.keys(duplicateFields).length > 0;


                                    return (
                                        <Form>
                                            <div className="p-4">
                                                <h3 className="text-lg font-bold mb-2">
                                                    {selectedProduct?.product_name} - Serial Upload
                                                </h3>

                                                <div>

                                                    <FieldArray
                                                        name="serials"
                                                        render={() => (
                                                            <div className="grid grid-cols-3 gap-4">
                                                                {values.serials.map((serial, index) => (
                                                                    <div key={index} className="mb-4">
                                                                        <InputField
                                                                            required
                                                                            label={`Serial ${index + 1}`}
                                                                            name={`serials.${index}`}
                                                                            type="text"
                                                                            value={serial}
                                                                            onChange={handleChange}
                                                                            placeholder={`Enter Serial Number ${index + 1}`}
                                                                        />
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        )}
                                                    />
                                                    {/* <div className='col-span-2'>
                                                        <div className='p-2 bg-red-100 mt-8 rounded flex justify-center'>
                                                            Duplicate Serials Checker
                                                        </div>
                                                        {hasDuplicates && (
                                                            <div className="mt-4 bg-red-100 text-red-600 p-2 rounded">
                                                                There are {Object.keys(duplicateFields).length} duplicate serial(s) found in the following fields:
                                                                <ul className="list-disc list-inside">
                                                                    {Object.values(duplicateFields).map((indices, index) => (
                                                                        <li key={index}>
                                                                            Duplicate in fields: {indices.join(", ")}
                                                                        </li>
                                                                    ))}
                                                                </ul>
                                                            </div>
                                                        )}
                                                        {hasMatches && (
                                                            <div className="mt-4 bg-red-100 text-red-600 p-2 rounded">
                                                                <p className="font-bold">The following serials already exist in our database:</p>
                                                                <ul className="list-disc list-inside">
                                                                    {matchingSerials?.map((serial, idx) => (
                                                                        <li key={idx}>{serial}</li>
                                                                    ))}
                                                                </ul>
                                                            </div>
                                                        )}
                                                    </div> */}
                                                </div>

                                                <div className='w-full'>
                                                    <div className={`p-2 ${hasDuplicates || hasMatches ? 'bg-red-100' : 'bg-green-100'} mt-8 rounded flex justify-center`}>
                                                        {hasDuplicates || hasMatches ? 'Duplicate Serials Checker' : 'No Duplicate Serials Found'}
                                                    </div>
                                                    {hasDuplicates && (
                                                        <div className="mt-4 bg-red-100 text-red-600 p-2 rounded">
                                                            There are {Object.keys(duplicateFields).length} duplicate serial(s) found in the following fields:
                                                            <ul className="list-disc list-inside">
                                                                {Object.values(duplicateFields).map((indices, index) => (
                                                                    <li key={index}>
                                                                        Duplicate in fields: {indices.join(", ")}
                                                                    </li>
                                                                ))}
                                                            </ul>
                                                        </div>
                                                    )}
                                                    {hasMatches && (
                                                        <div className="mt-4 bg-red-100 text-red-600 p-2 rounded">
                                                            <p className="font-bold">The following serials already exist in our database:</p>
                                                            <ul className="list-disc list-inside">
                                                                {matchingSerials?.map((serial, idx) => (
                                                                    <li key={idx}>{serial}</li>
                                                                ))}
                                                            </ul>
                                                        </div>
                                                    )}

                                                </div>

                                                <div className="mt-4 flex justify-end items-center">

                                                    <div className='flex space-x-2 '>
                                                        <button
                                                            disabled={hasDuplicates || hasMatches}
                                                            type="submit"
                                                            className="bg-blue-600 text-white py-2 px-4 rounded-md"
                                                        >
                                                            Submit
                                                        </button>
                                                        <button
                                                            type="button"
                                                            className="bg-gray-600 text-white py-2 px-4 ml-4 rounded-md"
                                                            onClick={() => handleCloseModal(values)}
                                                        >
                                                            Close
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </Form>
                                    )
                                }
                                }
                            </Formik>
                        ) : (
                            <div className="p-4">
                                <h3 className="text-lg font-bold mb-2">Upload Excel File</h3>
                                <ExcelDropZone onDrop={handleExcelUpload} height="250px" width='w-full' error={error} />
                                {uploadedFile && (
                                    <div className="mt-4">
                                        <p>Uploaded file: {uploadedFile.name}</p>
                                    </div>
                                )}
                                <div className='flex justify-end'>
                                    <button onClick={handleDownload} type='button' className='mt-3 p-2 bg-blue-800 text-white rounded'>see demo file</button>
                                </div>
                                {excelSerials.length > 0 && (
                                    <div className="mt-4">
                                        <h4 className="text-lg font-semibold mb-2">Serials from Excel:</h4>
                                        <table className="table-auto w-full text-left">
                                            <thead>
                                                <tr>
                                                    <th className="border px-4 py-2">No.</th>
                                                    <th className="border px-4 py-2">Serial Number</th>
                                                    <th className="border px-4 py-2">Duplicate Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {excelSerials?.map((serial, index) => {
                                                    const isDuplicate = duplicates.includes(serial);
                                                    return (
                                                        <tr key={index} className={`${isDuplicate ? 'bg-red-200' : ''}`}>
                                                            <td className="border px-4 py-2">{index + 1}</td>
                                                            <td className="border px-4 py-2">{serial}</td>
                                                            <td className="border px-4 py-2">
                                                                {isDuplicate ? (
                                                                    <span className="text-red-500">Duplicate</span>
                                                                ) : (
                                                                    <span className="text-green-500">Unique</span>
                                                                )}
                                                            </td>
                                                        </tr>
                                                    );
                                                })}
                                            </tbody>
                                        </table>
                                    </div>
                                )}
                                {excelSerials.length > 0 && (
                                    <div className="mt-4">
                                        <button
                                            className="bg-blue-600 text-white py-2 px-4 rounded-md"
                                            onClick={handleSubmitExcelSerials}
                                            disabled={duplicates.length > 0}
                                        >
                                            Submit Excel Serials
                                        </button>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </Modal>
            )}
        </div>
    );
};

export default AddedProductsTable;
