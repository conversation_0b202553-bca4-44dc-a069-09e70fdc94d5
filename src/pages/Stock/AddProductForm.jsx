import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { Icon } from "@iconify/react";
import Modal from "@/components/ui/Modal";
import InputSelect from "@/components/ui/form/InputSelect";
import InputField from "@/components/ui/form/InputField";

const AddProductForm = ({ isModalOpen, closeModal, productOptions, handleAddProduct }) => {
  const validationSchema = Yup.object().shape({
    product_id: Yup.string().required("Please select a product."),
    qty: Yup.number().required("Quantity is required").positive("Quantity must be a positive number."),
    // unit_price: Yup.number().required("Unit price is required").positive("Unit price must be a positive number."),
    // warranty_period: Yup.string().required("Warranty period is required."),
    // warranty_period_value: Yup.number().
    // required("Warranty time is required").positive("Warranty time must be a positive number."),
  });


  return (
    <Modal activeModal={isModalOpen} onClose={closeModal} title="Select Product" className={'max-w-4xl min-h-[375px]'}>
      <Formik
        initialValues={{
          product_id: '',
          // unit_price: '',
          qty: '',
          // warranty_period: 'months',
          // warranty_period_value: '',
          description: '',
        }}
        validationSchema={validationSchema}
        onSubmit={(values, { resetForm }) => {
          handleAddProduct(values, resetForm);
          closeModal();
        }}
      >
        {({ values, handleChange, errors, touched }) => (
          <Form>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className='flex items-center'>
                <div className="w-full">
                  <InputSelect
                    label="Select Product"
                    name="product_id"
                    options={productOptions}
                    placeholder="Select a product"
                    className="w-[100%]"
                    required
                  />
                  {touched.product_id && errors.product_id && (
                    <div className="text-red-500 text-xs mt-2">{errors.product_id}</div>
                  )}
                </div>
              </div>
              {/* <InputField
                label="Unit Price"
                name="unit_price"
                type="number"
                placeholder="Enter unit price"
                required
              /> */}
              <InputField
                label="Quantity"
                name="qty"
                type="number"
                placeholder="Enter Quantity"
                required
              />
              {/* <InputSelect
                label="Warranty Period"
                name="warranty_period"
                options={[
                  { label: 'months', value: 'months' },
                  { label: 'years', value: 'years' },
                ]}
                placeholder="Select Warranty Period"
              /> */}
              {/* <InputField
                label="Warranty Time"
                name="warranty_period_value"
                type="number"
                placeholder="Warranty Time"
                required
              /> */}
              {/* <InputField
                label="Remark"
                name="description"
                type="text"
                placeholder="Remark"
              /> */}
            </div>
            <div className="flex justify-end">
              <button
                type="submit"
                className="bg-blue-800 text-white mt-5 px-4 py-2 rounded-md"
              >
                Add Product
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default AddProductForm;
