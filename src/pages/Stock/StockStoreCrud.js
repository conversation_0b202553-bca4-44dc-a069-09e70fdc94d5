import * as Yup from 'yup';
export const initialValues = {
  vendor_id: '',
  invoice_number: '',
  received_date: '',
  product_id: '',
  // unit_price: '',
  qty: '',
  // warranty_period: '',
  // warranty_period_value: '',
  description: '',
  payment_status: 'paid',
};

export const validationSchema = Yup.object().shape({
  vendor_id: Yup.string().required('Vendor is required'),
  invoice_number: Yup.string()
    .required('Lot number is required'),
  received_date: Yup.date().required('Received date is required').nullable(),
  payment_status: Yup.string().required('Payment status is required'),
});



