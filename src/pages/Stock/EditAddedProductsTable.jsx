import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { Formik, Form, FieldArray } from 'formik';
import * as Yup from 'yup';
import InputField from '@/components/ui/form/InputField';
import Modal from '@/components/ui/Modal';
import ExcelDropZone from '@/components/ui/form/ExcelDropZone';
import * as XLSX from 'xlsx';

const validationSchema = Yup.object().shape({
    serials: Yup.array()
        .of(
            Yup.string().trim().required('Serial is required')
        )
});

const EditAddedProductsTable = ({ allProducts, addedProducts, handleEditProduct, handleDeleteProduct, handleUploadSerial }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [serialsInitialValues, setSerialsInitialValues] = useState([]);
    const [activeTab, setActiveTab] = useState('manual');
    const [uploadedFile, setUploadedFile] = useState(null);
    const [excelSerials, setExcelSerials] = useState([]);
    const [error, setError] = useState('');
    const [duplicates, setDuplicates] = useState([])

    useEffect(() => {
        if (selectedProduct) {
            updateSerialFields(selectedProduct.qty, selectedProduct?.serials || []);
        }
    }, [selectedProduct]);

    const updateSerialFields = (qty, serials) => {
        const uniqueSerials = serials.map((serial) => serial.serial_number);
        const updatedSerials = uniqueSerials.length >= qty
            ? uniqueSerials.slice(0, qty)
            : [...uniqueSerials, ...Array(qty - uniqueSerials.length).fill('')];

        setSerialsInitialValues(updatedSerials);
    };

    const handleOpenModal = (addedProduct) => {
        setSelectedProduct(addedProduct);
        updateSerialFields(addedProduct.qty, addedProduct?.serials || []);
        setIsModalOpen(true);
    };

    const handleCloseModal = () => {
        setIsModalOpen(false);
        setSelectedProduct(null);
        setSerialsInitialValues([]);
    };

    const handleExcelUpload = (file) => {
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];

                const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                const serialsFromExcel = rows.slice(1)
                    .map((row) => row[1])
                    .filter((serial) => serial !== undefined && serial !== null && serial !== '');  // Remove empty entries

                if (serialsFromExcel.length !== selectedProduct.qty) {
                    setError(`Serial count mismatch: Expected ${selectedProduct.qty}, but found ${serialsFromExcel.length}.`);
                    setUploadedFile(null);
                    return;
                }

                const foundDuplicates = serialsFromExcel.filter((item, index) => serialsFromExcel.indexOf(item) !== index);

                setDuplicates(foundDuplicates);
                setUploadedFile(file);
                setExcelSerials(serialsFromExcel);
                setError('');
            };
            reader.readAsArrayBuffer(file);
        } else {
            setUploadedFile(null);
            setExcelSerials([]);
            setError('');
        }
    };

    const handleSubmitExcelSerials = () => {
        if (excelSerials.length !== selectedProduct.qty) {
            toast.error(`Serial count must match the quantity (${selectedProduct.qty})`);
            return;
        }
        handleUploadSerial(selectedProduct.product_id, excelSerials);
        handleCloseModal();
    };


    return (
        <div className='overflow-x-auto'>
            <table className="table-auto w-full text-left">
                <thead>
                    <tr>
                        <th className="border-r text-[#1F2F70] font-medium border-white px-4 py-2 bg-[#E3E9FE]">Product Name</th>
                        {/* <th className="border-r text-[#1F2F70] font-medium border-white px-4 py-2 bg-[#E3E9FE]">Unit Price</th> */}
                        <th className="border-r text-[#1F2F70] font-medium border-white px-4 py-2 bg-[#E3E9FE]">Quantity</th>
                        {/* <th className="border-r text-[#1F2F70] font-medium border-white px-4 py-2 bg-[#E3E9FE]">Total Price</th> */}
                        {/* <th className="border-r text-[#1F2F70] font-medium border-white px-4 py-2 bg-[#E3E9FE]">Warranty Period</th> */}
                        <th className="border-r text-[#1F2F70] font-medium border-white px-4 py-2 bg-[#E3E9FE]">Serials</th>
                        <th className="border text-[#1F2F70] font-medium border-white px-4 py-2 bg-[#E3E9FE]">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {addedProducts.length === 0 ? (
                        <tr>
                            <td colSpan="8" className="text-center py-4">No products added yet.</td>
                        </tr>
                    ) : (
                        addedProducts.map((addedProduct, index) => {
                            const productInfo = allProducts?.data?.find(product => product.id === addedProduct.product_id);
                            const uploadedSerialsCount = addedProduct.serials?.length || 0;

                            return (
                                <tr key={index}>
                                    <td className="border px-4 py-2">{addedProduct.product_name}</td>
                                    {/* <td className="border px-4 py-2">{addedProduct.unit_price}</td> */}
                                    <td className="border px-4 py-2">{addedProduct.qty}</td>
                                    {/* <td className="border px-4 py-2">{(addedProduct.unit_price * addedProduct.qty).toFixed(2)}</td> */}
                                    {/* <td className="border px-4 py-2">{addedProduct.warranty_period_value} {addedProduct.warranty_period}</td> */}
                                    {/* <td className="border px-4 py-2">{addedProduct.description}</td> */}

                                    <td className="border px-4 py-2">
                                        {productInfo?.has_serials ? (
                                            uploadedSerialsCount >= addedProduct.qty ? (
                                                <button
                                                    type="button"
                                                    className="bg-[#E3E9FE] text-[#1F2F70] p-1 rounded-md hover:text-green-700 flex items-center space-x-1"
                                                    onClick={() => handleOpenModal(addedProduct)}
                                                >
                                                    See Uploaded Serials
                                                </button>
                                            ) : (
                                                <button
                                                    type="button"
                                                    className="bg-[#E3E9FE] text-[#1F2F70] p-1 rounded-md hover:text-green-700 flex items-center space-x-1"
                                                    onClick={() => handleOpenModal(addedProduct)}
                                                >
                                                    <Icon icon="mdi:plus" className="w-5 h-5" />
                                                    <span>Upload Serial</span>
                                                </button>
                                            )
                                        ) : (
                                            <span className='p-1 bg-gray-100 rounded-md'>Non Serial Product</span>
                                        )}
                                    </td>
                                    <td className="border px-4 py-2">
                                        <div className='flex space-x-2 items-center'>
                                            <button type="button" className="text-blue-500 hover:text-blue-700" onClick={() => handleEditProduct(index)}>
                                                <Icon icon="mdi:pencil" className="w-5 h-5" />
                                            </button>
                                            <button type="button" onClick={() => handleDeleteProduct(index)} className="text-red-500 hover:text-red-700">
                                                <Icon icon="mdi:trash-can" className="w-5 h-5" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            );
                        })
                    )}
                </tbody>
            </table>

            {isModalOpen && selectedProduct && (
                <Modal
                    activeModal={isModalOpen}
                    onClose={handleCloseModal}
                    title="Upload Serial"
                    className='max-w-4xl'
                >
                    <div className="tabs">
                        <div className='grid grid-cols-2 gap-3'>
                            <div className='col-span-1'>
                                <button
                                    className={`px-4 w-full py-2 ${activeTab === 'manual' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
                                    onClick={() => setActiveTab('manual')}
                                >
                                    Upload Serial Manually
                                </button>
                            </div>
                            <div className='col-span-1'>
                                <button
                                    className={`px-4 w-full py-2 ${activeTab === 'excel' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
                                    onClick={() => setActiveTab('excel')}
                                >
                                    Upload via Excel
                                </button>
                            </div>
                        </div>

                        {
                            activeTab === 'manual' ? <>
                                <Formik
                                    key={selectedProduct.product_id}
                                    initialValues={{ serials: serialsInitialValues }}
                                    validationSchema={validationSchema}
                                    onSubmit={(values) => {
                                        const sanitizedSerials = values.serials.map(serial => serial.trim());
                                        handleUploadSerial(selectedProduct.product_id, sanitizedSerials);
                                        handleCloseModal();
                                    }}
                                    enableReinitialize={true}
                                >
                                    {({ values, handleChange }) => (
                                        <Form>
                                            <div className="p-4">
                                                <h3 className="text-lg font-bold mb-2">
                                                    {selectedProduct?.product_name} - Serial Upload
                                                </h3>

                                                <FieldArray
                                                    name="serials"
                                                    render={() => (
                                                        <div>
                                                            {values.serials.map((serial, index) => (
                                                                <div key={index} className="mb-4">
                                                                    <InputField
                                                                        required
                                                                        label={`Serial ${index + 1}`}
                                                                        name={`serials.${index}`}
                                                                        type="text"
                                                                        value={serial}
                                                                        onChange={handleChange}
                                                                        placeholder={`Enter Serial Number ${index + 1}`}
                                                                    />
                                                                </div>
                                                            ))}
                                                        </div>
                                                    )}
                                                />

                                                <div className="mt-4">
                                                    <button
                                                        type="submit"
                                                        className="bg-blue-600 text-white py-2 px-4 rounded-md"
                                                    >
                                                        Submit
                                                    </button>
                                                </div>
                                            </div>
                                        </Form>
                                    )}
                                </Formik>
                            </> : <div className="p-4">
                                <h3 className="text-lg font-bold mb-2">Upload Excel File</h3>
                                <ExcelDropZone onDrop={handleExcelUpload} height="250px" width='w-full' error={error} />
                                {uploadedFile && (
                                    <div className="mt-4">
                                        <p>Uploaded file: {uploadedFile.name}</p>
                                    </div>
                                )}

                                {excelSerials.length > 0 && (
                                    <div className="mt-4">
                                        <h4 className="text-lg font-semibold mb-2">Serials from Excel:</h4>
                                        <table className="table-auto w-full text-left">
                                            <thead>
                                                <tr>
                                                    <th className="border px-4 py-2">No.</th>
                                                    <th className="border px-4 py-2">Serial Number</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {excelSerials.map((serial, index) => (
                                                    <tr key={index}>
                                                        <td className="border px-4 py-2">{index + 1}</td>
                                                        <td className={`border px-4 py-2 ${duplicates.includes(serial) ? 'bg-red-200' : ''}`}>
                                                            {serial} {duplicates.includes(serial) && <span className="text-red-500">(Duplicate)</span>}
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                )}

                                {excelSerials.length > 0 && (
                                    <div className="mt-4">
                                        <button
                                            className="bg-blue-600 text-white py-2 px-4 rounded-md"
                                            onClick={handleSubmitExcelSerials}
                                            disabled={duplicates.length > 0}
                                        >
                                            Submit Excel Serials
                                        </button>
                                    </div>
                                )}
                            </div>
                        }
                    </div>
                </Modal>
            )}
        </div>
    );
};

export default EditAddedProductsTable;
