import React from 'react';
import { Formik, Form } from 'formik';
import InputSelect from '@/components/ui/form/InputSelect';
import InputField from '@/components/ui/form/InputField';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import * as Yup from 'yup';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import { saveSerials } from '@/store/api/apihandler/serialSlice';

const EditAddedProduct = ({ isOpen, onClose, product, productOptions, onSubmit }) => {
    const warrantyOptions = [
        { label: 'years', value: 1 },
        { label: 'months', value: 0 }
    ];

    const dispatch = useDispatch();

    const validationSchema = Yup.object().shape({
        product_id: Yup.string().required('Product is required'),
        qty: Yup.number()
            .min(1, 'Quantity must be at least 1')
            .required('Quantity is required'),
    });

    const getWarrantyValue = (warranty_period) => {
        return warranty_period === 'years' ? 1 : 0;
    };

    const existingSerials = useSelector((state) => state?.serials?.productSerials[product?.product_id]) || [];
    const handleFormSubmit = (values) => {
        const updatedValues = {
            ...values,
            warranty_period: values.warranty_period === 1 ? 'years' : 'months'
        };

        let adjustedSerials = [];
        if (values.qty > existingSerials.length) {
            adjustedSerials = [...existingSerials, ...Array(values.qty - existingSerials.length).fill('')];
        } else if (values.qty < existingSerials.length) {
            adjustedSerials = existingSerials.slice(0, values.qty);
        } else {
            adjustedSerials = [...existingSerials];
        }

        dispatch(saveSerials({
            productId: product.product_id,
            serials: adjustedSerials,
            qty: values.qty
        }));

        onSubmit(updatedValues);
    };


    return (
        <Modal activeModal={isOpen} onClose={onClose} title="Edit Product" className='max-w-4xl'>
            <Formik
                initialValues={{
                    product_id: product?.product_id,
                    // unit_price: product?.unit_price,
                    qty: product?.qty,
                    // warranty_period: getWarrantyValue(product?.warranty_period),
                    // warranty_period_value: product?.warranty_period_value,
                    description: product?.description
                }}
                validationSchema={validationSchema}
                onSubmit={handleFormSubmit}
                enableReinitialize
            >
                {({ values, handleChange }) => (
                    <Form>
                        <div className="grid grid-cols-2 gap-4">
                            <InputSelect
                                label="Select Product"
                                name="product_id"
                                options={productOptions}
                                value={values.product_id}
                                onChange={handleChange}
                                required
                            />
                            <InputField
                                label="Quantity"
                                name="qty"
                                type='number'
                                placeholder="Enter Quantity"
                                value={values.qty}
                                onChange={handleChange}
                                required
                            />
                            {/* <InputField
                                label="Remark"
                                name="description"
                                type='text'
                                placeholder="Remark"
                                value={values.warranty_period_value}
                                onChange={handleChange}
                            /> */}
                        </div>
                        <div className='flex justify-end mt-4'>
                            <Button text="Update Product" type="submit" className="bg-blue-800 text-white" />
                        </div>
                    </Form>
                )}
            </Formik>
        </Modal>
    );
};

export default EditAddedProduct;
