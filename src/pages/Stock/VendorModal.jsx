import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import {
  createValidationSchema,
  isActiveOptions,
  vendorInitialValues,
} from "@/formHandlers/VendorCrud";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate } from "react-router-dom";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import { toast } from "react-toastify";

const VendorModal = ({ isModalOpen, closeModal }) => {
  const navigate = useNavigate();
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();

  const handleSubmit = async (values, { resetForm }) => {
    const modifiedValues = { ...values };
    console.log(values);

    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        if (values.image instanceof File) {
          formData.append(key, values.image);
        }
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const response = await postApi({
        end_point: "vendors",
        body: formData,
      }).unwrap();
      console.log("Submission successful:", response);
      toast.success("Vendor created successfully!");
      closeModal();
      resetForm();
    } catch (err) {
      console.error("Submission failed:", err);
    }
  };

  return (
    <Modal activeModal={isModalOpen} onClose={closeModal} title="Create Vendor" className={'max-w-4xl'}>
      <div>
        <Formik
          initialValues={vendorInitialValues}
          validationSchema={createValidationSchema()}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting }) => (
            <Form>
                <div className="grid grid-cols-2 gap-5">
                  <InputField
                    label="Name"
                    name="name"
                    type="text"
                    placeholder="Enter name"
                    required
                  />
                  <InputField
                    label="Company"
                    name="company_name"
                    type="text"
                    required
                    placeholder="Enter Company name"
                  />
                  <InputField
                    label="Email"
                    name="email"
                    type="email"
                    placeholder="Company Email"
                  />
                  <InputField
                    label="Number"
                    name="number"
                    type="tel"
                    placeholder="Phone Number"
                    required
                    onInput={(e) => {
                      e.target.value = e.target.value.replace(/[^0-9]/g, "");
                    }}
                  />
                  <InputField
                    label="Website"
                    name="website"
                    type="text"
                    placeholder="Company Website"
                  />
                  <InputField
                    label="Address"
                    name="address"
                    type="text"
                    placeholder="Company Address"
                  />
                  <InputFile
                    label="image"
                    name="image"
                    type="file"
                    title="Upload your file"
                    accept="image/*"
                  />
                  <InputSelect
                    label="Is Active"
                    name="is_active"
                    options={isActiveOptions}
                    placeholder="Select status"
                    required
                  />
                </div>
                <div className="mt-3">
                  <TextAreaField
                    label="Description"
                    name="description"
                    type="text"
                    placeholder="Enter description"
                  />
                </div>

                <div className="w-full text-end">
                  <Button
                    type="submit"
                    className="btn text-center btn-primary"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Submitting..." : "Submit"}
                  </Button>
                </div>
            </Form>
          )}
        </Formik>
      </div>
    </Modal>
  );
};

export default VendorModal;
