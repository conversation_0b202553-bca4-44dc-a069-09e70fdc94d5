import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import Card from '@/components/ui/Card';
import { useGetApiQuery, usePostApiMutation } from '@/store/api/apihandler/commonSlice';
import EditAddedProduct from './EditAddedProduct';
import AddedProductsTable from './AddedProductsTable';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { initialValues, validationSchema } from './StockStoreCrud';
import AddProductForm from './AddProductForm';
import { Icon } from '@iconify/react';
import { clearAllSerials, clearSerials } from '@/store/api/apihandler/serialSlice';
import { useDispatch } from 'react-redux';
import Button from '@/components/ui/Button';
import VendorModal from './VendorModal';
import { PurchaseInfo, SummaryInfo } from './PurchaseSummaryInfo';

const StoreStock = () => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isVendorModalOpen, setIsVendorModalOpen] = useState(false);

    const openVendorModal = () => {
        setIsVendorModalOpen(true);
    };
    const closeVendorModal = () => {
        setIsVendorModalOpen(false);
    }

    const openModal = () => {
        setIsModalOpen(true);
    };

    const closeModal = () => {
        setIsModalOpen(false);
    }

    const [postApi] = usePostApiMutation();
    const navigate = useNavigate();
    const allVendors = useGetApiQuery({ url: "vendors", params: { pagination: 0, is_active: 1 } });
    const allProducts = useGetApiQuery({ url: "products", params: { pagination: 0, is_active: 1 } });

    const [addedProducts, setAddedProducts] = useState([]);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [currentProductIndex, setCurrentProductIndex] = useState(null);
    const [discount, setDiscount] = useState(0);
    const dispatch = useDispatch();

    const vendorOptions = allVendors?.data?.map((vendor) => ({
        label: vendor.name,
        value: vendor.id,
        image: vendor?.image
    }));
    const productOptions = allProducts?.data?.map((product) => ({
        label: product.name,
        value: product.id,
        image: product?.image
    }));

    const handleAddProduct = (values, resetForm) => {
        // Check if the product is already in the addedProducts array
        const isProductAlreadyAdded = addedProducts.some(product => product.product_id === values.product_id);

        if (isProductAlreadyAdded) {
            toast.error('You have already added this product.');
            return;
        }
        setAddedProducts([...addedProducts, {
            product_id: values.product_id,
            product_name: allProducts?.data?.find((product) => product.id === values.product_id)?.name,
            // unit_price: 0,
            qty: values.qty,
            // warranty_period: values.warranty_period,
            // warranty_period_value: String(values.warranty_period_value),
            description: values.description,
            // total_price: values.unit_price * values.qty,
            serials: []
        }]);

        resetForm({
            values: {
                ...values,
                product_id: '',
                // unit_price: '',
                qty: '',
                // warranty_period: '',
                // warranty_period_value: '',
                description: ''
            }
        });
    };


    const handleDeleteProduct = (index) => {
        const productToDelete = addedProducts[index];
        const updatedProducts = addedProducts.filter((_, i) => i !== index);
        setAddedProducts(updatedProducts);
        if (productToDelete && productToDelete.product_id) {
            dispatch(clearSerials({ productId: productToDelete.product_id }));
        }
        toast.success(`${productToDelete.product_name} deleted successfully`);
    };

    const handleEditProduct = (index) => {
        setCurrentProductIndex(index);
        setIsEditModalOpen(true);
    };

    const handleUpdateProduct = (values) => {
        const updatedProducts = [...addedProducts];
        updatedProducts[currentProductIndex] = {
            ...updatedProducts[currentProductIndex],
            product_id: values.product_id,
            product_name: allProducts?.data?.find((product) => product.id === values.product_id)?.name,
            // unit_price: 0,
            qty: values.qty,
            // warranty_period: values.warranty_period,
            // warranty_period_value: String(values.warranty_period_value),
            description: values.description,
            // total_price: values.unit_price * values.qty,
        };
        setAddedProducts(updatedProducts);
        setIsEditModalOpen(false);
    };

    // const subtotal = addedProducts.reduce((acc, product) => acc + (product.unit_price * product.qty), 0);

    // const handleDiscountChange = (e) => {
    //     const value = e.target.value;
    //     if (value === '' || /^\d*$/.test(value)) {
    //         const discountValue = parseFloat(value) || 0;
    //         if (discountValue <= subtotal) {
    //             setDiscount(discountValue);
    //         } else {
    //             setDiscount(subtotal);
    //         }
    //     }
    // };

    // const netPrice = subtotal - discount;

    const handleUploadSerial = (productId, serials) => {
        const updatedProducts = addedProducts.map((product) => {
            if (product.product_id === productId) {
                return {
                    ...product,
                    serials: serials,
                };
            }
            return product;
        });
        setAddedProducts(updatedProducts);
    };

    const handleSubmit = async (values, { resetForm }) => {
        if (addedProducts.length === 0) {
            toast.error(`Please upload at least one product`);
            return;
        }

        const productWithMissingSerials = addedProducts.find(product => {
            const productInfo = allProducts?.data?.find(item => item.id === product.product_id);
            return productInfo?.has_serials && product.serials.length !== product.qty;
        });

        if (productWithMissingSerials) {
            toast.error(`Please upload the correct number of serials for the product: ${productWithMissingSerials.product_name}`);
            return;
        }

        const dataToSubmit = {
            vendor_id: values.vendor_id,
            invoice_number: values.invoice_number,
            received_date: values.received_date,
            // price: subtotal,
            discount_price: discount,
            // total_price: netPrice,
            payment_status: values.payment_status,
            product_details: addedProducts,
        };

        try {
            const response = await postApi({
                end_point: "stocks",
                body: dataToSubmit,
            }).unwrap();
            toast.success('Submission successful');
            navigate("/stock-list");
            resetForm();
        } catch (err) {
            toast.error(err?.data?.errors?.[0] || "Submission failed");
            console.error("Submission failed:", err);
        }

        dispatch(clearAllSerials());
        resetForm({ values: initialValues });
        setAddedProducts([]);
        setDiscount(0);
    };

    return (
        <div>
            <Formik
                initialValues={initialValues}
                onSubmit={handleSubmit}
                validationSchema={validationSchema}
            >
                {({ values, isSubmitting, resetForm, errors, touched }) => (
                    <Form>
                        {console.log(values)}
                        <div className='grid grid-cols-1 lg:grid-cols-5 gap-5'>
                            <div className='lg:col-span-3'>
                                <PurchaseInfo vendorOptions={vendorOptions} touched={touched} errors={errors} openVendorModal={openVendorModal} />
                                <Card
                                    title="Added Products"
                                    noborder={true}
                                    className="w-full mt-5"
                                    titleClass="text-lg font-bold text-gray-800"
                                    headerslot={
                                        <Button onClick={openModal} type="button" className="bg-blue-800 text-white px-4 py-2 rounded-md flex items-center space-x-2">
                                            <div className="w-6 h-6 flex items-center justify-center rounded-full">
                                                <Icon icon="formkit:add" className='w-8 h-8' />
                                            </div>
                                            <span>Add Product</span>
                                        </Button>}
                                >
                                    <AddedProductsTable
                                        allProducts={allProducts}
                                        addedProducts={addedProducts}
                                        handleEditProduct={handleEditProduct}
                                        handleDeleteProduct={handleDeleteProduct}
                                        handleUploadSerial={handleUploadSerial}
                                    />


                                </Card>
                            </div>
                            <div className='col-span-2'>
                                <SummaryInfo touched={touched} errors={errors} isSubmitting={isSubmitting} />
                            </div>
                        </div>

                    </Form>
                )}
            </Formik>

            <AddProductForm
                isModalOpen={isModalOpen}
                closeModal={closeModal}
                productOptions={productOptions}
                handleAddProduct={handleAddProduct}
            />

            {
                isVendorModalOpen && <VendorModal
                    closeModal={closeVendorModal}
                    isModalOpen={isVendorModalOpen}
                />
            }

            {isEditModalOpen && <EditAddedProduct
                isOpen={isEditModalOpen}
                onClose={() => setIsEditModalOpen(false)}
                product={addedProducts[currentProductIndex]}
                productOptions={productOptions}
                onSubmit={handleUpdateProduct}
            />}
        </div>
    );
};

export default StoreStock;
