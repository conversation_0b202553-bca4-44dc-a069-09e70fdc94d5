import Modal from '@/components/ui/Modal';
import React from 'react';

const WarningModal = ({activeModal,onClose}) => {
    return (
        <Modal activeModal={activeModal} onClose={onClose} title="Warning" className={"max-w-2xl"}>
        <div className="text-center">
            <p className="text-red-600 text-lg">Please add some products before submitting the form.</p>
            <button onClick={onClose} className="bg-blue-500 text-white px-4 py-2 mt-4 rounded-md">
                OK
            </button>
        </div>
    </Modal>
    );
};

export default WarningModal;