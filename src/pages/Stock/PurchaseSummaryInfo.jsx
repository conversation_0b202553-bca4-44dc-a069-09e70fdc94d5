import React from 'react';
import Card from '@/components/ui/Card';
import Tooltip from '@/components/ui/Tooltip';
import { Icon } from '@iconify/react';
import InputSelect from '@/components/ui/form/InputSelect';
import InputField from '@/components/ui/form/InputField';
import DateTimePicker from '@/components/ui/form/DateTimePicker';

const PurchaseInfoCard = ({ vendorOptions, touched, errors, openVendorModal }) => (
    <Card
        title="Purchase Information"
        noborder={true}
        className="w-full"
        titleClass="text-lg font-bold text-gray-800"
    >
        <div className="grid grid-cols-1 lg:grid-cols-3  gap-4 mb-4">
            <div className='flex'>
                <div className='w-[95%]'>
                    <InputSelect
                        label="Select Vendor"
                        name="vendor_id"
                        options={vendorOptions}
                        placeholder="Select a vendor"
                        required
                    />
                    {touched.vendor_id && errors.vendor_id && (
                        <div className="text-red-500 text-xs mt-2">{errors.vendor_id}</div>
                    )}
                </div>
                <Tooltip
                    content="Add Vendor"
                    placement="top"
                    arrow
                    animation="Interactive"
                >
                    <div
                        className="ml-3 mt-10 flex-shrink-0"
                        onClick={openVendorModal}
                    >
                        <Icon
                            icon="oui:ml-create-single-metric-job"
                            className="w-6 h-6 font-bold text-gray-400 cursor-pointer hover:text-primary-400"
                        />
                    </div>
                </Tooltip>
            </div>
            <InputField
                label="Lot Number"
                name="invoice_number"
                type='text'
                placeholder="Example: Lot12"
                required
            />
            <DateTimePicker
                name='received_date'
                label='Received Date'
                inputType={'date'}
                required />
        </div>
    </Card>
);

const SummaryCard = ({ touched, errors, isSubmitting }) => (
    <Card
        noborder={true}
        title="Summary"
        titleClass="text-lg font-bold text-gray-800"
    >
        <div className='border-b border-gray-200'>
        </div>
        <div className='flex justify-between mt-2'>
            <div className="w-1/2">
                <p className='mt-2'>Payment Status</p>
            </div>
            <div className="w-1/2">
                <InputSelect
                    name="payment_status"
                    options={[
                        { label: 'Paid', value: 'paid' },
                        { label: 'Unpaid', value: 'unpaid' }
                    ]}
                    placeholder="Select Payment Status"
                    className="w-full"
                />
            </div>
        </div>
        <div className='flex justify-end'>
            <button
                type="submit"
                className="bg-blue-800 text-white font-bold py-2 px-4 mt-4 rounded"
                disabled={isSubmitting}
            >
                {isSubmitting ? "Submitting..." : "Submit"}
            </button>
        </div>
    </Card>
);

export const PurchaseInfo = PurchaseInfoCard;
export const SummaryInfo = SummaryCard;