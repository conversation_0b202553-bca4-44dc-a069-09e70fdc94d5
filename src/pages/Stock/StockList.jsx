import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import DeleteStock from "./DeleteStock";
import { formattedDate } from "@/constant/data";

const StockList = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const { data, isFetching, isLoading } = useGetApiQuery({ url: "stocks", params: apiParam });
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Vendor Id", field: "vendor_id" },
    { label: "Received Date", field: "received_date" },
    { label: "Entry Date", field: "created_at" },
    { label: "Lot Number", field: "invoice_number" },
    { label: "Payment Status", field: "payment_status" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/edit-stock/${data?.data[val]?.id}`);
      },
    },
    {
      name: "view", 
      icon  : "heroicons-outline:eye",
      onClick: (val) => {
        navigate(`/view-stock/${data?.data[val]?.id}`);
      }
    },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      vendor_id: item.vendor?.name,
      invoice_number: item?.invoice_number,
      received_date: formattedDate(item?.received_date),
      created_at:  formattedDate(item?.created_at),
      payment_status: (
        <Badge
          className={
            item.payment_status === "paid"
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {" "}
          {item.payment_status === 'paid' ? "Paid" : "Due"}
        </Badge>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        title="Stock List"
        loading={isLoading || isFetching}
        columns={columns}
        actions={actions}
        goto={"Create New Stock"}
        gotoLink={"/store-stock"}
        changePage={changePage}
        data={tableData}
        filter={filter}
        setFilter={setApiParam}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
      <DeleteStock
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </div>
  );
};

export default StockList;
