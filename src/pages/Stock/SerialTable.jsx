import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import Pagination from '@/components/partials/common-table/pagination';
import TableSkeleton from '@/components/ui/TableSkeleton';
import { useGetApiQuery } from '@/store/api/apihandler/commonSlice';
import { Icon } from '@iconify/react';

const SerialTable = ({ productId }) => {
    const { id } = useParams();
    const [apiParam, setApiParam] = useState(0);
    const { data: apiResponse, isLoading } = useGetApiQuery({ url: "serials-by-products", params: { product_id: productId, stock_entry_id: id, page: apiParam } });
    console.log(apiResponse);
    const columns = ['Serial Number', 'Status'];
    return (
        <div>
            <div className='flex items-center justify-center bg-blue-100 space-x-2 mb-3 mt-3 p-3 rounded-lg'>
                <h6>Uploaded Serials</h6>
            </div>
            {isLoading ? <TableSkeleton columns={columns} /> : (
                <table className="w-full border-collapse">
                    <thead>
                        <tr>
                            <th className="border p-2 text-left">Serial Number</th>
                            <th className="border p-2 text-left">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {apiResponse?.data?.length > 0 ? (
                            apiResponse?.data.map((serial, index) => (
                                <tr key={index}>
                                    <td className="border p-2">{serial?.serial_number}</td>
                                    <td className="border p-2 flex items-center space-x-2">
                                        <Icon icon={serial?.sold ? "mdi:check-circle" : "mdi:present-outline"} className={`text-${serial?.sold ? 'green' : 'yellow'}-500`} />
                                        <span>{serial?.sold ? 'Sold' : 'Available'}</span>
                                    </td>
                                </tr>
                            ))
                        ) : (
                            <tr>
                                <td colSpan="2" className="border p-2 text-center text-gray-500">No data available</td>
                            </tr>
                        )}
                    </tbody>
                </table>
            )}
            <Pagination
                totalPages={Math.ceil(apiResponse?.total / apiResponse?.per_page)}
                currentPage={apiResponse?.current_page}
                handlePageChange={setApiParam}
            />
        </div>
    );
};

export default SerialTable;