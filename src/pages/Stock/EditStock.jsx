import React, { useState, useEffect } from 'react';
import { Formik, Form } from 'formik';
import { useGetApiQuery, useGetApiWithIdQuery, useUpdateApiJsonMutation } from '@/store/api/apihandler/commonSlice';
import InputSelect from '@/components/ui/form/InputSelect';
import InputField from '@/components/ui/form/InputField';
import DateTimePicker from '@/components/ui/form/DateTimePicker';
import { Icon } from '@iconify/react';
import EditAddedProductsTable from './EditAddedProductsTable';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import Card from '@/components/ui/Card';
import EditAddedProduct from './EditAddedProduct';
import AddProductForm from './AddProductForm';
import Button from '@/components/ui/Button';

const EditStock = () => {
    const { id } = useParams();
    const { data: stock, isLoading } = useGetApiWithIdQuery(["stocks-edit", id]);
    const { data: serials } = useGetApiWithIdQuery(["serials-by-stocks", id]);

    const allVendors = useGetApiQuery({ url: "vendors", params: { pagination: 0, is_active: 1 } });
    const allProducts = useGetApiQuery({ url: "products", params: { pagination: 0, is_active: 1 } });

    // const vendorOptions = allVendors?.data?.map((vendor) => ({ label: vendor.name, value: vendor.id }));
    const vendorOptions = allVendors?.data?.map((vendor) => ({
        label: vendor.name,
        value: vendor.id,
        image: vendor.image,
    }));

    // const productOptions = allProducts?.data?.map((product) => ({ label: product.name, value: product.id }));
    const productOptions = allProducts?.data?.map((product) => ({
        label: product.name,
        value: product.id,
        image: product.image,
    }));

    const [updateApiJson] = useUpdateApiJsonMutation();
    const navigate = useNavigate();

    const [addedProducts, setAddedProducts] = useState([]);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [currentProductIndex, setCurrentProductIndex] = useState(null);
    const [discount, setDiscount] = useState(0);
    const [isAddProductModalOpen, setIsAddProductModalOpen] = useState(false);

    useEffect(() => {
        if (stock && serials) {
            const formattedProducts = stock?.stock_entry_detail.map((detail) => ({
                product_id: detail.product_id,
                product_name: detail.product.name,
                // unit_price: detail.unit_price,
                qty: detail.qty,
                // warranty_period: detail.warranty_period,
                // warranty_period_value: detail.warranty_period_value,
                description: detail.description,
                // total_price: detail.unit_price * detail.qty,
                // serials: detail.product.serials || [],
                serials: serials && serials?.filter(serial => serial?.product_id === detail?.product_id),
            }));

            setAddedProducts(formattedProducts);
            setDiscount(parseFloat(stock.discount_price));
        }
    }, [stock,serials]);

    // Function to handle adding new products
    const handleAddProduct = (values, resetForm) => {
        // Check if the product is already in the addedProducts array
        const isProductAlreadyAdded = addedProducts.some(product => product.product_id === values.product_id);

        if (isProductAlreadyAdded) {
            toast.error('You have already added this product.');
            return;
        }

        setAddedProducts([...addedProducts, {
            product_id: values.product_id,
            product_name: allProducts?.data?.find((product) => product.id === values.product_id)?.name,
            // unit_price: values.unit_price,
            qty: values.qty,
            // warranty_period: values.warranty_period,
            // warranty_period_value: String(values.warranty_period_value),
            description: values.description,
            // total_price: values.unit_price * values.qty,
            serials: [],
        }]);

        // Reset the form fields after adding the product
        resetForm({
            values: {
                ...values,
                product_id: '',
                // unit_price: '',
                // subtotal: '',
                qty: '',
                // warranty_period: '',
                // warranty_period_value: '',
                description: ''
            }
        });
        setIsAddProductModalOpen(false);
    };

    const handleDeleteProduct = (index) => {
        const updatedProducts = addedProducts.filter((_, i) => i !== index);
        setAddedProducts(updatedProducts);
    };

    // Handle editing a product
    const handleEditProduct = (index) => {
        setCurrentProductIndex(index);
        setIsEditModalOpen(true);
    };

    // Handle updating a product
    const handleUpdateProduct = (values) => {
        const updatedProducts = [...addedProducts];

        const updatedProductName = productOptions.find(
            (option) => option.value === values.product_id
        )?.label;

        const currentProduct = updatedProducts[currentProductIndex];

        if (values.qty < currentProduct.qty) {
            // Clear the serials array if the qty is reduced
            currentProduct.serials = currentProduct.serials.slice(0, values.qty);
            toast.info("Quantity reduced. Extra serials removed.");
        }

        updatedProducts[currentProductIndex] = {
            ...currentProduct,
            product_id: values.product_id,
            product_name: updatedProductName,
            // unit_price: values.unit_price,
            qty: values.qty,
            // warranty_period: values.warranty_period,
            // warranty_period_value: String(values.warranty_period_value),
            description: values.description,
            // total_price: values.unit_price * values.qty,
        };

        setAddedProducts(updatedProducts);
        setIsEditModalOpen(false);
    };

    const handleUploadSerial = (productId, serials) => {
        const updatedProducts = addedProducts.map((product) => {
            if (product.product_id === productId) {
                return { ...product, serials };
            }
            return product;
        });
        setAddedProducts(updatedProducts);
    };

    // const subtotal = addedProducts.reduce((acc, product) => acc + (product.unit_price * product.qty), 0);
    // const netPrice = subtotal - discount;

    // const handleDiscountChange = (e) => {
    //     const value = e.target.value;
    //     const discountValue = parseFloat(value) || 0;
    //     setDiscount(discountValue <= subtotal ? discountValue : subtotal);
    // };

    // Handle submitting the form
    const handleSubmit = async (values, { resetForm }) => {
        console.log("Submitted values:", {
            vendor_id: values.vendor_id,
            invoice_number: values.invoice_number,
            received_date: values.received_date,
            payment_status: values.payment_status,
            product_details: addedProducts,
            // price: subtotal,
            // discount_price: discount,
            // total_price: netPrice,
        });
        if (addedProducts.length === 0) {
            toast.error("Please add at least one product.");
            return;
        }

        // Ensure serials match the quantity
        const productWithMissingSerials = addedProducts.find(product => {
            const productInfo = allProducts?.data?.find(item => item.id === product.product_id);
            return productInfo?.has_serials && (!product.serials || product.serials.length !== product.qty);
        });

        if (productWithMissingSerials) {
            toast.error(`Please upload the correct number of serials for the product: ${productWithMissingSerials.product_name}`);
            return;
        }

        // Prepare the data for submission
        const dataToSubmit = {
            vendor_id: values.vendor_id,
            invoice_number: values.invoice_number,
            received_date: values.received_date,
            payment_status: values.payment_status,
            // price: subtotal,
            // discount_price: discount,
            // total_price: netPrice,
            product_details: addedProducts.map(product => {
                const productInfo = allProducts?.data?.find(item => item.id === product.product_id);

                // Map over the serials and ensure only serial_number (or the plain serial string) is sent
                let serialsToSubmit = product.serials;
                if (productInfo?.has_serials) {
                    serialsToSubmit = product.serials.map(serial => {
                        // If serial is an object, extract the serial_number, otherwise, return it as-is
                        return typeof serial === 'object' && serial.serial_number ? serial.serial_number : serial;
                    });
                }

                return {
                    product_id: product.product_id,
                    // warranty_period: product.warranty_period,
                    // warranty_period_value: product.warranty_period_value,
                    // unit_price: product.unit_price,
                    // total_price: product.total_price,
                    qty: product.qty,
                    description: product.description,
                    serials: serialsToSubmit,  
                };
            }),
        };

        console.log(dataToSubmit, 'data to submit');

        try {
            await updateApiJson({
                end_point: `stocks/${id}`,
                method: "PUT",
                body: dataToSubmit,
            }).unwrap();
            toast.success('Stock updated successfully');
            navigate("/stock-list");
        } catch (err) {
            // toast.error(err?.data?.errors?.[0] || "Update failed");
            console.error("Update failed:", err);
        }
    };


    if (isLoading) {
        return <p>Loading...</p>;
    }

    return (
        <div>
            <Formik
                initialValues={{
                    vendor_id: stock.vendor_id || '',
                    invoice_number: stock.invoice_number || '',
                    received_date: stock.received_date || '',
                    payment_status: stock.payment_status || '',
                }}
                onSubmit={handleSubmit}
            >
                {({ values, isSubmitting, resetForm, touched, errors }) => (
                    <Form>
                        <div className='grid grid-cols-1 lg:grid-cols-5 gap-5'>
                            <div className='lg:col-span-3'>
                                <Card
                                    title="Purchase Information"
                                    className="w-full"
                                    noborder={true}
                                    titleClass="text-lg font-bold text-gray-800"
                                >
                                    <div className="grid grid-cols-1 lg:grid-cols-3  gap-4 mb-4">
                                        <InputSelect
                                            label="Select Vendor"
                                            name="vendor_id"
                                            options={vendorOptions}
                                            placeholder="Select a vendor"
                                            required
                                        />
                                        <InputField
                                            label="Lot Number"
                                            name="invoice_number"
                                            type='text'
                                            placeholder="Example : Lot12"
                                            required
                                        />
                                        <DateTimePicker
                                            name='received_date'
                                            label='Received Date'
                                            inputType={'date'}
                                            required
                                        />
                                    </div>
                                </Card>

                                <Card
                                    title="Added Products"
                                    noborder={true}
                                    className="w-full mt-5"
                                    titleClass="text-lg font-bold text-gray-800"
                                    headerslot={
                                        <Button onClick={() => setIsAddProductModalOpen(true)} type="button" className="bg-blue-800 text-white px-4 py-2 rounded-md flex items-center space-x-2">
                                            <div className="w-6 h-6 flex items-center justify-center rounded-full">
                                                <Icon icon="formkit:add" className='w-8 h-8' />
                                            </div>
                                            <span>Select Product</span>
                                        </Button>}
                                >
                                    <EditAddedProductsTable
                                        allProducts={allProducts}
                                        addedProducts={addedProducts}
                                        handleEditProduct={handleEditProduct}
                                        handleDeleteProduct={handleDeleteProduct}
                                        handleUploadSerial={handleUploadSerial}
                                    />
                                </Card>

                            </div>
                            <div className='col-span-2'>
                                <Card
                                    title="Summary"
                                    className=""
                                    noborder={true}
                                    titleClass="text-lg font-bold text-gray-800"
                                >
                                    <div className='flex justify-between mt-2'>
                                        <div className="w-1/2">
                                            <p>Payment Status</p>
                                        </div>
                                        <div className="w-1/2">
                                            <InputSelect
                                                name="payment_status"
                                                options={[
                                                    { label: 'Paid', value: 'paid' },
                                                    { label: 'Unpaid', value: 'unpaid' }
                                                ]}
                                                placeholder="Select Payment Status"
                                                className="w-full"
                                            />
                                            {touched.payment_status && errors.payment_status && (
                                                <div className="text-red-500 text-xs mt-2">{errors.payment_status}</div>
                                            )}
                                        </div>
                                    </div>
                                    <div className='flex justify-end'>
                                        <button
                                            type="submit"
                                            className="bg-blue-800 text-white font-bold py-2 px-4 mt-4 rounded"
                                            disabled={isSubmitting}
                                        >
                                            {isSubmitting ? "Updating..." : "Update"}
                                        </button>
                                    </div>
                                </Card>
                            </div>
                        </div>
                    </Form>
                )}
            </Formik>

            <AddProductForm
                isOpen={isAddProductModalOpen}
                isModalOpen={isAddProductModalOpen}
                closeModal={() => setIsAddProductModalOpen(false)}
                productOptions={productOptions}
                handleAddProduct={handleAddProduct}
            />
            <EditAddedProduct
                isOpen={isEditModalOpen}
                onClose={() => setIsEditModalOpen(false)}
                product={addedProducts[currentProductIndex]}
                productOptions={productOptions}
                onSubmit={handleUpdateProduct}
            />
        </div>
    );
};

export default EditStock;
