import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import AcceptTask from "./AcceptTask";

const SupportList = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const allSupports = useGetApiQuery({ url: "support-list", params: apiParam });
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);

  const handleModal = (task) => {
    setSelectedTask(task);
    setShowModal(!showModal);
  };

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Sl", field: "index" },
    { label: "Customer Name", field: "client_name" },
    { label: "Support Type", field: "support_type" },
    { label: "Assign Date", field: "assign_date" },
    { label: "Status", field: "status" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "View",
      icon: "heroicons-outline:eye",
      onClick: (val) => {
        const task = allSupports?.data?.data[val];
        navigate(`/task-details/${task?.id}`);
      },
    },
  ];

  const getDate = (date) => {
    const options = { year: "numeric", month: "long", day: "numeric" };
    return new Date(date).toLocaleDateString("en-US", options);
  };

  const tableData = allSupports?.data?.data?.map((item, index) => {
    return {
      index: index + 1,
      client_name: item.name,
      support_type: (
        <Badge className="bg-indigo-200 text-black">
          {item.support_type?.name}
        </Badge>
      ),
      assign_date: getDate(item.assign_date),
      status: (
        <Badge
          className={
            (item.status === "completed" && `bg-success-500 text-white`) ||
            (item.status === "pending" && `bg-warning-500 text-white`) ||
            (item.status === "on_going" && `bg-info-500 text-black`) ||
            (item.status === "cancelled" && `bg-danger-500 text-white`) ||
            (item.status === "forwarded" && `bg-primary-500 text-white`)
          }
        >
          {item.status}
        </Badge>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        title="Support List"
        columns={columns}
        actions={actions}
        changePage={changePage}
        data={tableData}
        filter={filter}
        setFilter={setApiParam}
        currentPage={allSupports?.data?.current_page}
        totalPages={Math.ceil(
          allSupports?.data?.total / allSupports?.data?.per_page
        )}
      />

      {showModal && (
        <AcceptTask
          activeModal={showModal}
          task={selectedTask}
          onClose={handleModal}
        />
      )}
    </div>
  );
};

export default SupportList;
