import React from 'react';
import { Icon } from "@iconify/react";

const SummaryCard = ({ icon, iconColor, title, value, valueColor, description }) => (
    <div className="rounded-lg p-4 shadow-md bg-gray-50 flex flex-col items-start">
        <div className="flex items-center mb-2">
            <Icon icon={icon} className={`${iconColor} text-2xl mr-2`} />
            <p className="font-semibold text-xs text-gray-800">{title}</p>
        </div>
        <div className="flex-grow flex items-center justify-center w-full">
            <p className={`font-bold text-sm ${valueColor} text-lg`}> ৳{value.toFixed(2)}</p>
        </div>
    </div>
);

const FinancialSummary = ({ supportAccounts = [], supportExpenses = [] }) => {
    const prepaidAccounts = supportAccounts.filter(account => account.payment_type === 'prepaid');
    const postpaidAccounts = supportAccounts.filter(account => account.payment_type === 'postpaid');

    const totalPrepaidGiven = prepaidAccounts.reduce((sum, account) => sum + parseFloat(account.paid_amount), 0);
    const totalPrepaidSpent = supportExpenses.reduce((sum, expense) => sum + parseFloat(expense.amount), 0);
    const prepaidBalance = totalPrepaidGiven - totalPrepaidSpent;

    const totalPostpaidSpent = supportExpenses.reduce((sum, expense) => {
        const postpaidAccount = postpaidAccounts.find(account => account.support_expense_id === expense.id);
        return postpaidAccount ? sum + parseFloat(expense.amount) : sum;
    }, 0);

    return (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
            <SummaryCard
                icon="mdi:cash-multiple"
                iconColor="text-yellow-500"
                title="Total Prepaid Given"
                value={totalPrepaidGiven}
                valueColor="text-blue-800"
                description="Amount given in advance"
            />
            <SummaryCard
                icon="mdi:cash-check"
                iconColor="text-purple-500"
                title="Total Prepaid Spent"
                value={totalPrepaidSpent}
                valueColor="text-blue-800"
                description="Amount spent from advance"
            />
            <SummaryCard
                icon="mdi:cash-refund"
                iconColor={prepaidBalance > 0 ? "text-green-500" : "text-red-500"}
                title={prepaidBalance > 0 ? "Remaining Balance" : "Reimbursement Needed"}
                value={Math.abs(prepaidBalance)}
                valueColor={prepaidBalance > 0 ? "text-green-600" : "text-red-600"}
                description={prepaidBalance > 0 ? "Balance to return" : "Amount to reimburse"}
            />
            <SummaryCard
                icon="mdi:cash-plus"
                iconColor="text-red-500"
                title="Postpaid Due"
                value={totalPostpaidSpent}
                valueColor="text-blue-800"
                description="Amount to be reimbursed"
            />
        </div>
    );
};

export default FinancialSummary;
