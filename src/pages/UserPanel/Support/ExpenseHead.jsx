import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { Icon } from "@iconify/react";
import InputField from '@/components/ui/form/InputField';
import Text<PERSON>reaField from '@/components/ui/form/TextAreaField';
import FileUpload from '@/components/ui/form/FileUpload';
import { useParams } from 'react-router-dom';
import Button from '@/components/ui/Button';
import { useGetApiQuery, usePostApiMutation, useUpdateApiJsonMutation } from '@/store/api/apihandler/commonSlice';
import InputSelect from '@/components/ui/form/InputSelect';
import { toast } from 'react-toastify';
import CreatePdfModal from './SupportExpenses/CreatePdfModal';
import DeleteModal from './DeleteModal';

const ExpenseHead = ({ supportExpenses = [], support }) => {
    console.log(support, 'this is support')
    const { data: expenseHeads } = useGetApiQuery({ url: "expense-heads", params: { is_active: 1, pagination: 0 } });
    const { id } = useParams();

    const expenseHeadOptions = expenseHeads?.map((head) => ({
        label: head.name,
        value: head.id,
    })) || [];

    const [pdfModal, setPdfModal] = useState(false);
    const [formVisible, setFormVisible] = useState(false);
    const [formMode, setFormMode] = useState("create");
    const [selectedExpenseId, setSelectedExpenseId] = useState(null);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);

    const [postApi] = usePostApiMutation();
    const [updateApiJson] = useUpdateApiJsonMutation();

    const { data: expenseData } = useGetApiQuery(
        { url: `user/support-expenses/${selectedExpenseId}` },
        { skip: !(formMode === "edit" && selectedExpenseId) }
    );

    console.log(expenseData, "expenseData");

    const initialValues = formMode === "edit" && expenseData ? expenseData : {
        support_id: id,
        expense_head_id: '',
        amount: '',
        description: '',
        // attachment: null,
    };

    const handleExpenseSubmit = async (values, { resetForm }) => {
        const request = formMode === "edit" && selectedExpenseId
            ? updateApiJson({ end_point: `user/support-expenses/${selectedExpenseId}`, body: values })
            : postApi({ end_point: "user/support-expenses", body: values });
        try {
            await request.unwrap();
            toast.success(`Expense ${formMode === "edit" ? 'updated' : 'submitted'} successfully!`);
            resetForm();
            resetFormState();
        } catch (error) {
            console.error(`Error ${formMode === "edit" ? 'updating' : 'submitting'} expense:`, error);
        }
    };

    const data = {
        id: id
    }
    // const handleCloseAccount = async()=>{
    //     try {
    //         await postApi({ end_point: `user/expense-complete/${id}`, body: data }).unwrap();
    //         toast.success(`Account closed successfully!`);
    //     } catch (error) {
    //         console.error(`Error closing account:`, error);
    //     }
    // }

    const resetFormState = () => {
        setFormVisible(false);
        setFormMode("create");
        setSelectedExpenseId(null);
    };

    const handleDeleteClick = (expenseId) => {
        setSelectedExpenseId(expenseId);
        setDeleteModalOpen(true);
    };

    return (
        <div>
            {!formVisible ? (
                <>
                    {supportExpenses.length > 0 ? (
                        <ExpenseTable
                            supportExpenses={supportExpenses}
                            onEdit={(expenseId) => {
                                setFormMode("edit");
                                setFormVisible(true);
                                setSelectedExpenseId(expenseId);
                            }
                            }
                            onDelete={handleDeleteClick}
                        />
                    ) : (
                        <p className="text-gray-500 mt-4">No support expenses have been added.</p>
                    )}
                    <div className="mt-4 flex justify-end gap-3">
                        {/* <Button
                            onClick={() => {handleCloseAccount()}}
                            className="bg-blue-100 text-black font-semibold py-2 px-4 rounded-lg shadow-md"
                        >
                            Close Account
                        </Button> */}
                       {support?.expense_status === "on_going" && <Button
                            onClick={() => setFormVisible(true)}
                            className="bg-blue-500 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200"
                        >
                            Add Expense
                        </Button>}
                    </div>
                </>
            ) : (
                <ExpenseForm
                    initialValues={initialValues}
                    expenseHeadOptions={expenseHeadOptions}
                    formMode={formMode}
                    onSubmit={handleExpenseSubmit}
                    onCancel={resetFormState}
                    pdfModal={pdfModal}
                    setPdfModal={setPdfModal}
                />
            )}
            {deleteModalOpen && (
                <DeleteModal
                    isModalOpen={deleteModalOpen}
                    setDeleteModal={setDeleteModalOpen}
                    expenseId={selectedExpenseId}
                />
            )}
        </div>
    );
};

const ExpenseTable = ({ supportExpenses, onEdit, onDelete }) => (
    <table className="w-full border rounded-lg shadow-sm">
        <thead>
            <tr className="bg-gray-100 text-gray-700">
                <th className="p-3 border">Expense Head</th>
                <th className="p-3 border">Amount</th>
                <th className="p-3 border">Description</th>
                <th className="p-3 border">Action</th>
            </tr>
        </thead>
        <tbody>
            {supportExpenses.map((expense) => (
                <tr key={expense.id} className="text-gray-700 hover:bg-gray-100 transition-colors duration-200">
                    <td className="p-3 border">{expense.expense_head.name}</td>
                    <td className="p-3 border">{expense.amount}</td>
                    <td className="p-3 border">{expense.description || "N/A"}</td>
                    <td className="p-3 border flex justify-center gap-2">
                        <Button
                            onClick={() => onEdit(expense.id)}
                            className="bg-yellow-200 text-yellow-700 px-3 py-1 rounded-md hover:bg-yellow-300 transition-colors duration-200"
                        >
                            <div className="flex items-center gap-2">
                                <Icon icon="mdi:pencil-outline" className="mr-1" />
                                Edit
                            </div>
                        </Button>
                        <Button
                            onClick={() => onDelete(expense.id)}
                            className="bg-red-200 text-red-700 px-3 py-1 rounded-md hover:bg-red-300 transition-colors duration-200"
                        >
                            <div className="flex items-center gap-2">
                                <Icon icon="mdi:delete-outline" className="mr-1" />
                                Delete
                            </div>
                        </Button>
                    </td>
                </tr>
            ))}
        </tbody>
    </table>
);

const ExpenseForm = ({ initialValues, expenseHeadOptions, formMode, onSubmit, onCancel, pdfModal, setPdfModal }) => {
    const getExpenseHeadLabel = (id) => {
        const selectedOption = expenseHeadOptions.find(option => option.value === id);
        return selectedOption ? selectedOption.label : '';
    };

    const validationSchema = Yup.object({
        support_id: Yup.number().required('Support ID is required'),
        expense_head_id: Yup.number().required('Expense Head ID is required'),
        amount: Yup.number().required('Amount is required').min(0.01, 'Amount must be greater than 0'),
        description: Yup.string().when('expense_head_id', (expense_head_id, schema) => {
            const label = getExpenseHeadLabel(expense_head_id);
            return label === "Others" ? schema.required('Description is required') : schema;
        }),
    });

    return (
        <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={onSubmit} enableReinitialize={true}>
            {({ values }) => (
                <Form>
                    <div className="grid grid-cols-2 gap-6">
                        <InputSelect label="Expense Type" name="expense_head_id" options={expenseHeadOptions} required />
                        <InputField label="Amount" name="amount" type="number" placeholder="Enter Amount" required />
                        {/* <FileUpload title="Upload your attachment" name="attachment" endpoint="file-upload" valueKey="file" accept="image/*,application/pdf" /> */}
                        {/* <div className="w-full">
                            <label className="block text-gray-700 font-semibold mb-2">Make PDF (If you have multiple files)</label>
                            <button
                                onClick={() => setPdfModal(true)}
                                type="button"
                                className="w-full flex items-center justify-center bg-gray-900 text-white font-semibold py-2 rounded-lg shadow-md hover:bg-gray-800 transition-colors duration-200"
                            >
                                <Icon icon="teenyicons:pdf-solid" className="mr-2 text-lg" />
                                Make PDF
                            </button>
                        </div> */}
                    </div>
                    <div className="w-full mt-3">
                        <TextAreaField
                            label="Description"
                            name="description"
                            placeholder="Enter Description"
                            required={getExpenseHeadLabel(values.expense_head_id) === "Others"}
                        />
                    </div>
                    <div className="mt-6 flex justify-end gap-3">
                        <Button onClick={onCancel} className="bg-gray-500 text-white font-semibold py-3 rounded-lg shadow-md hover:bg-gray-600 transition-colors duration-200">
                            Cancel
                        </Button>
                        <Button type="submit" className="bg-blue-500 text-white font-semibold py-3 rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200">
                            {formMode === "edit" ? "Update" : "Submit"}
                        </Button>
                    </div>
                    {pdfModal && <CreatePdfModal isModalOpen={pdfModal} setPdfModal={setPdfModal} />}
                </Form>
            )}
        </Formik>
    );
};

export default ExpenseHead;
