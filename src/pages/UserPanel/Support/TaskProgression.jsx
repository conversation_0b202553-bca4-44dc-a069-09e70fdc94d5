import React from "react";
import { Icon } from "@iconify/react";

const TaskProgression = ({ status }) => {
  //   const steps = [
  //     { key: "pending", label: "Pending", icon: "mdi:clock-outline" },
  //     { key: "forwarded", label: "Forwarded", icon: "mdi:arrow-right-bold" },
  //     { key: "on_going", label: "Ongoing", icon: "mdi:play-circle-outline" },
  //     { key: "cancelled", label: "Cancelled", icon: "mdi:close-circle-outline" },
  //     { key: "completed", label: "Completed", icon: "mdi:trophy-outline" },
  //   ];

  const steps = [
    { key: "accept", label: "Accept", icon: "mdi:clock-outline" },
    { key: "forwarded", label: "Forwarded", icon: "mdi:arrow-right-bold" },
    { key: "on_going", label: "Ongoing", icon: "mdi:play-circle-outline" },
    { key: "cancelled", label: "Cancelled", icon: "mdi:close-circle-outline" },
    { key: "completed", label: "Completed", icon: "mdi:trophy-outline" },
  ];

  //   ['accept', on_going, 'complete',  'cancel'])->default('accept');

  const currentStepIndex = steps.findIndex((step) => step.key === status);

  return (
    <div className="p-4 bg-gray-50 rounded-lg shadow-lg w-full">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">
        Task TimeLine
      </h2>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.key} className="flex flex-col items-center space-y-2">
            {/* Step Icon */}
            <Icon
              icon={step.icon}
              className={`text-2xl ${
                index === currentStepIndex ? "text-blue-500" : "text-gray-300"
              }`}
            />
            <span
              className={`text-sm ${
                index === currentStepIndex
                  ? "text-blue-600 font-semibold"
                  : "text-gray-500"
              }`}
            >
              {step.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TaskProgression;
