import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { Icon } from "@iconify/react";
import {
  useGetApiWithIdQuery,
  usePostApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { toast } from "react-toastify";
import <PERSON><PERSON><PERSON><PERSON>ield from "@/components/ui/form/TextAreaField";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import TaskProgression from "./TaskProgression";
import ExpenseHead from "./ExpenseHead";
import FinancialSummary from "./FinancialSummary";

const DetailSection = ({ icon, iconColor, title, children }) => (
  <div className="bg-gray-50 p-4 rounded-lg shadow-sm">
    <div className="flex items-center">
      <Icon icon={icon} className={`${iconColor} text-xl mr-3`} />
      <div>
        <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
        <div className="text-gray-700">{children}</div>
      </div>
    </div>
  </div>
);

const SupportDetails = () => {
  const { id: taskId } = useParams();
  const { data: support } = useGetApiWithIdQuery(["support-details", taskId]);
  const [postApi] = usePostApiMutation();
  const [cancelTask, setCancelTask] = useState(false);

  const validationSchema = Yup.object().shape({
    description: cancelTask
      ? Yup.string().required("Reason for cancellation is required")
      : Yup.string(),
  });

  const handleTaskAction = async (action) => {
    const endpoints = {
      accept: `task-accept/${taskId}`,
      complete: `task-complete/${taskId}`,
      forward: `task-reject/${taskId}`,
      cancel: `task-cancel/${taskId}`,
    };

    try {
      await postApi({ end_point: endpoints[action], body: taskId }).unwrap();
      setCancelTask(false);
      toast.success(`Task ${action} successfully`);
    } catch (err) {
      console.error(`Failed to ${action} task:`, err);
    }
  };

  const statusColors = {
    pending: "text-yellow-500",
    on_going: "text-blue-500",
    completed: "text-green-500",
    cancelled: "text-red-500",
    forwarded: "text-purple-500",
  };

  const actionButtons = {
    pending: [
      {
        action: "forward",
        label: "Forward Task",
        color: "bg-red-300 text-gray-600",
      },
      {
        action: "accept",
        label: "Accept Task",
        color: "bg-blue-600 text-white",
      },
    ],
    on_going: [
      {
        action: () => setCancelTask(true),
        label: "Cancel",
        color: "bg-red-500 text-white",
      },
      {
        action: "complete",
        label: "Mark as Completed",
        color: "bg-green-600 text-white",
      },
    ],
    completed: [
      {
        action: () => toast.info("Task already completed"),
        label: "Task Completed",
        color: "bg-green-600 text-white",
      },
    ],
  };

  return (
    <div className="grid grid-cols-12 gap-4">
      <div className="col-span-5 p-8 bg-white rounded-lg shadow-lg space-y-6 self-start">
        <h2 className="text-2xl font-bold text-gray-800 border-b pb-2">
          Task Details
        </h2>

        <div>
          <TaskProgression status={support?.status} />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <DetailSection
            icon="mdi:cog-outline"
            iconColor="text-blue-500"
            title="Support Type"
          >
            <p>{support?.support_type?.name}</p>
            <p className="text-gray-500 text-sm mt-1">
              {support?.support_type?.description}
            </p>
          </DetailSection>
          <DetailSection
            icon="mdi:file-document-outline"
            iconColor="text-green-500"
            title="Client Invoice"
          >
            <p>Invoice No: {support?.sale?.invoice_no}</p>
            <p>
              Grand Total: {support?.sale?.grand_total} / Paid:{" "}
              {support?.sale?.paid_amount} / Due: {support?.sale?.due_amount}
            </p>
            <p>Payment Status: {support?.sale?.payment_status}</p>
          </DetailSection>
          <DetailSection
            icon="mdi:account-outline"
            iconColor="text-purple-500"
            title="Assigned Employee(s)"
          >
            <p>
              {support?.employees?.map((employee) => employee.name).join(", ")}
            </p>
          </DetailSection>
          <DetailSection
            icon="mdi:clipboard-check-outline"
            iconColor={statusColors[support?.status] || "text-gray-500"}
            title="Task Status"
          >
            <p
              className={`${statusColors[support?.status] || "text-gray-600"}`}
            >
              {support?.status
                ?.replace("_", " ")
                .replace(/^\w/, (c) => c.toUpperCase()) || "No status Found"}
            </p>
          </DetailSection>
          <DetailSection
            icon="mdi:phone-outline"
            iconColor="text-red-500"
            title="Contact Info"
          >
            <p>{support?.contact_info}</p>
          </DetailSection>
          <DetailSection
            icon="mdi:map-marker-outline"
            iconColor="text-blue-500"
            title="Address"
          >
            <p>{support?.address}</p>
          </DetailSection>
        </div>

        {/* <div>
                    <TaskProgression status={support?.status} />
                </div> */}
        {cancelTask && (
          <Formik
            initialValues={{ description: "" }}
            validationSchema={validationSchema}
            onSubmit={(values) => handleTaskAction("cancel", values)}
          >
            <Form className="space-y-4">
              <TextAreaField
                label="Reason of Canceling Task"
                name="description"
                required
                placeholder="Enter reason for cancelling task"
              />
              <div className="flex justify-end space-x-3">
                <button
                  className="flex items-center gap-2 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition duration-300"
                  onClick={() => setCancelTask(false)}
                  type="button"
                >
                  <Icon
                    icon="mdi:close-circle-outline"
                    className="text-gray-700 text-lg"
                  />
                  <span>Cancel</span>
                </button>
                <button
                  className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition duration-300"
                  type="submit"
                >
                  <Icon
                    icon="mdi:send-circle-outline"
                    className="text-white text-lg"
                  />
                  <span>Submit</span>
                </button>
              </div>
            </Form>
          </Formik>
        )}

        <div className="flex justify-end space-x-4 mt-6">
          {(actionButtons[support?.status] || []).map(
            ({ action, label, color }) => (
              <button
                key={label}
                className={`${color} font-medium px-6 py-2 rounded-lg hover:opacity-75 transition duration-300`}
                onClick={() =>
                  typeof action === "function"
                    ? action()
                    : handleTaskAction(action)
                }
              >
                <Icon
                  icon={`mdi:${label.toLowerCase().replace(/\s/g, "-")}`}
                  className="inline-block mr-2"
                />
                {label}
              </button>
            )
          )}
          <button
            className="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-400 transition duration-300"
            onClick={() => window.history.back()}
          >
            <Icon icon="mdi:close" className="inline-block mr-2" />
            Back
          </button>
        </div>
      </div>
      <div className="col-span-7 p-8 bg-white rounded-lg shadow-lg space-y-6 self-start">
        <h2 className="text-2xl font-bold text-gray-800 border-b pb-2">
          Expenses
        </h2>
        <FinancialSummary
          supportAccounts={support?.support_accounts}
          supportExpenses={support?.support_expenses}
        />
        <ExpenseHead supportExpenses={support?.support_expenses} support={support} />
      </div>
    </div>
  );
};

export default SupportDetails;
