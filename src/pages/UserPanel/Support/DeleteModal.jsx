import Modal from '@/components/ui/Modal';
import { useDeleteApiMutation } from '@/store/api/apihandler/commonSlice';
import { toast } from 'react-toastify';
import React from 'react';
import Button from '@/components/ui/Button';
import { Icon } from "@iconify/react";

const DeleteModal = ({ isModalOpen, setDeleteModal, expenseId }) => {
    const [deleteApi] = useDeleteApiMutation();

    const onSubmit = async () => {
        try {
            await deleteApi({
                end_point: "user/support-expenses/" + expenseId,
                body: {},
            }).unwrap();
            toast.success("Expense deleted successfully!");
            setDeleteModal(false);
        } catch (err) {
            console.error("Error deleting expense:", err);
            toast.error("Failed to delete expense. Please try again.");
        }
    };

    return (
        <Modal
            activeModal={isModalOpen}
            title="Delete Expense"
            className="max-w-2xl bg-gray-50 rounded-lg shadow-lg"
            onClose={() => setDeleteModal(false)}
        >
            <div className="p-6 text-center">
                <div className="flex justify-center mb-4">
                    <Icon icon="mdi:alert-circle-outline" className="text-red-600 text-4xl" />
                </div>
                <p className="text-xl font-semibold text-gray-800 mb-2">
                    Confirm Deletion
                </p>
                <p className="text-sm text-gray-600 mb-6">
                    Are you sure you want to delete this expense? This action cannot be undone.
                </p>
                <div className="flex justify-center gap-4 mt-6">
                    <Button
                        onClick={onSubmit}
                        className="flex items-center gap-2 bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600 transition-colors duration-200"
                    >
                        <Icon icon="mdi:check-circle-outline" className="text-white" />
                        Yes, Delete
                    </Button>
                    <Button
                        onClick={() => setDeleteModal(false)}
                        className="flex items-center gap-2 bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors duration-200"
                    >
                        <Icon icon="mdi:close-circle-outline" className="text-gray-600" />
                        No, Cancel
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default DeleteModal;
