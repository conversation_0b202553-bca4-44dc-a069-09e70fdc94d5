import Modal from "@/components/ui/Modal";
import { useGetApiWithIdQuery, usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { toast } from "react-toastify";
import { Form, Formik } from "formik";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import * as Yup from "yup";

const DetailSection = ({ icon, iconColor, title, children }) => (
    <div className="bg-gray-50 p-4 rounded-lg shadow-sm">
        <div className="flex items-center">
            <Icon icon={icon} className={`${iconColor} text-xl mr-3`} />
            <div>
                <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
                <div className="text-gray-700">{children}</div>
            </div>
        </div>
    </div>
);

const AcceptTask = ({ activeModal, onClose, task }) => {
    const { data: support } = useGetApiWithIdQuery(["support-details", task]);
    const [postApi] = usePostApiMutation();
    const [cancelTask, setCancelTask] = useState(false);

    const validationSchema = (cancelTask) =>
        Yup.object().shape({
            description: cancelTask
                ? Yup.string().required("Reason for cancellation is required")
                : Yup.string(),
        });

    const handleTaskAction = async (action) => {
        const endpoints = {
            accept: `task-accept/${task}`,
            complete: `task-complete/${task}`,
            forward: `task-reject/${task}`,
        };

        try {
            await postApi({ end_point: endpoints[action], body: task }).unwrap();
            onClose();
            toast.success(
                `Task successfully ${action === "accept"
                    ? "accepted"
                    : action === "complete"
                        ? "marked as completed"
                        : "forwarded"
                }`
            );
        } catch (err) {
            console.error(`Failed to ${action} task:`, err);
        }
    };



    const handleCancelTask = async (values) => {
        try {
            await postApi({ end_point: `task-cancel/${task}`, body: { id: task, ...values } }).unwrap();
            setCancelTask(false);
            onClose();
            toast.success("Task successfully cancelled");
        } catch (err) {
            console.error("Failed to cancel task:", err);
        }
    };

    return (
        <Modal
            activeModal={activeModal}
            onClose={onClose}
            title="Task Overview"
            className="max-w-4xl bg-white rounded-lg shadow-lg"
        >
            <div className="p-8 space-y-6">
                <h2 className="text-2xl font-bold text-gray-800 border-b pb-2">Task Details</h2>

                <DetailSection icon="mdi:cog-outline" iconColor="text-blue-500" title="Support Type">
                    <p>{support?.support_type?.name}</p>
                    <p className="text-gray-500 text-sm mt-1">{support?.support_type?.description}</p>
                </DetailSection>

                <DetailSection icon="mdi:file-document-outline" iconColor="text-green-500" title="Client Invoice">
                    <p>Invoice No: {support?.sale?.invoice_no}</p>
                    <p>Grand Total: {support?.sale?.grand_total} / Paid: {support?.sale?.paid_amount} / Due: {support?.sale?.due_amount}</p>
                    <p>Payment Status: {support?.sale?.payment_status}</p>
                </DetailSection>

                <DetailSection icon="mdi:account-outline" iconColor="text-purple-500" title="Assigned Employee(s)">
                    <p>{support?.employees?.map((employee) => employee.name).join(", ")}</p>
                </DetailSection>

                <DetailSection
                    icon="mdi:clipboard-check-outline"
                    iconColor={
                        support?.status === "pending" ? "text-yellow-500" :
                            support?.status === "on_going" ? "text-blue-500" :
                                support?.status === "completed" ? "text-green-500" :
                                    support?.status === "cancelled" ? "text-red-500" :
                                        support?.status === "forwarded" ? "text-purple-500" :
                                            "text-gray-500"
                    }
                    title="Task Status"
                >
                    <p className={
                        support?.status === "pending" ? "text-yellow-600" :
                            support?.status === "on_going" ? "text-blue-600" :
                                support?.status === "completed" ? "text-green-600" :
                                    support?.status === "cancelled" ? "text-red-600" :
                                        support?.status === "forwarded" ? "text-purple-600" :
                                            "text-gray-600"
                    }>
                        {support?.status === "pending" ? "Pending" :
                            support?.status === "on_going" ? "On Going" :
                                support?.status === "completed" ? "Completed" :
                                    support?.status === "cancelled" ? "Cancelled" :
                                        support?.status === "forwarded" ? "Forwarded" :
                                            "No status Found"}
                    </p>
                </DetailSection>

                <DetailSection icon="mdi:phone-outline" iconColor="text-red-500" title="Contact Info">
                    <p>{support?.contact_info}</p>
                </DetailSection>

                <DetailSection icon="mdi:map-marker-outline" iconColor="text-blue-500" title="Address">
                    <p>{support?.address}</p>
                </DetailSection>

                {cancelTask && (
                    <Formik initialValues={{ description: "" }} validationSchema={validationSchema(cancelTask)} onSubmit={handleCancelTask}>
                        {({ errors, touched }) => (
                            <Form>
                                <div className="flex flex-col space-y-4">
                                    <TextAreaField
                                        label="Reason of Canceling Task"
                                        name="description"
                                        type="text"
                                        required
                                        placeholder="Enter reason of canceling task"
                                    />

                                    <div className="flex justify-end space-x-3">
                                        <button
                                            className="flex items-center gap-2 bg-gray-300 text-gray-700 font-medium px-4 py-2 rounded-lg hover:bg-gray-400 transition duration-300"
                                            onClick={() => setCancelTask(false)}
                                            type="button"
                                        >
                                            <Icon icon="mdi:close-circle-outline" className="text-gray-700 text-lg" />
                                            Cancel
                                        </button>
                                        <button
                                            className="flex items-center gap-2 bg-blue-500 text-white font-medium px-4 py-2 rounded-lg hover:bg-blue-600 transition duration-300"
                                            type="submit"
                                        >
                                            <Icon icon="mdi:send-circle-outline" className="text-white text-lg" />
                                            Submit
                                        </button>
                                    </div>
                                </div>
                            </Form>
                        )}
                    </Formik>
                )}

                {/* Action Buttons */}
                <div className="flex justify-end space-x-4 mt-6">
                    {support?.status === "pending" && (
                        <>
                            <button className="bg-red-300 text-gray-600 font-medium px-6 py-2 rounded-lg hover:bg-red-400 transition duration-300"
                                onClick={() => handleTaskAction("forward")} type="button">
                                <Icon icon="mdi:arrow-right" className="inline-block mr-2" />
                                Forward Task
                            </button>
                            <button
                                className="bg-blue-600 text-white font-medium px-6 py-2 rounded-lg hover:bg-blue-500 transition duration-300"
                                onClick={() => handleTaskAction("accept")}
                            >
                                <Icon icon="mdi:check" className="inline-block mr-2" />
                                Accept Task
                            </button>
                        </>
                    )}
                    {support?.status === "on_going" && (
                        <>
                            <button className="bg-red-500 text-white font-medium px-6 py-2 rounded-lg hover:bg-red-600 transition duration-300" onClick={() => setCancelTask(true)}>
                                <Icon icon="mdi:close" className="inline-block mr-2" />
                                Cancel
                            </button>
                            <button
                                className="bg-green-600 text-white font-medium px-6 py-2 rounded-lg hover:bg-green-500 transition duration-300"
                                onClick={() => handleTaskAction("complete")}
                            >
                                <Icon icon="mdi:check-all" className="inline-block mr-2" />
                                Mark as Completed
                            </button>
                        </>
                    )}
                    {support?.status === "completed" && (
                        <button
                            className="bg-green-600 text-white font-semibold px-6 py-2 rounded-lg hover:bg-green-700 transition duration-300 flex items-center gap-2"
                            onClick={onClose}
                        >
                            <Icon icon="mdi:trophy-outline" className="text-yellow-300 text-lg" />
                            Task Completed
                        </button>

                    )}
                    <button className="bg-gray-500 text-white font-medium px-6 py-2 rounded-lg hover:bg-gray-400 transition duration-300" onClick={onClose}>
                        <Icon icon="mdi:close" className="inline-block mr-2" />
                        Close
                    </button>
                </div>
            </div>
        </Modal>
    );
};

export default AcceptTask;
