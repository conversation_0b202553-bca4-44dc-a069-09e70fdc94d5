import React from 'react';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import { Icon } from '@iconify/react';
import ImageToPdfUploader from './ImageToPdf';

const CreatePdfModal = ({ isModalOpen, setPdfModal }) => {
    return (
        <Modal
            activeModal={isModalOpen}
            title="Create PDF"
            className="max-w-4xl"
            onClose={() => setPdfModal(false)}
        >
            <div className="p-4">
                <h2 className="text-2xl font-semibold mb-4">Upload Images to Convert to PDF</h2>
                <ImageToPdfUploader onClose={() => setPdfModal(false)} />
            </div>
        </Modal>
    );
};

export default CreatePdfModal;
