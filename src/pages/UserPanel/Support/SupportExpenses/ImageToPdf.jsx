import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import { jsPDF } from 'jspdf';
import Button from '@/components/ui/Button';

const ImageToPdfUploader = ({ onClose }) => {
    const [uploadedImages, setUploadedImages] = useState([]);

    const handleFileChange = (event) => {
        const files = Array.from(event.target.files);
        const imagePreviews = files.map((file) => URL.createObjectURL(file));
        setUploadedImages((prevImages) => [...prevImages, ...imagePreviews]);
    };

    const downloadAsPDF = () => {
        const pdf = new jsPDF("p", "mm", "a4");
        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();
        const quality = 0.7;

        uploadedImages.forEach((image, index) => {
            const img = new Image();
            img.src = image;

            img.onload = () => {
                const imgWidth = img.width;
                const imgHeight = img.height;
                const scaleRatio = Math.min(pageWidth / imgWidth, pageHeight / imgHeight) * 0.9;
                const pdfWidth = imgWidth * scaleRatio;
                const pdfHeight = imgHeight * scaleRatio;
                const x = (pageWidth - pdfWidth) / 2;
                const y = (pageHeight - pdfHeight) / 2;

                pdf.addImage(img, "JPEG", x, y, pdfWidth, pdfHeight, undefined, "FAST", quality);

                if (index < uploadedImages.length - 1) {
                    pdf.addPage();
                }

                if (index === uploadedImages.length - 1) {
                    pdf.save("uploaded_images.pdf");
                    onClose();
                }
            };
        });
    };

    return (
        <div>
            <input
                type="file"
                accept="image/*"
                multiple
                onChange={handleFileChange}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 mb-6"
            />
            <div className="grid grid-cols-3 gap-4 mb-6">
                {uploadedImages.map((image, index) => (
                    <div key={index} className="relative">
                        <img
                            src={image}
                            alt={`Preview ${index + 1}`}
                            className="w-full h-32 object-cover rounded-lg"
                        />
                    </div>
                ))}
            </div>

            <div className="flex gap-4 justify-end">
                <Button
                    btnClass="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition"
                    onClick={downloadAsPDF}
                    disabled={uploadedImages.length === 0}
                >
                    <Icon icon="mdi:content-save" className="text-lg" />
                    <span>Save as PDF</span>
                </Button>
                <Button
                    btnClass="btn-primary"
                    onClick={() => onClose()}
                >
                    <div className='flex items-center gap-2'>
                        <Icon icon="mdi:close" className="text-lg" />
                        <span>Close</span>
                    </div>
                </Button>
            </div>
        </div >
    );
};

export default ImageToPdfUploader;
