import Modal from '@/components/ui/Modal';
import React from 'react';

const ViewAttachmentModal = ({ showAttachment, setShowAttachment, attachment }) => {
    const isImage = /\.(jpg|jpeg|png|gif)$/i.test(attachment);
    const isPDF = /\.pdf$/i.test(attachment);
    const apiUrl = import.meta.env.VITE_MEDIA_URL;

    return (
        <Modal
            activeModal={showAttachment}
            onClose={() => setShowAttachment(false)}
            title="View Attachment"
            className="max-w-5xl"
            footerContent={
                <div className="flex justify-end gap-3">
                    <button
                        onClick={() => setShowAttachment(false)}
                        className="btn bg-blue-600 text-white hover:bg-blue-700"
                    >
                        Close
                    </button>
                </div>
            }
        >
            <div className="flex justify-center items-center">
                {isImage ? (
                    <img src={`${apiUrl}/${attachment}`} alt="Attachment" className="max-w-full max-h-96" />
                ) : isPDF ? (
                    <iframe
                        src={`${apiUrl}/${attachment}`}
                        title="PDF Preview"
                        className="w-full h-96"
                        frameBorder="0"
                    />
                ) : (
                    <p className="text-gray-500">Unsupported file type</p>
                )}
            </div>
        </Modal>
    );
};

export default ViewAttachmentModal;
