import React, { useState } from "react";
import { Formik, Form, FieldArray } from "formik";
import * as Yup from "yup";
import InputSelect from "@/components/ui/form/InputSelect";
import InputField from "@/components/ui/form/InputField";
import { useNavigate, useParams } from "react-router-dom";
import InputFile from "@/components/ui/form/InputFile";
import TextAreaField from "@/components/ui/form/TextAreaField";
import { Icon } from "@iconify/react";
import CreatePdfModal from "./CreatePdfModal";
import FileUpload from "@/components/ui/form/FileUpload";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { useGetApiQuery, usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { toast } from "react-toastify";

const SummaryRow = ({ label, value, icon }) => (
    <div className="flex items-center justify-between p-2 rounded-md bg-white shadow-sm hover:shadow-md transition-shadow">
        <div className="flex items-center space-x-2">
            <Icon icon={icon} className="text-blue-500" width="20" height="20" />
            <span className="text-gray-700 font-medium">{label}:</span>
        </div>
        <span className="text-gray-900">{value || "Not provided"}</span>
    </div>
);

const validationSchema = Yup.object({
    support_id: Yup.number().required("Support ID is required"),
    advance_amount: Yup.number().required("Advance amount is required"),
    expense_amount: Yup.number().required("Expense amount is required"),
    head_details: Yup.array().of(
        Yup.object({
            expense_head_id: Yup.number().required("Expense type is required"),
            amount: Yup.number().required("Amount is required"),
        })
    ),
});

const CreateExpense = () => {
    const { id } = useParams();
    const [pdfModal, setPdfModal] = useState(false);
    const [postApi] = usePostApiMutation();
    const navigate = useNavigate();

    const { data: expenseHeads } = useGetApiQuery({ url: "expense-heads", params: { is_active: 1, pagination: 0 } });
    const expenseHeadOptions = expenseHeads?.map((head) => ({
        label: head.name,
        value: head.id,
    }));

    const handleSubmit = async (values, { resetForm }) => {
        try {
            const response = await postApi({
                end_point: "support-expenses",
                body: values,
            }).unwrap();
            toast.success("Expense submitted successfully!");
            resetForm();
            navigate(`/support-expenses/${id}`);
        } catch (error) {
            console.error("Error submitting support expenses:", error);
        }
    };
    return (
        <Formik
            initialValues={{
                support_id: id,
                advance_amount: "",
                expense_amount: "",
                description: "",
                attachment: null,
                head_details: [],
            }}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
        >
            {({ values, isSubmitting }) => (
                <div className="">
                    <Form className="space-y-4 grid grid-cols-5 gap-8 p-6 bg-gray-50">
                        <>
                            <Card title={"Expense Details"} className="col-span-3">
                                {console.log(values)}
                                <div className="flex space-x-4 mb-5">
                                    <div className="w-1/2">
                                        <InputField
                                            name="advance_amount"
                                            placeholder="Enter Advance Amount"
                                            type="number"
                                            label="Advance Amount"
                                            required
                                        />
                                    </div>
                                    <div className="w-1/2">
                                        <InputField
                                            name="expense_amount"
                                            placeholder="Enter Expense Amount"
                                            type="number"
                                            label="Expense Amount"
                                            required
                                        />
                                    </div>
                                </div>

                                <div className="flex space-x-4 mb-5">
                                    <div className="w-1/2">
                                        <FileUpload
                                            title="Upload your attachment"
                                            name="attachment"
                                            label="Upload Attachment"
                                            endpoint="file-upload"
                                            valueKey="file"
                                            accept="image/*,application/pdf"
                                        />
                                    </div>
                                    <div className="w-1/2">
                                        <label className="block text-gray-700 font-semibold mb-2">Make PDF (If you have multiple files)</label>
                                        <button
                                            onClick={() => setPdfModal(true)}
                                            type="button"
                                            className="w-full flex items-center justify-center bg-gray-900 text-white font-semibold py-2 rounded-lg shadow-md hover:bg-gray-800 transition-colors duration-200"
                                        >
                                            <Icon icon="teenyicons:pdf-solid" className="mr-2 text-lg" />
                                            Make PDF
                                        </button>
                                    </div>
                                </div>
                                <TextAreaField
                                    label="Description"
                                    name="description"
                                    type="text"
                                    placeholder="Enter Description"
                                />

                                <FieldArray name="head_details">
                                    {({ push, remove }) => (
                                        <div>
                                            {values?.head_details.length > 0 && <label className="block font-semibold text-gray-700 mb-4 text-lg">Extra Expenses</label>}
                                            
                                            {values.head_details.length === 0 && (
                                                <div className="flex items-center justify-end space-x-4">
                                                    <div className="flex items-center text-gray-600 space-x-2">
                                                        <Icon icon="mdi:information-outline" className="text-blue-500" width="20" height="20" />
                                                        <span>Overspend? No worries! Click "Add Expense" to track it here.</span>
                                                    </div>
                                                    <Button
                                                        type="button"
                                                        onClick={() => push({ expense_head_id: "", amount: "" })}
                                                        className="bg-blue-500 text-white py-2 rounded-lg font-semibold hover:bg-blue-600 transition"
                                                    >
                                                        Add Expense
                                                    </Button>
                                                </div>
                                            )}

                                            {values.head_details.map((detail, index) => (
                                                <div
                                                    key={index}
                                                    className="flex items-center mb-4 p-4 bg-gray-50 rounded-lg shadow-sm hover:shadow-md transition-shadow"
                                                >
                                                    <div className="flex-grow flex space-x-4 items-center">
                                                        <div className="w-1/2">
                                                            <InputSelect
                                                                label="Expense Type"
                                                                name={`head_details[${index}].expense_head_id`}
                                                                options={expenseHeadOptions}
                                                                placeholder="Select Expense Type"
                                                                required
                                                            />
                                                        </div>
                                                        <div className="w-1/2">
                                                            <InputField
                                                                name={`head_details[${index}].amount`}
                                                                type="number"
                                                                placeholder="Enter Amount"
                                                                label="Amount"
                                                                required
                                                            />
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center space-x-2 ml-4 mt-7">
                                                        <button
                                                            type="button"
                                                            onClick={() => push({ expense_head_id: "", amount: "" })}
                                                            className="flex items-center justify-center w-8 h-8 bg-green-500 text-white rounded-full shadow-md hover:bg-green-600 hover:shadow-lg transition-all transform hover:scale-105"
                                                            aria-label="Add Expense"
                                                        >
                                                            +
                                                        </button>
                                                        <button
                                                            type="button"
                                                            onClick={() => remove(index)}
                                                            className="flex items-center justify-center w-8 h-8 bg-red-500 text-white rounded-full shadow-md hover:bg-red-600 hover:shadow-lg transition-all transform hover:scale-105"
                                                            aria-label="Remove Expense"
                                                        >
                                                            ×
                                                        </button>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </FieldArray>

                            </Card>
                            <Card title="Expense Summary" className="col-span-2 self-start">
                                <div className="p-4 space-y-4 bg-gray-100 rounded-lg">
                                    <h3 className="text-lg font-semibold text-gray-700">Summary of Inputs</h3>

                                    <SummaryRow
                                        label="Advance Amount"
                                        value={values.advance_amount}
                                        icon="mdi:cash"
                                    />
                                    <SummaryRow
                                        label="Expense Amount"
                                        value={values.expense_amount}
                                        icon="mdi:cash-minus"
                                    />
                                    <SummaryRow
                                        label="Description"
                                        value={values.description}
                                        icon="mdi:note-text"
                                    />
                                    <SummaryRow
                                        label="Attachment"
                                        value={values.attachment ? "Uploaded" : "Not uploaded"}
                                        icon="mdi:file-document"
                                    />

                                    {values.head_details.length > 0 ? (
                                        values.head_details.map((detail, index) => (
                                            <SummaryRow
                                                key={index}
                                                label={`Extra Expense ${index + 1}`}
                                                value={detail.amount}
                                                icon="mdi:cash-plus"
                                            />
                                        ))
                                    ) : (
                                        <p className="text-gray-600">No extra expenses added.</p>
                                    )}
                                </div>
                                <button
                                    type="submit"
                                    className={`w-full py-2 mt-4 rounded-lg text-white transition ${isSubmitting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'}`}
                                    disabled={isSubmitting}
                                >
                                    {isSubmitting ? 'Submitting...' : 'Submit'}
                                </button>
                            </Card>

                        </>

                        {pdfModal && (
                            <CreatePdfModal
                                isModalOpen={pdfModal}
                                setPdfModal={setPdfModal}
                            />
                        )}
                    </Form>
                </div>
            )}
        </Formik>
    );
};

export default CreateExpense;
