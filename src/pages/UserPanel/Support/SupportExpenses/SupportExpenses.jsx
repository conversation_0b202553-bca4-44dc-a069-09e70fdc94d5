import React, { memo, useState } from 'react';
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import { useNavigate, useParams } from "react-router-dom";
import Icon from "@/components/ui/Icon";
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import ViewAttachmentModal from './ViewAttachmentModal';

const InfoRow = memo(({ icon, iconClass, label }) => (
    <div className="flex items-center space-x-2">
        <Icon icon={icon} className={iconClass} width="24" height="24" />
        <p className="text-gray-600">{label}</p>
    </div>
));

const ExpenseCard = memo(({ expense, setShowAttachment, setAttachment }) => {
    const {
        support: { name },
        advance_amount,
        expense_amount,
        refund_amount = '0.00',
        extra_expense_amount,
        attachment,
        status,
        created_at
    } = expense;

    const handleAttachmentClick = () => {
        setAttachment(attachment);
        setShowAttachment(true);  
    };

    return (
        <div className="p-4 bg-gray-100 shadow-md rounded-lg flex flex-col space-y-2">
            <InfoRow icon="carbon:task" iconClass="text-blue-500 font-semibold text-lg" label={name} />
            <InfoRow icon="mdi:cash" iconClass="text-green-500" label={`Advance: ৳${advance_amount || '0.00'}`} />
            <InfoRow icon="mdi:cash-minus" iconClass="text-red-500" label={`Expense: ৳${expense_amount || '0.00'}`} />
            <InfoRow icon="mdi:cash-refund" iconClass="text-orange-500" label={`Refund: ৳${refund_amount || '0.00'}`} />
            <InfoRow icon="mdi:cash-plus" iconClass="text-purple-500" label={`Extra: ৳${extra_expense_amount || '0.00'}`} />
            <div onClick={handleAttachmentClick} className="flex items-center space-x-2 cursor-pointer">
                <Icon icon="mdi:file-document" className="text-gray-500" width="24" height="24" />
                <span className="text-blue-500">Attachment</span>
            </div>
            <InfoRow icon="mdi:information" iconClass="text-teal-500" label={`Status: ${status}`} />
            <p className="text-gray-500 text-sm">Created: {new Date(created_at).toLocaleDateString()}</p>
        </div>
    );
});

const SupportIndexes = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { data, isLoading, error } = useGetApiQuery({
        url: `support-expenses?support_id=${id}`
    });

    const [showAttachment, setShowAttachment] = useState(false);
    const [attachment, setAttachment] = useState(null);

    if (isLoading) return <div className="p-6 text-center">Loading expenses...</div>;
    if (error) return <div className="p-6 text-center text-red-500">Error loading expenses. Please try again.</div>;

    return (
        <Card title="Support Expenses"
            headerslot={
                <Button onClick={() => navigate(`/create-expenses/${id}`)}>
                    <div className='flex items-center gap-1'>
                        <Icon icon="mdi:plus-circle" className="mr-2" width="20" height="20" />
                        Add Expense
                    </div>
                </Button>
            }>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-6">
                {data?.data?.length ? (
                    data.data.map(expense => (
                        <ExpenseCard
                            key={expense.id}
                            expense={expense}
                            setShowAttachment={setShowAttachment}
                            setAttachment={setAttachment}
                        />
                    ))
                ) : (
                    <div className="col-span-full text-center text-gray-500">No expenses available.</div>
                )}
            </div>
            {showAttachment && (
                <ViewAttachmentModal
                    showAttachment={showAttachment}
                    setShowAttachment={setShowAttachment}
                    attachment={attachment}
                />
            )}
        </Card>
    );
};

export default SupportIndexes;
