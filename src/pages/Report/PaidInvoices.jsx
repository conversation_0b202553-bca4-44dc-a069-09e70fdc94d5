import React, { useState } from "react";
import Card from "@/components/ui/Card";
import Badge from "@/components/ui/Badge";
import Dropdown from "@/components/ui/Dropdown";
import Icon from "@/components/ui/Icon";
import { Menu } from "@headlessui/react";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import { useNavigate } from "react-router-dom";
import { formattedDate } from "@/constant/data";
import TableSkeleton from "@/components/ui/TableSkeleton";
import Pagination from "@/components/partials/common-table/pagination";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { format } from "date-fns";

const PaidInvoices = () => {
    const [filter, setFilter] = useState("");
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);
    const [apiParam, setApiParam] = useState(0);

    // Build query parameters dynamically
    const queryParams = { page: apiParam };
    if (filter) {
        queryParams.search = filter;
    }
    if (startDate && endDate) {
        queryParams.start_date = format(startDate, "dd-MM-yyyy");
        queryParams.end_date = format(endDate, "dd-MM-yyyy");
    }

    const { data, isLoading, isFetching } = useGetApiQuery({
        url: "paid-invoice",
        params: queryParams,
    });

    const navigate = useNavigate();

    const changePage = (value) => {
        setApiParam(value);
    };

    const columns = [
        { label: "Invoice No", field: "invoice_no" },
        { label: "Client ID", field: "client_id" },
        { label: "Grand Total", field: "grand_total" },
        { label: "Paid Amount", field: "paid_amount" },
        { label: "Payment Status", field: "payment_status" },
        { label: "Invoice Date", field: "invoice_date" },
        { label: "Action", field: "" },
    ];

    const actions = [
        {
            name: "View Details",
            icon: "heroicons-outline:eye",
            onClick: (val) => {
                navigate(`/invoice-details/${data?.data[val]?.id}`);
            },
        },
    ];

    const tableData = data?.data?.map((item, index) => ({
        invoice_no: item.invoice_no,
        client_id: item.client_id,
        grand_total: `৳ ${parseFloat(item.grand_total).toFixed(2)}`,
        paid_amount: `৳ ${parseFloat(item.paid_amount).toFixed(2)}`,
        payment_status: (
            <Badge
                className={
                    item.payment_status === "Paid"
                        ? `bg-success-500 text-white`
                        : `bg-warning-500 text-white`
                }
            >
                {item.payment_status}
            </Badge>
        ),
        invoice_date: formattedDate(item.invoice_date),
    }));

    return (
        <div>
            <Card noborder>
                <div className="md:flex justify-between items-center mb-6">
                    <h4 className="card-title mb-5 lg:mb-0">Paid Invoices</h4>
                    <div className="flex flex-wrap gap-2 md:flex-nowrap">
                        {/* Date Range Pickers */}
                        <div className="w-full md:w-auto relative">
                            <DatePicker
                                selected={startDate}
                                onChange={(date) => setStartDate(date)}
                                dateFormat="dd-MM-yyyy"
                                placeholderText="Start Date"
                                className="form-control py-2 pl-10"
                            />
                            <Icon
                                icon="heroicons-outline:calendar"
                                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-500 text-lg"
                            />
                        </div>
                        <div className="w-full md:w-auto relative">
                            <DatePicker
                                selected={endDate}
                                onChange={(date) => setEndDate(date)}
                                dateFormat="dd-MM-yyyy"
                                placeholderText="End Date"
                                className="form-control py-2 pl-10"
                            />
                            <Icon
                                icon="heroicons-outline:calendar"
                                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-500 text-lg"
                            />
                        </div>
                        {/* Search Input */}
                        <div className="w-full md:w-auto">
                            <div className="form-group relative">
                                <input
                                    type="text"
                                    value={filter}
                                    onChange={(e) => setFilter(e.target.value)}
                                    className="form-control py-2"
                                    placeholder="Search..."
                                />
                                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-500">
                                    <Icon icon="heroicons-outline:search" />
                                </span>
                            </div>
                        </div>
                        {/* Create New Invoice Button */}
                        <button
                            className="btn btn-primary btn-sm w-full md:w-auto"
                            onClick={() => navigate("/create-invoice")}
                        >
                            Create New Invoice
                        </button>
                    </div>
                </div>
                {isLoading || isFetching ? (
                    <TableSkeleton columns={columns} actions={actions} />
                ) : tableData?.length > 0 ? (
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                            <thead className="bg-slate-200 dark:bg-slate-700">
                                <tr>
                                    {columns.map((column, i) => (
                                        <th key={i} className="table-th">
                                            {column.label}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-slate-100 dark:divide-slate-800">
                                {tableData?.map((row, dataIndex) => (
                                    <tr
                                        key={dataIndex}
                                        className="hover:bg-slate-200 dark:hover:bg-slate-700"
                                    >
                                        {columns.map(
                                            (column, index) =>
                                                column.field && (
                                                    <td key={index} className="table-td">
                                                        {row[column.field]}
                                                    </td>
                                                )
                                        )}
                                        <td className="table-td">
                                            <Dropdown
                                                classMenuItems="right-0 w-[140px] top-[110%]"
                                                label={
                                                    <span className="text-xl text-center block w-full">
                                                        <Icon icon="heroicons-outline:dots-vertical" />
                                                    </span>
                                                }
                                            >
                                                <div className="divide-y divide-slate-100 dark:divide-slate-800">
                                                    {actions.map((item, i) => (
                                                        <Menu.Item key={i}>
                                                            <div
                                                                className="w-full px-4 py-2 text-sm cursor-pointer flex items-center space-x-2 rtl:space-x-reverse"
                                                                onClick={() => item.onClick(dataIndex)}
                                                            >
                                                                <span className="text-base">
                                                                    <Icon icon={item.icon} />
                                                                </span>
                                                                <span>{item.name}</span>
                                                            </div>
                                                        </Menu.Item>
                                                    ))}
                                                </div>
                                            </Dropdown>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                ) : (
                    "No data found"
                )}
                {data?.total > 0 && (
                    <Pagination
                        totalPages={Math.ceil(data?.total / data?.per_page)}
                        currentPage={data?.current_page}
                        handlePageChange={changePage}
                    />
                )}
            </Card>
        </div>
    );
};

export default PaidInvoices;
