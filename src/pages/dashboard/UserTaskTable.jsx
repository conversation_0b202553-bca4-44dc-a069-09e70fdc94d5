import React from "react";
import { useNavigate } from "react-router-dom";
import NoImage from "@/assets/CRM/NoImage.png";
import BrokenImage from "@/assets/CRM/BrokenImage.svg";
import Tooltip from "@/components/ui/Tooltip";
import Button from "@/components/ui/Button";
import { formattedDate } from "@/constant/data";
import { Icon } from "@iconify/react";
import Card from "@/components/ui/Card";

const UserTaskTable = ({ tableData }) => {
  const navigate = useNavigate();

  const handleShowDetails = (taskId) => {
    navigate(`/task-details/${taskId}`);
  };

  return (
    <div className="text-black rounded-lg shadow-sm bg-white dark:bg-gray-800 mx-auto mt-6">
      <h2 className="text-2xl font-bold text-left py-4 px-6 text-gray-900 dark:text-gray-100">
        Task Overview
      </h2>
      <Card noBorder>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-100 dark:bg-gray-700">
              <tr>
                {["Image", "Name", "Status", "Assign Date", "Deadline", "Client", "Details"].map(
                  (heading) => (
                    <th
                      key={heading}
                      className="px-4 py-3 text-sm font-semibold text-gray-600 dark:text-gray-300 text-center"
                    >
                      {heading}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {tableData?.length > 0 ? (
                tableData.map((item) => (
                  <tr
                    key={item.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
                  >
                    <td className="px-4 py-2 text-center">
                      <div className="rounded-lg w-16 h-16 flex items-center justify-center overflow-hidden bg-gray-100 dark:bg-gray-700">
                        <img
                          src={NoImage}
                          alt="No Image Available"
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = BrokenImage;
                          }}
                        />
                      </div>
                    </td>
                    <td className="px-4 py-2 text-center">
                      <Tooltip
                        content={
                          <>
                            Task Details:{" "}
                            <span className="font-bold">{item.name}</span>
                          </>
                        }
                        placement="top"
                        arrow
                        animation="Interactive"
                      >
                        <span className="text-lg text-gray-800 dark:text-gray-200 hover:underline hover:text-blue-500 cursor-pointer">
                          {item.name.length > 30
                            ? `${item.name.substring(0, 30)}...`
                            : item.name}
                        </span>
                      </Tooltip>
                    </td>
                    <td className="px-4 py-2 text-center">
                      <p
                        className={`text-sm font-medium ${item.status === "completed"
                            ? "text-green-500"
                            : item.status === "pending"
                              ? "text-yellow-500"
                              : item.status === "on_going"
                                ? "text-blue-500"
                                : item.status === "cancelled"
                                  ? "text-red-500"
                                  : "text-gray-500"
                          }`}
                      >
                        {item.status === "on_going"
                          ? "On Going"
                          : item.status === "cancelled"
                            ? "Cancelled"
                            : item.status.charAt(0).toUpperCase() +
                            item.status.slice(1)}
                      </p>
                    </td>
                    <td className="px-4 py-2 text-center">
                      {formattedDate(item.assign_date)}
                    </td>
                    <td className="px-4 py-2 text-center">
                      {item.deadline ? formattedDate(item.deadline) : "N/A"}
                    </td>
                    <td className="px-4 py-2 text-center">
                      {item.sale?.client?.name || "Unknown"}
                    </td>
                    <td className="px-4 py-2 text-center">
                      <Button
                        onClick={() => handleShowDetails(item.id)}
                        className="flex items-center gap-2 bg-blue-500 text-white rounded-lg py-2 px-4 hover:bg-blue-600"
                      >
                        <Icon icon="mdi:information" className="text-white" />
                        Details
                      </Button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan="7"
                    className="px-4 py-4 text-center text-gray-500 dark:text-gray-400"
                  >
                    No tasks available
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default UserTaskTable;
