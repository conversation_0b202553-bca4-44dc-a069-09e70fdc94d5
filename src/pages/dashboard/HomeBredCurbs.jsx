import React, { useState } from "react";
import Icon from "@/components/ui/Icon";
import dayjs from "dayjs";
import Datepicker from "react-tailwindcss-datepicker";
import Button from "@/components/ui/Button";
import { useNavigate } from "react-router-dom";

const HomeBredCurbs = ({ title }) => {
  const [value, setValue] = useState({
    startDate: new Date(),
    endDate: new Date().setMonth(11),
  });

  const navigate = useNavigate();

  const handleValueChange = (newValue) => {
    setValue(newValue);
  };
  const handleNavigate = () => {
    navigate("/store-sale");
  };

  let user = null;
  try {
    const storedUser = localStorage.getItem("user");
    user = storedUser ? JSON.parse(storedUser) : null;
  } catch (error) {
    console.error("Error parsing user from localStorage", error);
  }

  return (
    <div className="flex justify-between flex-wrap items-center mb-6">
      <h4 className="ms-2 font-medium lg:text-2xl text-xl capitalize inline-block ltr:pr-4 rtl:pl-4  my-3 text-downriver-950">
        {title}
      </h4>
      <div className="flex sm:space-x-4 space-x-2 sm:justify-end items-center rtl:space-x-reverse">
        <div className="date-range-custom relative hidden ">
          <Datepicker
            value={value}
            inputClassName="input-class"
            containerClassName="container-class"
            onChange={handleValueChange}
          />
        </div>
        <div className="date-range-custom2 relative hidden ">
          <Datepicker
            value={value}
            asSingle={true}
            inputClassName="input-class"
            containerClassName="container-class"
            onChange={handleValueChange}
          />
        </div>
        {user?.role !== "user" && (
          <>
            <Button
              icon="iconamoon:invoice-thin"
              text="Generate Invoice"
              className="btn-primary"
              onClick={handleNavigate}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default HomeBredCurbs;
