import React from "react";
import { useNavigate } from "react-router-dom";
import StatCard from "./StatCard";

const informationCard = ({ cardData }) => {
  const navigate = useNavigate();
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      <StatCard
        title="Available Products"
        currentValue={cardData?.total_sale_qty}
        additionalValue={cardData?.total_stock_qty}
        backgroundColor="bg-halfDutchWhite-50"
        iconTextColor="text-chalky-200"
        icon="akar-icons:equal"
        currentValueColor="text-green-700"
        additionalValueColor="text-primary-900"
        currentValueTooltip="Sold"
        additionalValueTooltip="Stock"
        statusLabel="Total Products"
        onClick={() => navigate("/inventory-list")}
      />
      <StatCard
        title="Total Sales"
        currentValue={cardData?.total_sales}
        backgroundColor="bg-selago-50"
        iconTextColor="text-portage-400"
        icon="heroicons:tag"
        currentValueColor="text-downriver-900"
        currentValueTooltip="Sold"
        statusLabel="From Today"
        onClick={() => navigate("/sales-list")}
      />
      <StatCard
        title="Total Expense"
        currentValue={`৳${cardData?.total_expense}`}
        backgroundColor="bg-seashellPeach-50"
        iconTextColor="text-monaLisa-300"
        icon="system-uicons:fullscreen"
        currentValueColor="text-downriver-900"
        currentValueTooltip="Expense"
        statusLabel="From Today"
      />
      <StatCard
        title="Pending Task"
        currentValue={cardData?.total_tasks}
        backgroundColor="bg-frostee-100"
        iconTextColor="text-celadon-300"
        icon="icon-park-outline:loading-one"
        currentValueColor="text-downriver-900"
        currentValueTooltip="Count"
        statusLabel="Total Tasks"
        onClick={() => navigate("/support-list")}
      />
    </div>
  );
};

export default informationCard;
