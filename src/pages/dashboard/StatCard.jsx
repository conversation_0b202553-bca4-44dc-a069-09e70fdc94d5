import React from "react";
import Icon from "@/components/ui/Icon";
import Tooltip from "@/components/ui/Tooltip";

const StatCard = ({
    title,
    currentValue,
    additionalValue,
    backgroundColor,
    icon,
    currentValueColor,
    additionalValueColor,
    currentValueTooltip,
    additionalValueTooltip,
    statusLabel,
    currencySymbol,
    onClick,
    iconTextColor
}) => {
    return (
        <div
            onClick={onClick}
            className="bg-white p-4 flex items-center justify-between border rounded-md border-[#EDF4FF] shadow-lg shadow-[#edf4ff80] drop-shadow cursor-pointer hover:shadow-xl"
        >
            <div
                className={`w-11 h-11 flex items-center justify-center rounded-full mb-4 ${backgroundColor}`}
            >
                <Icon icon={icon} className={`w-6 h-6 font-extrabold ${iconTextColor}`} />
            </div>
            <div className="text-en">
                <h3 className="text-lg font-semibold text-fuscousGray-700 dark:text-slate-200">
                    {title}
                </h3>

                <div className="flex items-end justify-end mx-auto">
                    <Tooltip content={<span className="font-bold">{currentValueTooltip}</span>} placement="top" arrow animation="interactive">
                        <p className={`text-2xl font-bold my-2 p-2 ${currentValueColor}`}>
                            {currencySymbol && title === "Service Expense" ? `${currencySymbol}${currentValue}` : currentValue}
                        </p>
                    </Tooltip>

                    {additionalValue && (
                        <Tooltip content={<span className="font-bold">{additionalValueTooltip}</span>} placement="top" arrow animation="interactive">
                            <p className={`text-2xl font-bold my-2 p-2 ${additionalValueColor}`}>
                                <span className="font-bold text-slate-500 me-2">/</span>
                                {additionalValue}
                            </p>
                        </Tooltip>
                    )}
                </div>

                <div className="text-sm text-fuscousGray-500 mt-1 text-end">{statusLabel}</div>
            </div>
        </div>
    );
};

export default StatCard;
