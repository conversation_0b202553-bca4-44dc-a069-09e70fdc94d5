import React from "react";
import { useNavigate } from "react-router-dom";
import StatCard from "./StatCard";

const UserInformation = ({ cardData }) => {
    const navigate = useNavigate();
    return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <StatCard
                title="Completed Tasks"
                currentValue={cardData?.total_tasks?.completed}
                additionalValue={cardData?.total_tasks?.completed + cardData?.total_tasks?.cancelled + cardData?.total_tasks?.ongoing + cardData?.total_tasks?.pending}
                backgroundColor="bg-halfDutchWhite-50"
                icon="akar-icons:equal"
                iconTextColor="text-chalky-200"
                currentValueColor="text-green-700"
                additionalValueColor="text-primary-900"
                currentValueTooltip="Completed"
                additionalValueTooltip="Total Tasks"
                statusLabel="Total Tasks"
                onClick={() => navigate("/task-list")}
            />
            <StatCard
                title="Ongoing Tasks"
                currentValue={cardData?.total_tasks?.ongoing}
                backgroundColor="bg-selago-50"
                iconTextColor="text-portage-400"
                icon="carbon:continuous-deployment"
                currentValueColor="text-blue-700"
                currentValueTooltip="Ongoing"
                statusLabel="From Today"
                onClick={() => navigate("/task-list")}
            />
            <StatCard
                title="Pending Tasks"
                currentValue={cardData?.total_tasks?.pending}
                backgroundColor="bg-seashellPeach-50"
                iconTextColor="text-orange-700"
                icon="material-symbols:pending-outline"
                currentValueColor="text-warning-700"
                currentValueTooltip="Pending"
                statusLabel="From Today"
                onClick={() => navigate("/task-list")}
            />
            <StatCard
                title="Cancelled Tasks"
                currentValue={cardData?.total_tasks?.cancelled}
                backgroundColor="bg-red-200"
                iconTextColor="text-red-700"
                icon="ix:cancelled"
                currentValueColor="text-red-700"
                currentValueTooltip="Pending"
                statusLabel="From Today"
                onClick={() => navigate("/task-list")}
            />

        </div>
    );
};

export default UserInformation;
