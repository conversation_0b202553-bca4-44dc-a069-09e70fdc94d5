import React, { useEffect, useState } from "react";
import Card from "@/components/ui/Card";
import HomeBredCurbs from "./HomeBredCurbs";
import InformationCard from "./informationCard";
import useAuth from "@/hooks/useAuth";
import Loading from "@/components/Loading";
import StackBarChart from "@/pages/dashboard/stack-bar";
import Calculation from "./Calculation";
import BestSellingTable from "./BestSellingTable";

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isFetching, setIsFetching] = useState(false);
  const user = useAuth();
  const userRole = user?.user?.role;
  console.log(userRole)

  const crm_token = JSON.parse(localStorage.getItem("crm_token")) || null;

  const fetchDashboardData = async () => {
    setIsFetching(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_HOST_URL}dashboard`, {
        headers: {
          Authorization: `Bearer ${crm_token}`,
        },
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(`Failed to fetch data: ${data.message}`);
      }
      setDashboardData(data);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      setIsLoading(false);
      setIsFetching(false);
    }
  };

  useEffect(() => {
    if (userRole) {
      fetchDashboardData();
    }
  }, [userRole]);

  if (isLoading || isFetching) {
    return <Loading />;
  }

  const adminData = dashboardData?.data;

  return (
    <div>
      <HomeBredCurbs title="Dashboard" />
      {dashboardData && (
        <div className="grid grid-cols-1 gap-5 mb-5">
          <InformationCard cardData={adminData} />
        </div>
      )}
      <div className="grid grid-cols-12 gap-5 mb-5">
        <div className="lg:col-span-8 col-span-12 shadow-lg shadow-[#edf4ff80] drop-shadow">
          <Card>
            <header className="md:flex md:space-y-0 space-y-4">
              <h6 className="flex-1 text-downriver-950 text-xl font-semibold dark:text-white capitalize">
                Sales and Stock Distribution Over Time
              </h6>
            </header>
            {adminData && <div className="legend-ring">
              <StackBarChart barChart={adminData?.bar_chart_data} />
            </div>}
          </Card>
        </div>
        <div className="lg:col-span-4 col-span-12 shadow shadow-[#edf4ff80] drop-shadow">
          {adminData && <Card className="text-downriver-950 text-xl font-semibold " title="Overview">
            <Calculation pieChart={adminData?.pie_chart_data} />
          </Card>}
        </div>
      </div>

      <div className="grid grid-cols-12 gap-5">
        <div className="lg:col-span-12 col-span-12">
          <BestSellingTable topSellingProducts={adminData?.top_selling_products} />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
