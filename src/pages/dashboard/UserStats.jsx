import React from 'react';
import StatCard from './StatCard';
import { useNavigate } from 'react-router-dom';
import useAuth from '@/hooks/useAuth';

const UserStats = ({ cardData }) => {
    const navigate = useNavigate();
    const { user } = useAuth();
    return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {user?.role === 'user' ? <>
                <StatCard
                    title="Completed Tasks"
                    currentValue={cardData?.completed}
                    additionalValue={cardData?.completed + cardData?.cancelled + cardData?.ongoing + cardData?.pending}
                    backgroundColor="bg-halfDutchWhite-50"
                    icon="akar-icons:equal"
                    iconTextColor="text-chalky-200"
                    currentValueColor="text-green-700"
                    additionalValueColor="text-primary-900"
                    currentValueTooltip="Completed"
                    additionalValueTooltip="Total Tasks"
                    statusLabel="Total Tasks"
                    onClick={() => navigate("/task-list")}
                />
                <StatCard
                    title="Ongoing Tasks"
                    currentValue={cardData?.ongoing}
                    backgroundColor="bg-selago-50"
                    iconTextColor="text-portage-400"
                    icon="carbon:continuous-deployment"
                    currentValueColor="text-blue-700"
                    currentValueTooltip="Ongoing"
                    statusLabel="From Today"
                    onClick={() => navigate("/task-list")}
                />
                <StatCard
                    title="Pending Tasks"
                    currentValue={cardData?.pending}
                    backgroundColor="bg-seashellPeach-50"
                    iconTextColor="text-orange-700"
                    icon="material-symbols:pending-outline"
                    currentValueColor="text-warning-700"
                    currentValueTooltip="Pending"
                    statusLabel="From Today"
                    onClick={() => navigate("/task-list")}
                />
                <StatCard
                    title="Cancelled Tasks"
                    currentValue={cardData?.cancelled}
                    backgroundColor="bg-red-200"
                    iconTextColor="text-red-700"
                    icon="ix:cancelled"
                    currentValueColor="text-red-700"
                    currentValueTooltip="Pending"
                    statusLabel="From Today"
                    onClick={() => navigate("/task-list")}
                />
                </> : <>
                <StatCard
                    title="Available Products"
                    currentValue={cardData?.total_sale_qty}
                    additionalValue={cardData?.total_stock_qty}
                    backgroundColor="bg-halfDutchWhite-50"
                    iconTextColor="text-chalky-200"
                    icon="akar-icons:equal"
                    currentValueColor="text-green-700"
                    additionalValueColor="text-primary-900"
                    currentValueTooltip="Sold"
                    additionalValueTooltip="Available"
                    statusLabel="Total Products"
                    onClick={() => navigate("/inventory-list")}
                />
                <StatCard
                    title="Total Sales"
                    currentValue={cardData?.total_sales}
                    backgroundColor="bg-selago-50"
                    iconTextColor="text-portage-400"
                    icon="heroicons:tag"
                    currentValueColor="text-downriver-900"
                    currentValueTooltip="Sold"
                    statusLabel="From Today"
                    onClick={() => navigate("/sales-list")}
                />
                <StatCard
                    title="Total Expense"
                    currentValue={`৳${cardData?.total_expense}`}
                    backgroundColor="bg-seashellPeach-50"
                    iconTextColor="text-monaLisa-300"
                    icon="system-uicons:fullscreen"
                    currentValueColor="text-downriver-900"
                    currentValueTooltip="Expense"
                    statusLabel="From Today"
                />
                <StatCard
                    title="Pending Task"
                    currentValue={cardData?.total_tasks}
                    backgroundColor="bg-frostee-100"
                    iconTextColor="text-celadon-300"
                    icon="icon-park-outline:loading-one"
                    currentValueColor="text-downriver-900"
                    currentValueTooltip="Count"
                    statusLabel="Total Tasks"
                    onClick={() => navigate("/support-list")}
                />
            </>}
        </div>
    );
};

export default UserStats;