import React from "react";
import { DashboardDistributionColors } from "@/constant/data";
import Chart from "react-apexcharts";
import useDarkMode from "@/hooks/useDarkMode";

const Calculation = ({ height = 340, pieChart }) => {
  const [isDark] = useDarkMode();

  const labels = pieChart?.map(item => item?.label); 
  const series = pieChart?.map(item => item?.value);  

  const options = {
    labels: labels,  
    dataLabels: {
      enabled: true,
    },
    colors: [
      DashboardDistributionColors.success,
      DashboardDistributionColors.warning,
      "#A3A1FB", 
    ],
    legend: {
      position: "bottom",
      fontSize: "14px",
      fontFamily: "Inter",
      fontWeight: 500,
      labels: {
        colors: isDark ? "#CBD5E1" : "#475569",
      },
      markers: {
        width: 6,
        height: 6,
        offsetY: -1,
        offsetX: -5,
        radius: 12,
      },
      itemMargin: {
        horizontal: 10,
        vertical: 0,
      },
    },

    responsive: [
      {
        breakpoint: 480,
        options: {
          legend: {
            position: "bottom",
          },
        },
      },
    ],
  };

  return (
    <>
      <Chart options={options} series={series} type="pie" height={height} />
    </>
  );
};

export default Calculation;
