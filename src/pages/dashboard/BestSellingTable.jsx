import React from "react";
import NoImage from "@/assets/CRM/NoImage.png";
import BrokenImage from "@/assets/CRM/BrokenImage.svg";
import Button from "@/components/ui/Button";
import RakingIcon from "../../assets/CRM/rakingIcon.svg";
import { useNavigate } from "react-router-dom";
import Tooltip from "@/components/ui/Tooltip";
import Card from "@/components/ui/Card";

const BestSellingTable = ({ topSellingProducts }) => {
  const navigate = useNavigate();

  const handleDetailsClick = (product_id) =>
    navigate(`/details-product/${product_id}`);

  const renderTableCell = (content, classes = "px-4 py-2") => (
    <td className={classes}>{content}</td>
  );

  const renderImage = (imageUrl, name) => (
    <div className="rounded-sm w-16 h-16 flex items-center justify-center overflow-hidden">
      <img
        src={imageUrl ? `${import.meta.env.VITE_MEDIA_URL}/${imageUrl}` : NoImage}
        alt={name}
        className="w-full h-full object-cover"
        onError={(e) => {
          e.target.onerror = null;
          e.target.src = BrokenImage;
        }}
      />
    </div>
  );

  return (
    <div className="text-black rounded-lg shadow-sm mx-auto mt-4">
      <Card noBorder>
        <h4 className="card-title mb-5">Best Selling Products</h4>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-slate-100 dark:divide-slate-700">
            <thead className="bg-slate-200 dark:bg-slate-700">
              <tr>
                {["Image", "Name", "Total Sales", "Ranking", "Details"].map(
                  (heading) => (
                    <th
                      key={heading}
                      className="px-4 py-3  text-sm font-semibold text-gray-600 dark:text-slate-300 text-center"
                    >
                      {heading}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
              {topSellingProducts?.map((item, index) => (
                <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-slate-700">
                  {renderTableCell(renderImage(item.product?.image, item.product.name))}
                  {renderTableCell(
                    <Tooltip
                      content={
                        <>
                          Details of{" "}
                          <span className="font-bold">{item.product.name}</span>{" "}
                          Product
                        </>
                      }
                      placement="top"
                      arrow
                      animation="Interactive"
                    >
                      <span
                        onClick={() => handleDetailsClick(item.product_id)}
                        className="text-lg text-gray-700 hover:underline hover:text-primary-500 cursor-pointer block"
                      >
                        {item.product.name.length > 30
                          ? `${item.product.name.substring(0, 20)}...`
                          : item.product.name}
                      </span>
                    </Tooltip>
                  )}
                  {renderTableCell(
                    <p className="text-gray-900 font-semibold text-center">
                      {item.total_quantity.toLocaleString()} Pieces
                    </p>
                  )}
                  {renderTableCell(
                    <div className="flex items-center justify-center space-x-1">
                      <img src={RakingIcon} alt="Ranking" className="w-6 h-6" />
                      <span className="text-gray-900 font-semibold">{index + 1}</span>
                    </div>
                  )}
                  {renderTableCell(
                    <Button
                      icon="solar:eye-linear"
                      text="Details"
                      className="px-3 py-1 text-sm btn btn-primary"
                      onClick={() => handleDetailsClick(item.product_id)}
                    />,
                    "px-4 py-2 text-center"
                  )}
                </tr>
              ))}
            </tbody>
          </table>
          {!topSellingProducts?.length && (
            <p className="text-center py-4 text-gray-500">No data found</p>
          )}
        </div>
      </Card>
    </div>
  );
};

export default BestSellingTable;
