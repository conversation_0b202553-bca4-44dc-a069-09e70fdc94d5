import React from "react";
import Chart from "react-apexcharts";
import useDarkMode from "@/hooks/useDarkMode";
import { DashboardDistributionColors, formattedDate } from "@/constant/data";

const UserBarChart = ({ height = 340, lineChartData }) => {
  const [isDark] = useDarkMode();

  const completedData = lineChartData.map((item) => item.completed_count);
  const categories = lineChartData.map((item) =>
    formattedDate ? formattedDate(item.date) : item.date
  );

  const series = [
    {
      name: "Completed Count",
      data: completedData,
    },
  ];

  const options = {
    chart: {
      type: "bar", 
      toolbar: {
        show: false,
      },
    },
    plotOptions: {
      bar: {
        horizontal: false, 
        endingShape: "rounded",
        columnWidth: "50%", 
      },
    },
    legend: {
      show: true,
      position: "bottom",
      horizontalAlign: "center",
      fontSize: "12px",
      fontFamily: "Inter",
      offsetY: 0,
      markers: {
        width: 6,
        height: 6,
        offsetY: 0,
        offsetX: -5,
        radius: 12,
      },
      itemMargin: {
        horizontal: 18,
        vertical: 0,
      },
      labels: {
        colors: isDark ? "#CBD5E1" : "#475569",
      },
    },
    dataLabels: {
      enabled: false,
    },
    yaxis: {
      labels: {
        style: {
          colors: isDark ? "#CBD5E1" : "#475569",
          fontFamily: "Inter",
        },
      },
    },
    xaxis: {
      categories: categories, 
      labels: {
        offsetY: -3,
        style: {
          colors: isDark ? "#CBD5E1" : "#475569",
          fontFamily: "Inter",
        },
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    tooltip: {
      y: {
        formatter: function (val) {
          return val;
        },
      },
    },
    colors: [DashboardDistributionColors.primary],
    grid: {
      show: true,
      borderColor: isDark ? "#334155" : "#E2E8F0",
      strokeDashArray: 10,
      position: "back",
    },
  };

  return (
    <>
      <Chart options={options} series={series} type="bar" height={height} />
    </>
  );
};

export default UserBarChart;
