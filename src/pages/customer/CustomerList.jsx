import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import DeleteCustomer from "./DeleteCustomer";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import NoImage from "../../assets/CRM/NoImage.png";

const CustomerList = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery({ url: "clients", params: apiParam });
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Name", field: "name" },
    { label: "Image", field: "image" },
    { label: "Phone", field: "phone" },
    { label: "Status", field: "is_active" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "View",
      icon: "lets-icons:view-alt",
      onClick: (val) => {
        navigate(`/details-customer/${data?.data[val]?.id}`);
      },
    },
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/edit-customer/${data?.data[val]?.id}`);
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      image: (
        <>
          <img
            className="rounded-full h-10 w-10 object-cover"
            src={
              item.image
                ? `${import.meta.env.VITE_MEDIA_URL}/${item.image}`
                : NoImage
            }
            alt={item.name}
          />
        </>
      ),
      name: item.name,
      phone: item.phone,
      customer_source: item.customer_source,
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {" "}
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        title="Customers List"
        loading={isLoading || isFetching}
        columns={columns}
        actions={actions}
        goto={"Create New Customer"}
        gotoLink={"/create-customer"}
        changePage={changePage}
        data={tableData}
        filter={filter}
        setFilter={setApiParam}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
      <DeleteCustomer
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </div>
  );
};

export default CustomerList;
