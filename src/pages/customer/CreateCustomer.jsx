import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import {
  clientInitialValues,
  createValidationSchema,
  customerSources,
  isActiveOptions,
} from "@/formHandlers/CustomerCrud";
import { toast } from "react-toastify";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import React from "react";
import { useNavigate } from "react-router-dom";
import Button from "@/components/ui/Button";

const CreateCustomer = () => {
  const navigate = useNavigate();
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();
  const headerSlotContent = (
    <Button
      onClick={() => navigate("/customer-list")}
      type="button"
      className="btn btn-outline-primary"
    >
      Customer List
    </Button>
  );

  const handleSubmit = async (values, { resetForm, setFieldError }) => {
    const modifiedValues = { ...values };

    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        }
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const response = await postApi({
        end_point: "clients",
        body: formData,
      }).unwrap();
      toast.success("Customer Create Successfully!");
      navigate("/customer-list");
      resetForm();
    } catch (err) {
      // toast.error("Customer Create failed. Please try again.");
    }
  };

  return (
    <div>
      <Formik
        initialValues={clientInitialValues}
        validationSchema={createValidationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, errors }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Create Customer"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter name"
                />
                <InputField
                  label="Phone"
                  name="phone"
                  type="tel"
                  required
                  placeholder="Phone number"
                  onInput={(e) => {
                    e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  }}
                />
                <InputField
                  label="Email"
                  name="email"
                  type="email"
                  placeholder="Email"
                />
                <InputField
                  label="Company"
                  name="company_name"
                  type="text"
                  placeholder="Enter Company name"
                />

                <InputSelect
                  label="Customer Source"
                  name="customer_source"
                  options={customerSources}
                  placeholder="Customer Source"
                  required
                />

                <InputFile
                  label="image"
                  name="image"
                  type="file"
                  title="Upload image"
                  accept="image/*"
                />
                <InputField
                  label="Billing Address"
                  name="address"
                  type="text"
                  placeholder="Billing Address"
                />
                <InputField
                  label="Shipping Address"
                  name="shipping_address"
                  type="text"
                  placeholder="Shipping Address"
                />
              </div>
              <div className="my-4">
                <TextAreaField
                  label="Details"
                  name="source_details"
                  type="text"
                  placeholder="Details"
                />
              </div>

              <div className="w-full text-end my-5">
                <button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateCustomer;
