import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import NoImage from "../../assets/CRM/NoImage.png";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import Tooltip from "@/components/ui/Tooltip";

const DetailsCustomer = () => {
  const navigate = useNavigate();
  const { id } = useParams();

  const { data: productData } = useGetApiQuery({ url: `clients/${id}` });
  const customer = productData;

  if (!customer) {
    return <div>No Customer Details available.</div>;
  }

  return (
    <>
      <div className="grid gap-4 grid-cols-12 mt-5">
        <div className="xl:col-span-12 col-span-12 space-y-4">
          <Card>
            <div className="flex justify-between items-center">
              <div>
                <span className="font-bold text-lg">
                  Customer Details of{" "}
                  <span className="text-primary-500">{customer.name}</span>
                </span>
              </div>
              <div className="flex gap-3">
                <div>
                  <Tooltip
                    content="Back to Customer List"
                    placement="top"
                    arrow
                    animation="scale"
                  >
                    <div
                      className="m-1"
                      onClick={() => navigate("/customer-list")}
                    >
                      <Icon
                        icon="ic:round-arrow-back"
                        className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                      />
                    </div>
                  </Tooltip>
                </div>
                <div>
                  <Tooltip
                    content={`Edit ${customer.name} of Customer`}
                    placement="top"
                    arrow
                    animation="scale"
                  >
                    <div
                      className="m-1"
                      onClick={() => navigate(`/edit-customer/${customer.id}`)}
                    >
                      <Icon
                        icon="mynaui:edit-one"
                        className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                      />
                    </div>
                  </Tooltip>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>

      <div className="grid gap-4 grid-cols-12 mt-5">
        <div className="xl:col-span-5 col-span-12">
          <Card>
            <div className="mb-6">
              <img
                src={
                  customer.image
                    ? `${import.meta.env.VITE_MEDIA_URL}/${customer.image}`
                    : NoImage
                }
                alt={customer.name}
                className="w-full block object-cover"
              />
            </div>
          </Card>
        </div>

        <div className="xl:col-span-7 col-span-12">
          <Card>
            <div className="space-y-5">
              <div className="bg-slate-100 px-3 py-2 rounded-full flex items-center card-title font-bold text-lg my-2 gap-5">
                <Icon
                  icon="raphael:customer"
                  className="w-6 h-6 text-primary-500"
                />
                {customer.name || "N/A"}
              </div>
              <div className="bg-slate-100 px-3 py-2 rounded-full flex items-center card-title font-bold text-lg my-2 gap-5">
                <Icon
                  icon="line-md:phone-call-loop"
                  className="w-6 h-6 text-primary-500"
                />
                {customer.phone || "N/A"}
              </div>
              <div className="bg-slate-100 px-3 py-2 rounded-full flex items-center card-title font-bold text-lg my-2 gap-5">
                <Icon
                  icon="line-md:email-filled"
                  className="w-6 h-6 text-primary-500"
                />
                {customer.email || "N/A"}
              </div>
              <div className="bg-slate-100 px-3 py-2 rounded-full flex items-center card-title font-bold text-lg my-2 gap-5">
                <Icon icon="mdi:company" className="w-6 h-6 text-primary-500" />
                {customer.company_name || "N/A"}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-100 px-3 py-2 rounded-full flex items-center card-title font-bold text-lg my-2 gap-5">
                  <Icon
                    icon="material-symbols-light:source-notes-outline"
                    className="w-6 h-6 text-primary-500 font-bold"
                  />
                  <span className="">Source Type:</span>
                  <span className="bg-primary-200 text-primary-600 py-1  px-2 rounded-lg">
                    {customer.customer_source || "N/A"}
                  </span>
                </div>
                <div className="bg-slate-100 px-3 py-2 rounded-full flex items-center card-title font-bold text-lg my-2 gap-5">
                  <Icon
                    icon="hugeicons:user-status"
                    className="w-6 h-6 text-primary-500 font-bold"
                  />
                  <span className="">Status:</span>
                  <Badge
                    className={
                      customer.is_active
                        ? `text-success-500 font-bold text-base bg-success-100 px-2 py-1`
                        : `text-danger-500 font-bold text-base bg-danger-100 px-2 py-1`
                    }
                  >
                    {customer.is_active ? (
                      <>
                        Active
                        <Icon icon="icon-park-solid:correct" className="ml-2" />
                      </>
                    ) : (
                      <>
                        <Icon icon="maki:cross" className="mr-2" />
                        Inactive
                      </>
                    )}
                  </Badge>
                </div>
              </div>

              <div className="bg-slate-100 px-3 py-2 rounded-lg">
                <div className="flex items-center card-title font-bold text-lg my-2 gap-5">
                  <Icon
                    icon="hugeicons:note-done"
                    className="w-6 h-6 text-primary-500 font-bold"
                  />
                  <span className="">Details:</span>
                </div>
                <div className="ms-10 text-base text-slate-500 font-semibold text-justify">
                  {customer.source_details || "N/A"}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-100 px-3 py-2 rounded-lg">
                  <div className="flex items-center card-title font-bold text-lg my-2 gap-5">
                    <Icon
                      icon="mdi:file-location"
                      className="w-6 h-6 text-primary-500 font-bold"
                    />
                    <span className="">Billing Address:</span>
                  </div>
                  <div className="ms-10 text-base text-slate-500 font-semibold">
                    {customer.address || "N/A"}
                  </div>
                </div>
                <div className="bg-slate-100 px-3 py-2 rounded-lg">
                  <div className="flex items-center card-title font-bold text-lg my-2 gap-5">
                    <Icon
                      icon="mdi:file-location"
                      className="w-6 h-6 text-primary-500 font-bold"
                    />
                    <span className="">Shipping Address:</span>
                  </div>
                  <div className="ms-10 text-base text-slate-500 font-semibold">
                    {customer.shipping_address || "N/A"}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </>
  );
};

export default DetailsCustomer;
