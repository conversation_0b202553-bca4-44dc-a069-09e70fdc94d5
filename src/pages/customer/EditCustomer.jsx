import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import {
  createValidationSchema,
  customerSources,
  isActiveOptions,
} from "@/formHandlers/CustomerCrud";
import {
  useGetApiWithIdQuery,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import Switch from "@/components/ui/Switch";
import { toast } from "react-toastify";

const EditClient = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { data: client } = useGetApiWithIdQuery(["clients", id]);
  const [updateApi] = useUpdateApiMutation();

  const headerSlotContent = (
    <Button
      onClick={() => navigate("/customer-list")}
      className="btn btn-outline-primary"
    >
      Customer List
    </Button>
  );

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    // Object.keys(values).forEach((key) => {
    //   if (key === "image") {
    //     // Append the image field to formData
    //     if (values.image instanceof File) {
    //       formData.append(key, values.image);
    //     }
    //   } else {
    //     formData.append(key, values[key]);
    //   }
    // });

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        if (values.image instanceof File) {
          formData.append(key, values.image);
        }
      } else if (key === "is_active") {
        formData.append(key, values[key] ? 1 : 0);
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const data = {
        end_point: "clients/" + id,
        body: formData,
      };
      const response = await updateApi(data).unwrap();
      resetForm();
      navigate("/customer-list");
      toast.success("Customer Information Updated Successfully!");
    } catch (err) {
      toast.error("Failed to Update Customer Information. Please try again.");
    }
  };

  return (
    <div>
      <Formik
        initialValues={{
          name: client?.name || "",
          phone: client?.phone || "",
          email: client?.email || "",
          address: client?.address || "",
          shipping_address: client?.shipping_address || "",
          image: client?.image || "",
          company_name: client?.company_name || "",
          customer_source: client?.customer_source || "",
          source_details: client?.source_details || "",
          is_active: client?.is_active === 1,
        }}
        validationSchema={createValidationSchema()}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values, setFieldValue }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Edit Customer"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter name"
                />
                <InputField
                  label="Phone"
                  name="phone"
                  type="tel"
                  required
                  placeholder="Phone number"
                  onInput={(e) => {
                    e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  }}
                />
                <InputField
                  label="Email"
                  name="email"
                  type="email"
                  placeholder="Email"
                />
                <InputField
                  label="Company"
                  name="company_name"
                  type="text"
                  placeholder="Enter Company name"
                />
                <InputSelect
                  label="Customer Source"
                  name="customer_source"
                  options={customerSources}
                  placeholder="Customer Source"
                  required
                />
                <InputFile
                  label="image"
                  name="image"
                  type="file"
                  title="Upload image"
                  accept="image/*"
                />
                <InputField
                  label="Billing Address"
                  name="address"
                  type="text"
                  placeholder="Billing Address"
                />
                <InputField
                  label="Shipping Address"
                  name="shipping_address"
                  type="text"
                  placeholder="Shipping Address"
                />
              </div>
              <div className="my-5">
                <TextAreaField
                  label="Details"
                  name="source_details"
                  type="text"
                  placeholder="Details"
                />
              </div>
              <div className="my-5">
                <Switch
                  label="Is Active"
                  activeClass="bg-success-500"
                  name="is_active"
                  value={values.is_active}
                  onChange={() => setFieldValue("is_active", !values.is_active)}
                />
              </div>

              <div className="w-full text-end">
                <Button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditClient;
