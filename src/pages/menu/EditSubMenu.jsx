import Card from "@/components/ui/Card";
import CustomSelect from "@/components/ui/form/CustomSelect";
import InputField from "@/components/ui/form/InputField";
import InputSelect from "@/components/ui/form/InputSelect";
import {
  isActiveOptions,
  submenuValidationSchema,
} from "@/formHandlers/SubmenuCrud";
import {
  useGetApiQuery,
  useGetApiWithIdQuery,
  useUpdateApiJsonMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";

const EditSubmenu = () => {
  const allRoles = useGetApiQuery({ url: "all-roles" });
  const roleList = allRoles?.data?.map((role) => ({
    name: role?.name,
    id: role?.id,
  }));
  const { id } = useParams();
  const [updateApiJson] = useUpdateApiJsonMutation();
  const { data: submenu } = useGetApiWithIdQuery(["sub-menus", id]);
  const navigate = useNavigate();
  const headerSlotContent = (
    <Button
      onClick={() => navigate("/submenu-list")}
      type="button"
      className="btn btn-outline-primary"
    >
      Submenu List
    </Button>
  );

  const handleSubmit = async (values, { resetForm }) => {
    const modifiedValues = { ...values };
    try {
      const data = {
        end_point: "sub-menus/" + id,
        body: modifiedValues,
      };

      const response = await updateApiJson(data).unwrap();
      toast.success("Submenu Updated Successfully!");
      resetForm();
      navigate("/submenu-list");
    } catch (err) {
      // console.error("Submission failed:", err);
      toast.error("Submenu Updated failed. Please try again.");
    }
  };
  const initialRoleIds = submenu?.roles?.map((role) => role.id) || [];

  console.log(submenu, "this is submenu");
  return (
    <div className="w-full">
      <Card
        headerslot={headerSlotContent}
        title="Edit Submenu"
        className="w-full"
        titleClass="text-lg font-bold text-gray-800"
      >
        <Formik
          initialValues={
            {
              ...submenu,
              role_ids: initialRoleIds,
              is_active: submenu?.is_active === true ? 1 : 0,
            } || []
          }
          onSubmit={handleSubmit}
          enableReinitialize
          validationSchema={submenuValidationSchema}
        >
          {({ isSubmitting, values }) => (
            <Form>
              {console.log(values, "this is form values from submenu")}
              <div className="grid grid-cols-2 gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter submenu name"
                />
                <CustomSelect
                  name="role_ids"
                  options={roleList}
                  label="Select Roles"
                  valueKey="id"
                  labelKey="name"
                  required
                />
                <InputField
                  label="Icon"
                  name="icon"
                  type="text"
                  // required
                  placeholder="Enter submenu icon (e.g., 'home', 'menu')"
                />
                <InputSelect
                  label="Is Active"
                  name="is_active"
                  options={isActiveOptions}
                  placeholder="Select active status"
                  required
                />
                <InputField
                  label="Url"
                  name="url"
                  type="text"
                  required
                  placeholder="Enter submenu URL (e.g., 'about')"
                />
                <InputField
                  label="Order"
                  name="order"
                  type="number"
                  required
                  placeholder="Enter submenu display order"
                />
              </div>
              <div className="flex justify-end mt-4">
                <Button
                  disabled={isSubmitting}
                  type="submit"
                  className="btn btn-primary"
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </Card>
    </div>
  );
};

export default EditSubmenu;
