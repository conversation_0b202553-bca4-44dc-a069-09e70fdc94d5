import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import Badge from "@/components/ui/Badge";
import { Icon } from "@iconify/react";
// import Delete from "./DeleteMenu";
import { useNavigate } from "react-router-dom";
import DeleteSubmenu from "./DeleteSubmenu";

const SubMenuList = () => {
  const navigate = useNavigate();
  const [deleteData, setDeleteData] = useState(null);
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery({ url: "sub-menus", params: apiParam });
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Name", field: "name" },
    { label: "Icon", field: "icon" },
    { label: "Order", field: "order" },
    { label: "Route", field: "url" },
    { label: "Status", field: "is_active" },
    { label: "Actions", field: "" },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      name: item.name,
      icon: (
        <>
          <Icon icon={item.icon} width={25} />
        </>
      ),
      order: item.order,
      url: item.url,
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/edit-submenu/${data?.data[val]?.id}`);
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const devOption = <>
    <span>Submenu List</span>
    <span className="text-blue-800 ml-3 bg-gray-300 p-3 rounded-lg">Only for Developers</span>
  </>

  return (
    <div>
      <BasicTablePage
        loading={isLoading || isFetching}
        title={devOption}
        columns={columns}
        actions={actions}
        changePage={changePage}
        data={tableData}
        currentPage={data?.current_page}
        filter={filter}
        setFilter={setApiParam}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
      <DeleteSubmenu
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </div>
  );
};

export default SubMenuList;
