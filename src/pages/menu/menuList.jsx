import React, { useState } from "react";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import Badge from "@/components/ui/Badge";
import { Icon } from "@iconify/react";
import Delete from "./DeleteMenu";
import { useNavigate } from "react-router-dom";
import CreateSubMenu from "./CreateSubMenu";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";

const MenuList = () => {
  const navigate = useNavigate();
  const [deleteData, setDeleteData] = useState(null);
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery({ url: "menus", params: apiParam });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showCreateSubMenuModal, setShowCreateSubMenuModal] = useState(false);
  const [selectedMenuId, setSelectedMenuId] = useState(null);

  const openCreateSubMenuModal = (menuId) => {
    setSelectedMenuId(menuId);
    setShowCreateSubMenuModal(true);
  };

  const closeCreateSubMenuModal = () => {
    setShowCreateSubMenuModal(false);
    setSelectedMenuId(null);
  };

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Name", field: "name" },
    { label: "Icon", field: "icon" },
    { label: "Sub Menus", field: "sub_menus" },
    { label: "Order", field: "order" },
    { label: "Route", field: "url" },
    { label: "Status", field: "is_active" },
    { label: "Actions", field: "" },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      name: item.name,
      sub_menus: (
        <div className="flex items-center gap-3">
          {item.sub_menus.length > 0 ? (
            <>
              {item?.sub_menus?.map((submenu, index) => (
                <Badge
                  key={index}
                  className="text-xs bg-gray-100 text-black-500 font-medium"
                >
                  {submenu?.name}
                </Badge>
              ))}
            </>
          ) : (
            <Badge className="text-medium text-red-400  bg-red-100">
              No Submenu
            </Badge>
          )}
          <div className="flex items-center gap-2">
            <Button
              type="button"
              className="inline-flex items-center p-1 border border-primary-400 text-base font-medium rounded-full shadow-sm text-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 hover:bg-primary-100"
              onClick={(e) => {
                e.stopPropagation();
                openCreateSubMenuModal(item.id);
              }}
            >
              <Icon icon="akar-icons:plus" className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ),
      icon: (
        <span className="font-extrabold text-lg">
          <Icon icon={item.icon} width={25} />
        </span>
      ),
      order: item.order,
      url: item.url,
      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/edit-menu/${data?.data[val]?.id}`);
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const devOption = <>
    <span>Menu List</span>
    <span className="text-blue-800 ml-3 bg-gray-300 p-3 rounded-lg">Only for Developers</span>
  </>

  return (
    <div>
      <BasicTablePage
        loading={isLoading || isFetching}
        title={devOption}
        columns={columns}
        actions={actions}
        goto={"Create New Menu"}
        gotoLink={"/create-menu"}
        changePage={changePage}
        filter={filter}
        setFilter={setApiParam}
        data={tableData}
        currentPage={data?.current_page}
        totalPages={Math.ceil(data?.total / data?.per_page)}
      />

      {showCreateSubMenuModal && (
        <Modal
          activeModal={showCreateSubMenuModal}
          onClose={closeCreateSubMenuModal}
          title="Create SubMenu"
          className="max-w-3xl"
        >
          <CreateSubMenu
            menuId={selectedMenuId}
            closeModal={closeCreateSubMenuModal}
          />
        </Modal>
      )}

      <Delete
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </div>
  );
};

export default MenuList;
