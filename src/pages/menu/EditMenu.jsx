import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Card from "@/components/ui/Card";
import {
  useGetApiQuery,
  useGetApiWithIdQuery,
  useGetMenuItemsQuery,
  useUpdateApiJsonMutation,
} from "@/store/api/apihandler/commonSlice";
import {
  createValidationSchema,
  isActiveOptions,
} from "@/formHandlers/MenuCrud";
import { useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import InputSelect from "@/components/ui/form/InputSelect";
import CustomSelect from "@/components/ui/form/CustomSelect";
import TextAreaField from "@/components/ui/form/TextAreaField";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";

const EditMenu = () => {
  const { id } = useParams();
  const { data: menu } = useGetApiWithIdQuery(["menus", id]);

  console.log(menu, "it is menu");

  const navigate = useNavigate();
  const headerSlotContent = (
    <Button
      type="button"
      className="btn btn-outline-primary"
      onClick={() => navigate("/menu-list")}
    >
      Menu List
    </Button>
  );
  const [updateApiJson] = useUpdateApiJsonMutation();

  const { user } = useSelector((state) => state.auth);
  const { data: cachedMenu } = useGetMenuItemsQuery(user?.user?.id);

  const allRoles = useGetApiQuery({ url: "all-roles" });
  const roleList = allRoles?.data?.map((role) => ({
    name: role?.name,
    id: role?.id,
  }));

  const handleSubmit = async (values, { resetForm }) => {
    try {
      const data = {
        end_point: "menus/" + id,
        body: values,
      };

      const response = await updateApiJson(data).unwrap();
      toast.success("Menu Updated Successfully!");
      resetForm();
      navigate("/menu-list");
    } catch (err) {
      toast.error("Menu Updated failed. Please try again.");
    }
  };

  return (
    <div className="w-full">
      <Formik
        initialValues={
          {
            ...menu,
            role_ids: menu?.roles?.map((el) => el.id),
            is_active: menu?.is_active == true ? 1 : 0,
          } || []
        }
        validationSchema={createValidationSchema(cachedMenu?.menus, "edit")}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values }) => (
          <Form>
            {console.log(values, "it is values")}
            <Card
              headerslot={headerSlotContent}
              title="Edit Menu"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-2 gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter menu name"
                />
                {/* <InputField
                                    label="Description"
                                    name="description"
                                    type="text"
                                    required
                                    placeholder="Enter description"
                                /> */}
                <InputField
                  label="Icon"
                  name="icon"
                  type="text"
                  // required
                  placeholder="Enter icon"
                />
                <InputField
                  label="URL"
                  name="url"
                  type="text"
                  // required
                  placeholder="Enter URL"
                />
                <InputField
                  label="Order"
                  name="order"
                  type="number"
                  required
                  placeholder="Enter order"
                />
                <InputSelect
                  label="Is Active"
                  name="is_active"
                  options={isActiveOptions}
                  placeholder="Select status"
                  required
                />
                <CustomSelect
                  name="role_ids"
                  options={roleList}
                  label="Select Roles"
                  valueKey="id"
                  labelKey="name"
                  required
                />
              </div>
              <div className="w-full mt-4">
                <TextAreaField
                  label="Description"
                  name="description"
                  // required
                  placeholder="Enter your description here"
                  // columns={7}
                />
              </div>

              <div className="w-full text-end">
                <Button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditMenu;
