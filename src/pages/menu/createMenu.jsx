import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Card from "@/components/ui/Card";
import {
  useGetApiQuery,
  useGetMenuItemsQuery,
  usePostApiMutation,
} from "@/store/api/apihandler/commonSlice";
import {
  createValidationSchema,
  isActiveOptions,
  menuInitialValues,
} from "@/formHandlers/MenuCrud";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import InputSelect from "@/components/ui/form/InputSelect";
import CustomSelect from "@/components/ui/form/CustomSelect";
import TextAreaField from "@/components/ui/form/TextAreaField";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";

const CreateMenu = () => {
  const navigate = useNavigate();
  const headerSlotContent = (
    <Button
      type="button"
      className="btn btn-outline-primary"
      onClick={() => navigate("/menu-list")}
    >
      Menu List
    </Button>
  );
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();

  const { user } = useSelector((state) => state.auth);
  const { data: cachedMenu } = useGetMenuItemsQuery(user?.user?.id);

  const allRoles = useGetApiQuery({ url: "all-roles" });
  const roleList = allRoles?.data?.map((role) => ({
    name: role?.name,
    id: role?.id,
  }));

  const handleSubmit = async (values, { resetForm }) => {
    const modifiedValues = { ...values };
    try {
      const response = await postApi({
        end_point: "menus",
        body: values,
      }).unwrap();
      toast.success("Menu Created Successfully!");
      resetForm();
      navigate("/menu-list");
    } catch (err) {
      // toast.error("Menu Created failed. Please try again.");
    }
  };

  return (
    <div className="w-full">
      <Formik
        initialValues={menuInitialValues}
        validationSchema={createValidationSchema(cachedMenu?.menus)}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Create Menu"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-2 gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter menu name"
                />

                <InputField
                  label="Icon"
                  name="icon"
                  type="text"
                  placeholder="Enter icon"
                />
                <InputField
                  label="URL"
                  name="url"
                  type="text"
                  placeholder="Enter URL"
                />
                <InputField
                  label="Order"
                  name="order"
                  type="number"
                  required
                  placeholder="Enter order"
                />
                <InputSelect
                  label="Is Active"
                  name="is_active"
                  options={isActiveOptions}
                  placeholder="Select status"
                  required
                />
                <CustomSelect
                  name="role_ids"
                  options={roleList}
                  label="Select Roles"
                  valueKey="id"
                  labelKey="name"
                  required
                />
              </div>
              <div className="w-full mt-4">
                <TextAreaField
                  label="Description"
                  name="description"
                  placeholder="Enter your description here"
                />
              </div>

              <div className="w-full text-end">
                <Button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateMenu;
