import Button from "@/components/ui/Button";
import CustomSelect from "@/components/ui/form/CustomSelect";
import InputField from "@/components/ui/form/InputField";
import InputSelect from "@/components/ui/form/InputSelect";
import {
  isActiveOptions,
  subMenuInitialValues,
  submenuValidationSchema,
} from "@/formHandlers/SubmenuCrud";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import React from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

const CreateSubMenu = ({ menuId, closeModal }) => {
  const allRoles = useGetApiQuery({ url: "all-roles" });
  const roleList = allRoles?.data?.map((role) => ({
    name: role?.name,
    id: role?.id,
  }));

  const navigate = useNavigate();
  const headerSlotContent = (
    <button
      onClick={() => navigate("/submenu-list")}
      className="bg-blue-500 text-white p-2 rounded"
    >
      Menu List
    </button>
  );
  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();

  const handleSubmit = async (values, { resetForm }) => {
    const modifiedValues = { ...values, menu_id: menuId };
    try {
      const response = await postApi({
        end_point: "sub-menus",
        body: modifiedValues,
      }).unwrap();
      toast.success("Submenu Created Successfully!");
      resetForm();
      closeModal();
      navigate("/menu-list");
    } catch (err) {
      // console.error("Submission failed:", err);
      toast.error("Submenu Created failed. Please try again.");
    }
  };
  return (
    <div className="w-full">
      <Formik
        initialValues={subMenuInitialValues}
        onSubmit={handleSubmit}
        validationSchema={submenuValidationSchema}
      >
        {({ isSubmitting, values }) => (
          <Form>
            <div className="grid grid-cols-2 gap-4">
              <InputField
                label="Name"
                name="name"
                type="text"
                required
                placeholder="Enter submenu name"
              />
              <CustomSelect
                name="role_ids"
                options={roleList}
                label="Select Roles"
                valueKey="id"
                labelKey="name"
                required
              />
              <InputField
                label="Icon"
                name="icon"
                type="text"
                // required
                placeholder="Enter submenu icon (e.g., 'home', 'menu')"
              />
              <InputSelect
                label="Is Active"
                name="is_active"
                options={isActiveOptions}
                placeholder="Select active status"
                required
              />
              <InputField
                label="Url"
                name="url"
                type="text"
                required
                placeholder="Enter submenu URL (e.g., 'about')"
              />
              <InputField
                label="Order"
                name="order"
                type="number"
                required
                placeholder="Enter submenu display order"
              />
            </div>
            <div className="flex justify-end mt-3">
              <Button
                disabled={isSubmitting}
                type="submit"
                className="btn btn-primary"
              >
                {isSubmitting ? "Submitting..." : "Submit"}
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateSubMenu;
