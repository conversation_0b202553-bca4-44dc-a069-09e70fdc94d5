.area {
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Courier New", Courier, monospace;
  font-weight: bold;
}

.tags-input-container {
  border: 2px solid #e77979;
  padding: 0.5em;
  border-radius: 3px;
  width: min(80vw, 600px);
  margin-top: 1em;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5em;
}

.tag-item {
  background-color: rgb(218, 216, 216);
  display: inline-block;
  padding: 0.5em 0.75em;
  margin: 0.1em;
  border-radius: 20px;
}
.tag-item .close {
  height: 20px;
  width: 20px;
  background-color: rgb(236, 125, 136);
  color: #fff;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-left: 0.5em;
  font-size: 18px;
  cursor: pointer;
}

.tags-input {
  flex-grow: 1;
  padding: 0.3em;
  border: none;
  outline: none;
  border-radius: 10px;
}
/* 
.tag-item {
  background-color: rgb(218, 216, 216);
  display: inline-block;
  padding: 0.5em 0.75em;
  margin: 0.1em;
  border-radius: 20px;
  border: 2px solid transparent;
}

.tag-item.active {
  border-color: green;
}

.tag-item.inactive {
  border-color: red;
}

.tag-item .close {
  height: 20px;
  width: 20px;
  background-color: rgb(236, 125, 136);
  color: #fff;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-left: 0.5em;
  font-size: 18px;
  cursor: pointer;
}

.tags-input {
  flex-grow: 1;
  padding: 0.3em;
  border: none;
  outline: none;
  border-radius: 10px;
} */
