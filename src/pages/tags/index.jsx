import { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import "./tag.css";
import {
  useGetApiQuery,
  useUpdateApiJsonMutation,
  usePostApiMutation,
  useDeleteApiMutation,
} from "@/store/api/apihandler/commonSlice";

function Index() {
  const { data, isSuccess, isFetching, isError } = useGetApiQuery({
    url: "/product-tags",
  });

  const [updateApiJson] = useUpdateApiJsonMutation();
  const [postApi] = usePostApiMutation();
  const [deleteApi] = useDeleteApiMutation();
  const [tags, setTags] = useState([]);
  const [updateState, setUpdateState] = useState(null);
  const [editTag, setEditTag] = useState("");
  const [message, setMessage] = useState("");

  useEffect(() => {
    if (isSuccess && data) {
      console.log("Fetched data: ", data);
    }
    if (isError) {
      console.error("Error fetching data");
    }
  }, [isSuccess, isError, data]);

  const handleKeyDown = (e) => {
    if (e.key !== "Enter") return;
    const value = e.target.value;
    if (!value.trim()) return;
    setTags([...tags, value]);
    e.target.value = "";
  };

  const removeTag = (index) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    if (tags.length === 0) {
      setMessage("Please add tags");
      return;
    }

    const tagArr = tags.map((tag) => ({
      name: tag,
      description: null,
      is_active: true,
    }));

    try {
      console.log("Sending data: ", tagArr);

      const result = await postApi({
        end_point: "product-tags",
        body: { tags: tagArr },
      }).unwrap();

      console.log("API response: ", result);

      setMessage(result.message || "Tags submitted successfully");
      setTags([]);
    } catch (error) {
      console.error("Error submitting tags: ", error);
      setMessage(error.data?.message || "Failed to submit tags");
    }
  };

  const handleDelete = async (id) => {
    try {
      const result = await deleteApi({
        end_point: `/product-tags/${id}`,
        body: {},
      }).unwrap();

      setMessage(result.message || "Tag deleted successfully");

      const updatedTags = data.data.filter((tag) => tag.id !== id);
      data.data = updatedTags;
    } catch (error) {
      setMessage(error.data?.message || "Delete tag");
    }
  };

  const handleEditSubmit = async (id) => {
    if (!editTag.trim()) return;

    try {
      const data = {
        url: `/product-tags/${id}`,
        body: {
          name: editTag,
        },
      };

      const result = await updateApiJson(data).unwrap();

      setMessage(result.message || "Tag updated successfully");

      const updatedTags = data.data.map((tag) =>
        tag.id === id ? { ...tag, name: editTag } : tag
      );
      data.data = updatedTags;

      setUpdateState(null);
    } catch (error) {
      setMessage(error.data?.message || "Failed to update tag");
    }
  };

  const TagsEdit = ({ current }) => {
    const [edit, setEdit] = useState(current.name);

    const submitKeyDown = (e) => {
      if (e.key !== "Enter") return;
      handleEditSubmit(current.id);
    };

    return (
      <span className="tag-item">
        <input
          type="text"
          className="tags-input"
          placeholder="Edit name"
          name="edit"
          value={edit}
          onChange={(e) => {
            setEdit(e.target.value);
            setEditTag(e.target.value);
          }}
          onKeyDown={submitKeyDown}
        />
        <div className="flex gap-2 my-2">
          <span className="pointer" onClick={() => setUpdateState(null)}>
            <Icon icon="mdi:minus-circle" color="red" width="17" />
          </span>
          <span
            className="pointer"
            onClick={() => handleEditSubmit(current.id)}
          >
            <Icon icon="mdi:upload" color="green" width="17" />
          </span>
        </div>
      </span>
    );
  };

  return (
    <div className="text-center">
      <div className="my-5">
        {message && <p className="message">{message}</p>}
        {isSuccess && data && Array.isArray(data.data) && (
          <div>
            {data.data.map((tag, index) => (
              <div className="tag-item" key={index}>
                {updateState === tag.id ? (
                  <TagsEdit current={tag} />
                ) : (
                  <>
                    {/* Show the tag name */}
                    <span className="text me-1 flex my-auto items-center gap-2">
                      <Icon icon="ic:outline-title" color="blue" width="15" />{" "}
                      {tag.name}
                    </span>
                    <div className="flex gap-2 my-2">
                      {/* Edit icon */}
                      <span
                        className="pointer"
                        onClick={() => {
                          setUpdateState(tag.id);
                          setEditTag(tag.name);
                        }}
                      >
                        <Icon icon="mdi:pencil" width="17" />
                      </span>
                      {/* Delete icon */}
                      <span
                        className="pointer"
                        onClick={() => handleDelete(tag.id)}
                      >
                        <Icon icon="mdi:trash-can" color="red" width="17" />
                      </span>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="area">
        <div className="tags-input-container">
          {tags.map((tag, index) => (
            <div className="tag-item" key={index}>
              <span className="text">{tag?.slice(0, 20)}</span>
              <span className="close" onClick={() => removeTag(index)}>
                &times;
              </span>
            </div>
          ))}
          <input
            onKeyDown={handleKeyDown}
            type="text"
            className="tags-input"
            placeholder="Type something and enter!"
          />
        </div>
      </div>
      <div className="text-center mt-2">
        <button onClick={handleSubmit} className="btn btn-success btn-sm">
          Submit
        </button>
      </div>
    </div>
  );
}

export default Index;
