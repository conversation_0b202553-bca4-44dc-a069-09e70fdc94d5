import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import TextAreaField from "@/components/ui/form/TextAreaField";
import {
  createValidationSchema,
  isActiveOptions,
} from "@/formHandlers/CategoryCrud";
import {
  useGetApiQuery,
  useGetApiWithIdQuery,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";

const Edit = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Fetch all categories using API query
  const { data: categoriesData, isLoading: isCategoriesLoading } =
    useGetApiQuery({ url: "categories" });

  // Filter only active categories
  const allCategories = categoriesData?.data?.filter(
    (item) => item.is_active === true
  );

  // Convert categories to the required format for InputSelect
  const allCategoryOptions = allCategories?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  const { data: category } = useGetApiWithIdQuery(["categories", id]);
  const [updateApi] = useUpdateApiMutation();

  const headerSlotContent = (
    <Button
      onClick={() => navigate("/model-list")}
      className="btn btn-outline-primary"
    >
      Model List
    </Button>
  );

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        } else if (Array.isArray(values.image)) {
          // If multiple files are uploaded
          values.image.forEach((file) => formData.append(key, file));
        }
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const data = {
        end_point: "categories/" + id,
        body: formData,
      };

      const response = await updateApi(data).unwrap();
      toast.success("Model Updated Successfully!");
      resetForm();
      navigate("/model-list");
    } catch (err) {
      // console.error("Submission failed:", err);
      toast.error("Model Updated failed. Please try again.");
    }
  };
  // console.log(category);

  // Find the parent category name using parent_id
  // Find the parent category name using parent_id
  const parentCategoryName =
    allCategoryOptions?.find((option) => option.value === category?.parent_id)
      ?.label || "";

  return (
    <div>
      <Formik
        initialValues={{
          name: category?.name || "",
          image: category?.image || "",
          parent_id: category?.parent_id || "",
          description: category?.description || "",
          is_active: category?.is_active === true ? 1 : 0,
        }}
        validationSchema={createValidationSchema()}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Edit Model"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-1 md:grid-cols-2  gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter name"
                />
                <InputFile
                  label="image"
                  name="image"
                  type="file"
                  title="Upload your file"
                  accept="image/*"
                />
                <div className="">
                  <InputSelect
                    label="Category"
                    name="parent_id"
                    options={allCategoryOptions}
                    placeholder="Select Category"
                    required
                  />
                </div>

                <InputSelect
                  label="Is Active"
                  name="is_active"
                  options={isActiveOptions}
                  placeholder="Select status"
                  required
                />
              </div>
              <div className="mt-4">
                <TextAreaField
                  label="Description"
                  name="description"
                  type="text"
                  placeholder="Enter description"
                />
              </div>

              <div className="w-full text-end my-5">
                <button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Edit;
