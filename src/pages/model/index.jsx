import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import NoImage from "../../assets/CRM/NoImage.png";
import DeleteSupport from "./deleteModel";

const index = () => {
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const { data, isLoading, isFetching } = useGetApiQuery({
    url: "models",
    params: apiParam,
  });
  const navigate = useNavigate();

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Image", field: "image" },
    { label: "Name", field: "name" },
    { label: "Status", field: "is_active" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/edit-model/${data?.data[val]?.id}`, {
          state: { support: data?.data[val] },
        });
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const tableData = data?.data?.map((item, index) => {
    return {
      id: item.id,
      image: (
        <Tooltip
          content={
            <>
              Details of <span className="font-bold">{item.name}</span> model
            </>
          }
          placement="top"
          arrow
          animation="interactive"
        >
          <div className="inline-block">
            <img
              className="h-14 w-14 border-dashed border-slate-200 border hover:border-2 hover:border-primary-500"
              src={
                item.image
                  ? `${import.meta.env.VITE_MEDIA_URL}/${item.image}`
                  : NoImage
              }
              alt={item.name}
            />
          </div>
        </Tooltip>
      ),
      name: (
        <Tooltip
          content={
            <>
              Details of <span className="font-bold">{item.name}</span>
            </>
          }
          placement="top"
          arrow
          animation="Interactive"
        >
          <button
            className="hover:underline hover:text-primary-500"
          >
            {item.name.length > 16 ? `${item.name.slice(0, 16)}...` : item.name}
          </button>
        </Tooltip>
      ),

      is_active: (
        <Badge
          className={
            item.is_active
              ? `bg-success-500 text-white`
              : `bg-danger-500 text-white`
          }
        >
          {item.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    };
  });

  return (
    <div>
      <BasicTablePage
        title="Model List"
        loading={isLoading || isFetching}
        columns={columns}
        actions={actions}
        goto={
          <div className="flex items-center gap-2">
            <Icon
              icon="oui:ml-create-single-metric-job"
              className="text-white font-bold"
            />
            Add New Model
          </div>
        }
        gotoLink={"/create-model"}
        changePage={changePage}
        filter={filter}
        setFilter={setApiParam}
        data={tableData}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
      <DeleteSupport
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </div>
  );
};

export default index;
