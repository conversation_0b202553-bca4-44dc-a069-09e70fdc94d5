import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import InputFile from "@/components/ui/form/InputFile";
import InputSelect from "@/components/ui/form/InputSelect";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import {
  categoryInitialValues,
  createValidationSchema,
  isActiveOptions,
} from "@/formHandlers/CategoryCrud";
import {
  usePostApiMutation,
  useGetApiQuery,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import React from "react";
// import InputSelect from "@/components/ui/form/InputSelect";
import { useNavigate } from "react-router-dom";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";

const create = () => {
  const navigate = useNavigate();

  // Fetch all categories using API query
  const { data: categoriesData } = useGetApiQuery({ url: "categories" });

  // Filter only active categories
  const allCategories = categoriesData?.data.filter(
    (item) => item.is_active === true
  );

  // Convert categories to the required format for InputSelect
  const allCategoryOptions = allCategories?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  const [postApi, { isLoading, isError, error, isSuccess }] =
    usePostApiMutation();
  const headerSlotContent = (
    <Button
      onClick={() => navigate("/model-list")}
      className="btn text-center btn-outline-primary"
    >
      Model List
    </Button>
  );

  const handleSubmit = async (values, { resetForm }) => {
    const modifiedValues = { ...values };
    console.log(values);

    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        }
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const response = await postApi({
        end_point: "categories",
        body: formData,
      }).unwrap();
      // console.log("Submission successful:", response);
      toast.success("Model Create Successfully!");
      navigate("/model-list");
      resetForm();
    } catch (err) {
      // console.error("Submission failed:", err);
      toast.error("Model Create failed. Please try again.");
    }
  };

  return (
    <div>
      <Formik
        initialValues={categoryInitialValues}
        validationSchema={createValidationSchema()}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Create Model"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-1 md:grid-cols-3  gap-5">
                <InputField
                  label="Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter name"
                />
                <InputFile
                  label="image"
                  name="image"
                  type="file"
                  title="Upload your file"
                  accept="image/*"
                />
                <div className="">
                  <InputSelect
                    label="Category"
                    name="parent_id"
                    options={allCategoryOptions}
                    placeholder="Select Category"
                    required
                  />
                </div>
              </div>
              <div className="mt-4">
                <TextAreaField
                  label="Description"
                  name="description"
                  type="text"
                  placeholder="Enter description"
                />
              </div>

              <div className="w-full text-end">
                <Button
                  type="submit"
                  className="btn text-center btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default create;
