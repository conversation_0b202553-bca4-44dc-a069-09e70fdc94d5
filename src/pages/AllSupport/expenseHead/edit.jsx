import React from "react";
import Card from "@/components/ui/Card";
import {
  useGetApiWithIdQuery,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";
import { validationSchema } from "./formSetting";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";

const edit = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Fetch the Expense head data using the ID
  const { data: expenseHead, isLoading } = useGetApiWithIdQuery([
    "expense-heads",
    id,
  ]);

  const [updateApi] = useUpdateApiMutation();

  // Initial values populated dynamically with the fetched data
  const initialValues = {
    name: expenseHead?.name || "",
    is_active: expenseHead?.is_active ? true : false,
  };

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        }
      } else if (key === "is_active") {
        // Convert boolean to integer (1 or 0)
        formData.append(key, values.is_active ? 1 : 0);
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const data = {
        end_point: "expense-heads/" + id,
        body: formData,
      };

      const response = await updateApi(data).unwrap();
      resetForm();
      toast.success("Expense Head updated successfully!");
      navigate("/expense-head-list");
    } catch (err) {
      toast.error("Failed to update Expense Head. Please try again.");
    }
  };

  // Handle loading and form display
  if (isLoading) {
    return <p>Loading...</p>;
  }

  return (
    <div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values, setFieldValue }) => (
          <Form>
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-bold text-lg">
                        Edit{" "}
                        <span className="text-primary-500">{values.name}</span>
                      </span>
                    </div>
                    <div>
                      <Tooltip
                        content="Back to Expense Head List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() => navigate("/expense-head-list")}
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </Card>
              </div>
            </div>

            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-8 col-span-12 hidden md:block">
                <div className="grid gap-4">
                  <Card>
                    <>
                      <span className="font-bold text-slate-600 my-4">
                        Name:
                      </span>
                      <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                        <span className="flex items-center text-slate-700">
                          {values.name || "Untitled Expense Head Name"}
                        </span>
                      </div>
                    </>
                  </Card>

                  <Card>
                    <div className="flex justify-center items-center bg-slate-100 rounded-lg px-3 py-2">
                      <span className="mr-1 font-bold text-slate-600">
                        Status:
                      </span>
                      <Badge
                        className={
                          values.is_active
                            ? `text-success-500 font-bold text-base`
                            : `text-danger-500 font-bold text-base`
                        }
                      >
                        {values.is_active ? (
                          <>
                            Active
                            <Icon
                              icon="icon-park-solid:correct"
                              className="ml-2"
                            />
                          </>
                        ) : (
                          <>
                            <Icon icon="maki:cross" className="mr-2" />
                            Inactive
                          </>
                        )}
                      </Badge>
                    </div>
                  </Card>
                </div>
              </div>

              <div className="xl:col-span-4 col-span-12 space-y-4">
                <Card>
                  <div className="grid grid-cols-1 my-3">
                    <InputField
                      label="Expense Head Name"
                      name="name"
                      type="text"
                      placeholder="Enter Expense Head Name"
                      required
                    />
                  </div>

                  <div className="my-3">
                    <Switch
                      label="Is Active"
                      activeClass="bg-success-500"
                      name="is_active"
                      value={values.is_active}
                      onChange={() =>
                        setFieldValue("is_active", !values.is_active)
                      }
                    />
                  </div>
                </Card>
              </div>
            </div>

            {/* Submit and Cancel Buttons */}
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-end items-center gap-4">
                    <Button
                      type="button"
                      className="btn text-center btn-danger"
                      onClick={() => navigate("/expense-head-list")}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="btn text-center btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Updating..." : "Update"}
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default edit;
