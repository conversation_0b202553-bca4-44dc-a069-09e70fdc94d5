import React from "react";
import { useParams } from "react-router-dom";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import Tooltip from "@/components/ui/Tooltip";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import { useNavigate } from "react-router-dom";

const Details = () => {
  const navigate = useNavigate();
  const { id } = useParams();

  // Fetch data from API
  const { data: expenseHead, isLoading } = useGetApiQuery({
    url: `expense-heads/${id}`,
  });

  if (isLoading) return <p>Loading...</p>;

  return (
    <>
      <div className="grid gap-4 grid-cols-12 mt-5">
        <div className="xl:col-span-12 col-span-12 space-y-4">
          <Card>
            <div className="xl:col-span-12 col-span-12 space-y-4">
              <Card>
                <div className="flex justify-between items-center">
                  <div>
                    <span className="font-bold text-lg">
                      Expense Head Details of{" "}
                      <span className="text-primary-500">
                        {expenseHead.name}
                      </span>
                    </span>
                  </div>
                  <div className="flex gap-3">
                    <div>
                      <Tooltip
                        content="Back to Expense Head List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() => navigate("/expense-head-list")}
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                    <div>
                      <Tooltip
                        content={`Edit ${expenseHead.name} of Expense Head`}
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() =>
                            navigate(`/edit-expense-head/${expenseHead.id}`)
                          }
                        >
                          <Icon
                            icon="mynaui:edit-one"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </Card>
        </div>
      </div>
      <div className="grid grid-cols-12 gap-5 mb-5">
        <div className="lg:col-span-12 col-span-12">
          <Card>
            <div className="grid gap-4 md:grid-cols-3 grid-cols-1 mt-5">
              <Card className="col-span-2 shadow-base2">
                <span className="font-bold text-slate-600 my-4">
                  Task Title:
                </span>
                <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                  <span className="flex items-center text-slate-700">
                    {expenseHead?.name}
                  </span>
                </div>
              </Card>
              <Card>
                <div className="flex justify-center items-center bg-slate-100 rounded-lg px-3 py-2 mt-8">
                  <span className="mr-1 font-bold text-slate-600">Status:</span>
                  <Badge
                    className={
                      expenseHead.is_active
                        ? `text-success-500 font-bold text-base`
                        : `text-danger-500 font-bold text-base`
                    }
                  >
                    {expenseHead.is_active ? (
                      <>
                        Active
                        <Icon icon="icon-park-solid:correct" className="ml-2" />
                      </>
                    ) : (
                      <>
                        <Icon icon="maki:cross" className="mr-2" />
                        Inactive
                      </>
                    )}
                  </Badge>
                </div>
              </Card>
            </div>
          </Card>
        </div>
      </div>
    </>
  );
};

export default Details;
