import React from "react";
import Card from "@/components/ui/Card";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import <PERSON><PERSON><PERSON><PERSON>ield from "@/components/ui/form/TextAreaField";
import Button from "@/components/ui/Button";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import { initialValues, validationSchema } from "./formSetting";
import { toast } from "react-toastify";
import Switch from "@/components/ui/Switch";

const CreateSupport = () => {
  const navigate = useNavigate();
  const [postApi] = usePostApiMutation();

  // Form submission handler
  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();
    formData.append("name", values.name);
    formData.append("description", values.description);
    formData.append("is_active", values.is_active ? 1 : 0);

    try {
      const response = await postApi({
        end_point: "support-types",
        body: formData,
      }).unwrap();
      toast.success("Support Type Created Successfully!");
      navigate("/support-type");
      resetForm();
    } catch (err) {
      // toast.error("Submission failed Support Type. Please try again.");
    }
  };

  const headerSlotContent = (
    <Tooltip
      content="Back to Support Type List"
      placement="top"
      arrow
      animation="Interactive"
    >
      <div className="m-1" onClick={() => navigate("/support-type")}>
        <Icon
          icon="ion:arrow-back"
          className="w-6 h-6 text-gray-500 cursor-pointer hover:text-primary-500 m-1 hover:border-primary-500"
        />
      </div>
    </Tooltip>
  );

  return (
    <>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ setFieldValue, values, isSubmitting }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Add New Support Type"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-1 md:grid-cols-1 space-y-4">
                <InputField
                  label="Support Type Name"
                  name="name"
                  type="text"
                  required
                  placeholder="Enter Support Name"
                />
                {/* <div className="flex justify-start items-start my-auto mx-6 pt-6">
                  <Switch
                    label="Is Active"
                    activeClass="bg-success-500"
                    name="is_active"
                    value={values.is_active}
                    onChange={() =>
                      setFieldValue("is_active", !values.is_active)
                    }
                  />
                </div> */}
                <div className="col-span-3 ">
                  <TextAreaField
                    label="Description"
                    name="description"
                    type="text"
                    placeholder="Enter Description"
                  />
                </div>
              </div>

              {/* Submit and Cancel Buttons */}
              <div className="flex justify-end mt-5 mb-2 gap-3">
                <Button
                  type="button"
                  className="btn text-center btn-danger"
                  onClick={() => navigate("/support-type")}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="btn text-center btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default CreateSupport;
