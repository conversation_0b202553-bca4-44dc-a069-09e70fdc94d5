import React from "react";
import { useLocation } from "react-router-dom";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import { useNavigate } from "react-router-dom";
import Tooltip from "@/components/ui/Tooltip";

const DetailsSupport = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { support } = location.state || {};

  console.log(support);

  return (
    <>
      <div className="grid gap-4 grid-cols-12 mt-5">
        <div className="xl:col-span-12 col-span-12 space-y-4">
          <Card>
            <div className="xl:col-span-12 col-span-12 space-y-4">
              <Card>
                <div className="flex justify-between items-center">
                  <div>
                    <span className="font-bold text-lg">
                      Support Type{" "}
                      <span className="text-primary-500">{support.name}</span>{" "}
                      Details
                    </span>
                  </div>
                  <div className="flex gap-3">
                    <div>
                      <Tooltip
                        content="Back to Support Type List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() => navigate("/support-type")}
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                    <div>
                      <Tooltip
                        // content={`Edit ${support.name} Support Type`}
                        content={
                          <>
                            Edit of{" "}
                            <span className="font-bold">{support.name}</span>{" "}
                            Support Type
                          </>
                        }
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() =>
                            navigate(`/edit-support/${support.id}`)
                          }
                        >
                          <Icon
                            icon="mynaui:edit-one"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </Card>
        </div>
      </div>
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 mt-5">
        <Card>
         
            <span className="font-bold text-slate-600 my-4">Name:</span>
            <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
              <span className="flex items-center text-slate-700">
                {support.name}
              </span>
            </div>
          
        </Card>
        <Card>
          <div className="flex justify-center items-center bg-slate-100 rounded-lg px-3 py-2 mt-8">
            <span className="mr-1 font-bold text-slate-600">Status:</span>
            <Badge
              className={
                support.is_active
                  ? `text-success-500 font-bold text-base`
                  : `text-danger-500 font-bold text-base`
              }
            >
              {support.is_active ? (
                <>
                  Active
                  <Icon icon="icon-park-solid:correct" className="ml-2" />
                </>
              ) : (
                <>
                  <Icon icon="maki:cross" className="mr-2" />
                  Inactive
                </>
              )}
            </Badge>
          </div>
        </Card>
      </div>
      <div className="grid gap-4 grid-cols-1 mt-5">
        <Card>
          <>
            <span className="font-bold text-slate-600 my-4">Description:</span>
            <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
              <span className="flex items-center text-slate-700">
                {support.description}
              </span>
            </div>
          </>
        </Card>
      </div>
    </>
  );
};

export default DetailsSupport;
