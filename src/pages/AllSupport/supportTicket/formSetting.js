import * as yup from "yup";

// Initial values for form fields
export const initialValues = {
    name: "",
    description: "",
    task: "",
    employee_id: "",
    assign_date: "",
    // contact_info: "",
    // address: "",
    support_type_id: "",
    attachment: "",
    deadline: "",
    is_active: true,

};

// Validation schema for form fields
export const validationSchema = yup.object({
    name: yup
        .string()
        .max(255, "Task Title should not exceed 255 characters")
        .required("The Task Title is required"),
    support_type_id: yup
        .string()
        .required("Type is required"),
    assign_date: yup
        .string()
        .required("Assign date is required"),
    deadline: yup
        .string()
        .required("Deadline is required"),
    employee_id: yup.string().required("Assign To is required"),

});
