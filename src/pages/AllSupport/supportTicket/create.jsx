import React, { useState } from "react";
import Card from "@/components/ui/Card";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik, ErrorMessage } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import Button from "@/components/ui/Button";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import { initialValues, validationSchema } from "./formSetting";
import { toast } from "react-toastify";
import DateTimePicker from "@/components/ui/form/DateTimePicker";
import InputSelect from "@/components/ui/form/InputSelect";
import CreateSupportType from "./CreateOption/supportTypeCreate";
import InputFile from "@/components/ui/form/InputFile";

const CreateSupportTicket = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [showSupportTypeModal, setShowSupportTypeModal] = useState(false);
  const [supportTypedData, setSupportTypeData] = useState(null);
  const { data: userData } = useGetApiQuery({ url: "user-list" });
  const { data: supportTypeData } = useGetApiQuery({ url: "support-types" });
  const [postApi] = usePostApiMutation();

  const allUsersOptions = userData?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  const allSupportType = supportTypeData?.data?.filter(
    (item) => item?.is_active === 1
  );
  const allSupportTypeOptions = allSupportType?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();
    formData.append("name", values.name);
    formData.append("task", values.task);
    formData.append("employee_id", values.employee_id);
    formData.append("assign_date", values.assign_date);
    formData.append("support_type_id", values.support_type_id);
    formData.append("is_active", values.is_active ? 1 : 0);
    formData.append("deadline", values.deadline);
    formData.append("sale_id", id);

    try {
      await postApi({
        end_point: "supports",
        body: formData,
        method: "POST",
      }).unwrap();
      navigate(`/support-ticket/${id}`);
      toast.success("Support Ticket Created Successfully!");
      resetForm();
    } catch {
      toast.error("Support Ticket Created Failed. Please try again.");
    }
  };

  const headerSlotContent = (
    <Tooltip content="Back to Support Ticket List" placement="top" arrow>
      <div className="m-1" onClick={() => navigate(`/support-ticket/${id}`)}>
        <Icon
          icon="ion:arrow-back"
          className="w-6 h-6 text-gray-500 cursor-pointer hover:text-primary-500"
        />
      </div>
    </Tooltip>
  );

  return (
    <>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Add New Ticket"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="col-span-3">
                  <InputField
                    label="Task Title"
                    name="name"
                    type="text"
                    required
                    placeholder="Enter Task Title"
                  />
                </div>
                <div className="md:col-span-3 col-span-1">
                  <div className="grid md:grid-cols-2 grid-cols-1 gap-4">
                    <div className="flex items-center justify-between">
                      <div className="w-full">
                        <InputSelect
                          label="Type"
                          name="support_type_id"
                          options={allSupportTypeOptions}
                          placeholder="Select Type"
                          required
                        />
                        <ErrorMessage
                          name="support_type_id"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>
                      <Tooltip content="Add New Type" placement="top" arrow>
                        <div
                          className="ml-3 mt-8"
                          onClick={() => {
                            setSupportTypeData(null);
                            setShowSupportTypeModal(true);
                          }}
                        >
                          <Icon
                            icon="oui:ml-create-single-metric-job"
                            className="w-6 h-6 text-gray-400 cursor-pointer hover:text-primary-400"
                          />
                        </div>
                      </Tooltip>
                    </div>
                    <div>
                      <InputSelect
                        label="Assign To"
                        name="employee_id"
                        options={allUsersOptions}
                        placeholder="Select Assign To"
                        required
                      />
                      <ErrorMessage
                        name="employee_id"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>
                  </div>
                </div>
                <div className="col-span-3">
                  <div className="grid md:grid-cols-2 grid-cols-1  gap-4">
                    <DateTimePicker
                      name="assign_date"
                      label="Assign Date"
                      inputType="date"
                      required
                    />
                    <DateTimePicker
                      name="deadline"
                      label="Deadline"
                      inputType="date"
                      required
                    />
                    <InputFile
                      label="Attachment"
                      name="attachment"
                      type="file"
                      title="Upload Your Task Document"
                      accept="image/*"
                    />
                  </div>
                </div>
                <div className="col-span-3">
                  <TextAreaField
                    label="Task Details"
                    name="task"
                    placeholder="Enter Task Details"
                  />
                </div>
              </div>
              <div className="flex justify-end mt-5 gap-3">
                <Button
                  type="button"
                  className="btn btn-danger"
                  onClick={() => navigate(`/support-ticket/${id}`)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
      <CreateSupportType
        showSupportTypeModal={showSupportTypeModal}
        setShowSupportTypeModal={setShowSupportTypeModal}
        data={supportTypedData}
      />
    </>
  );
};

export default CreateSupportTicket;
