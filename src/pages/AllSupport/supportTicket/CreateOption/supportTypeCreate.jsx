import React from "react";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import { toast } from "react-toastify";
import Card from "@/components/ui/Card";
import InputField from "@/components/ui/form/InputField";
import <PERSON><PERSON>reaField from "@/components/ui/form/TextAreaField";
import { initialValues, validationSchema } from "../../supportType/formSetting";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate } from "react-router-dom";

const supportTypeCreate = ({
  showSupportTypeModal,
  setShowSupportTypeModal,
  data,
}) => {
  const navigate = useNavigate();
  const [postApi] = usePostApiMutation();

  // Form submission handler
  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();
    formData.append("name", values.name);
    formData.append("description", values.description);
    formData.append("is_active", values.is_active ? 1 : 0);

    try {
      const response = await postApi({
        end_point: "support-types",
        body: formData,
      }).unwrap();
      toast.success("Support Type Create Successfully!");
      setShowSupportTypeModal(false);
      resetForm();
    } catch (err) {
      setShowSupportTypeModal(false);
      // toast.error("Submission failed Support Type. Please try again.");
    }
  };

  return (
    <Modal
      activeModal={showSupportTypeModal}
      onClose={() => setShowSupportTypeModal(false)}
      title="Add New Support Type"
      className="max-w-5xl"
    >
      <div>
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, isSubmitting, setFieldValue }) => {
            return (
              <Form>
                <Card className="shadow-base2">
                  <div className="grid grid-cols-1 gap-4">
                    <InputField
                      label="Name"
                      name="name"
                      type="text"
                      required
                      placeholder="Enter Support Name"
                    />
                    <TextAreaField
                      label="Description"
                      name="description"
                      type="text"
                      placeholder="Enter description"
                    />
                  </div>

                  <div className="flex justify-end mt-5 mb-2 gap-3">
                    <Button
                      type="button"
                      className="btn text-center btn-danger"
                      onClick={() => {
                        setShowSupportTypeModal(false);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="btn text-center btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Submitting..." : "Submit"}
                    </Button>
                  </div>
                </Card>
              </Form>
            );
          }}
        </Formik>
      </div>
    </Modal>
  );
};

export default supportTypeCreate;
