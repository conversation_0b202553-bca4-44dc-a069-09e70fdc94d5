import InputField from "@/components/ui/form/InputField";
import InputSelect from "@/components/ui/form/InputSelect";
import Modal from "@/components/ui/Modal";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import Card from "@/components/ui/Card";
import TextAreaField from "@/components/ui/form/TextAreaField";
import Button from "@/components/ui/Button";
import * as yup from "yup";
import { toast } from "react-toastify";

const AdjustBalance = ({ activeModal, onClose, supportData }) => {
  console.log(supportData, 'Support Data');
  const { id } = useParams();
  const navigate = useNavigate();
  const [postApi] = usePostApiMutation();

  const initialValues = {
    description: "",
    is_active: true,
    payment_method: "",
    transaction_id: "",
    paid_amount: supportData?.remaining_expense > 0 ? supportData?.remaining_expense : supportData?.remaining_recollect,
  };

  const validationSchema = yup.object({
    payment_method: yup
      .string()
      .required("Payment method is required"),
  });


  const unitPayment = [
    { value: "Cash", label: "Cash" },
    { value: "BankTransfer", label: "Bank Transfer" },
    { value: "Cheque", label: "Cheque" },
    { value: "OnlinePayment", label: "Online Payment" },
    { value: "Bkash", label: "Bkash" },
    { value: "Ucash", label: "Ucash" },
    { value: "Nagad", label: "Nagad" },
    { value: "Rocket", label: "Rocket" },
    { value: "MobileBanking", label: "Mobile Banking" },
    { value: "AgentBanking", label: "Agent Banking" },
    { value: "Others", label: "Others" },
  ];

  // const paymentType = [
  //   { value: "postpaid", label: "Due" },
  //   { value: "recollect", label: "Recollect" },
  // ];

  // Form submission handler
  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    formData.append("description", values.description);
    formData.append("is_active", 1);
    formData.append("support_id", id);
    formData.append("payment_method", values.payment_method);
    formData.append("payment_type", supportData?.remaining_expense > 0 ? "postpaid" : "recollect");
    formData.append("paid_amount", values.paid_amount);
    formData.append("transaction_id", values.transaction_id);

    try {
      const response = await postApi({
        end_point: "expense-payments",
        body: formData,
      }).unwrap();
      toast.success("Support submitted successfully!");
      onClose();
      resetForm();
    } catch (err) {
      console.log("Submission failed. Please try again.");
    }
  };


  return (
    <Modal
      activeModal={activeModal}
      onClose={onClose}
      title="Adjust Balance"
      className="max-w-xl"
    >
      <>
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ setFieldValue, values, isSubmitting, touched, errors }) => (
            <Form>
              {console.log(values)}
              <Card
                //   title="Adjust Balance"
                className="w-full"
                titleClass="text-lg font-bold text-gray-800"
              >
                <div className="grid grid-cols-2 md:grid-cols-2 gap-4">
                  {/* <div>
                    <InputSelect
                      label="Payment Type"
                      id="payment_type"
                      name="payment_type"
                      value={values.payment_type}
                      onChange={(e) =>
                        setFieldValue("payment_type", e.target.value)
                      }
                      options={paymentType}
                      placeholder="Select Payment Type"
                      required
                    />
                    {touched.payment_type && errors.payment_type && (
                      <div className="text-red-500 text-xs mt-2">{errors.payment_type}</div>
                    )}
                  </div> */}
                  <div>
                    <InputSelect
                      label="Payment Method"
                      id="payment_method"
                      name="payment_method"
                      value={values.payment_method}
                      onChange={(e) =>
                        setFieldValue("payment_method", e.target.value)
                      }
                      options={unitPayment}
                      placeholder="Payment Method"
                      required
                    />
                    {touched.payment_method && errors.payment_method && (
                      <div className="text-red-500 text-xs mt-2">{errors.payment_method}</div>
                    )}
                  </div>
                  <InputField
                    label="Paid Amount"
                    name="paid_amount"
                    type="text"
                    placeholder="Enter Paid Amount"
                    disabled
                  />
                  <div className="col-span-2">
                    <InputField
                      label="Transaction Id"
                      name="transaction_id"
                      type="text"
                      placeholder="Enter Transaction Id"
                    />
                  </div>

                  <div className="col-span-2">
                    <TextAreaField
                      label="Description"
                      name="description"
                      type="text"
                      placeholder="Enter Description"
                    />
                  </div>
                </div>

                {/* Submit and Cancel Buttons */}
                <div className="flex justify-end mt-5 mb-2 gap-3">
                  <Button
                    type="button"
                    className="btn text-center btn-danger"
                    onClick={() => navigate(`/support-account-list/${id}`)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="btn text-center btn-primary"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Submitting..." : "Submit"}
                  </Button>
                </div>
              </Card>
            </Form>
          )}
        </Formik>
      </>
    </Modal>
  );
};

export default AdjustBalance;
