import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import DeleteSupportTicket from "./deleteTicket";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import { format, isBefore } from "date-fns";
import {
  getStatusColor,
  getStatusText,
  getStatusIcon,
} from "./OtherFile/DetailsStatus";
import ForwardSupportTicket from "./OtherFile/ForwardSupport";
import { formattedDate } from "@/constant/data";

const index = () => {
  const { id } = useParams();
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");

  // Other states and queries
  const [showForwardModal, setShowForwardModal] = useState(false);
  const [forwardData, setForwardData] = useState(null);
  const url = id ? `supports?sale_id=${id}` : "supports";
  const { data, isLoading, isFetching } = useGetApiQuery({ url: url, params: apiParam });

  const navigate = useNavigate();

  // Delete Modal State
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  const changePage = (value) => setApiParam(value);

  const columns = [
    { label: "Task Title", field: "name" },
    { label: "Assign Name", field: "assignName" },
    { label: "Support Type", field: "supportType" },
    { label: "Assign Date", field: "assign_date" },
    { label: "Deadline", field: "deadline" },
    { label: "Status", field: "is_active" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "View",
      icon: "lets-icons:view-alt",
      onClick: (val) => {
        navigate(`/details-support-ticket/${data?.data[val]?.id}`);
      },
    },
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(`/edit-support-ticket/${data?.data[val]?.id}`);
      },
    },
    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(data.data[val]);
        setShowDeleteModal(true);
      },
    },

    {
      name: "Account",
      icon: "fluent:person-accounts-24-filled",
      onClick: (val) => {
        const item = data?.data[val];
        navigate(`/support-account-list/${item.id}`);
      },
    },
  ];

  const tableData = data?.data?.map((item) => {
    const deadlineDate = new Date(item.deadline);
    const isOverDate =
      item.status !== "completed" && isBefore(deadlineDate, new Date());
    return {
      id: item.id,
      name: (
        <Tooltip
          content={`Details of ${item.name} Support Ticket`}
          placement="top"
          arrow
          animation="Interactive"
        >
          <button
            onClick={() =>
              navigate(`/details-support-ticket/${item.id}`, {
                state: { support: item },
              })
            }
            className="hover:underline hover:text-primary-600 text-primary-400 font-bold"
          >
            {item.name.length > 30 ? `${item.name.slice(0, 30)}...` : item.name}
          </button>
        </Tooltip>
      ),
      assignName: (
        <span className="bg-primary-500 p-1 rounded-md text-slate-50 font-semibold">
          {item.employee?.name}
        </span>
      ),
      supportType: (
        <span className="text-slate-600 font-semibold">
          {item.support_type?.name}
        </span>
      ),

      assign_date: formattedDate(item.assign_date),
      deadline: (
        <span
          className={`${isOverDate
            ? "font-bold text-xs shadow-xl p-2 bg-slate-50 text-danger-600 rounded-md sm:text-[10px]"
            : ""
            }`}
        >
          {formattedDate(deadlineDate)}
        </span>
      ),

      is_active: (
        <Badge
          className={`font-bold text-base cursor-pointer ${getStatusColor(
            item.status
          )}`}
          onClick={() => {
            if (item.status === "forwarded") {
              setForwardData(item);
              setShowForwardModal(true);
            }
          }}
        >
          <span className="flex items-center text-sm bg-success-100 p-1 rounded-lg ">
            {getStatusIcon(item.status)}
            {getStatusText(item.status)}
            {item.status === "forwarded" && " - Re Assign"}
          </span>
        </Badge>
      ),
      rowColor: isOverDate ? "bg-[#fde7e7] rounded-lg" : "",
    };
  });

  return (
    <div>
      <BasicTablePage
        title="Support Ticket"
        loading={isLoading || isFetching}
        columns={columns}
        actions={actions}
        {...(id && {
          goto: (
            <div
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => navigate(`/create-support-ticket/${id}`)}
            >
              <Icon
                icon="oui:ml-create-single-metric-job"
                className="text-white font-bold"
              />
              Add New Ticket
            </div>
          ),
          gotoLink: `/create-support-ticket/${id}`,
        })}
        BackButton={
          <div className="flex justify-center items-center">
            <Tooltip
              content="Back to Sales List"
              placement="top"
              arrow
              animation="Interactive"
            >
              <div
                className="border border-primary-300 hover:border-primary-500 p-2 text-primary-300 hover:text-primary-500 rounded-md"
                onClick={() => navigate(`/sales-list`)}
              >
                <Icon icon="heroicons-outline:arrow-left" />
              </div>
            </Tooltip>
          </div>
        }
        changePage={changePage}
        filter={filter}
        setFilter={setApiParam}
        data={tableData}
        currentPage={data?.current_page}
        totalPages={Math.ceil(
          data?.total / data?.per_page
        )}
      />
      <DeleteSupportTicket
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
      <ForwardSupportTicket
        showForwardModal={showForwardModal}
        setShowForwardModal={setShowForwardModal}
        forwardData={forwardData}
      />
    </div>
  );
};

export default index;
