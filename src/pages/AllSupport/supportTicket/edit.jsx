import React from "react";
import Card from "@/components/ui/Card";
import {
  useGetApiQuery,
  useGetApiWithIdQuery,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import TextAreaField from "@/components/ui/form/TextAreaField";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";
import { validationSchema } from "./formSetting";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import InputSelect from "@/components/ui/form/InputSelect";
import MultiSelectComponent from "@/components/ui/form/MultiselectComponent";
import DateTimePicker from "@/components/ui/form/DateTimePicker";
import Badge from "@/components/ui/Badge";
import { format } from "date-fns";
import InputFile from "@/components/ui/form/InputFile";

const EditSupport = () => {
  const { id } = useParams();
  // console.log(ticketId);
  const navigate = useNavigate();

  // Fetch all Support Types
  const { data: supportTypeData } = useGetApiQuery({ url: "support-types" });
  const allSupportType = supportTypeData?.data?.filter(
    (item) => item?.is_active === 1
  );
  const allSupportTypeOptions = allSupportType?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  // Fetch all Users using API query
  const { data: userData } = useGetApiQuery({ url: "user-list" });
  // Convert users to the required format for MultiSelectComponent
  const allUsersOptions = userData?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  // Fetch the support data using the ID
  const { data: support, isLoading } = useGetApiWithIdQuery(["supports", id]);

  const [updateApi] = useUpdateApiMutation();

  const initialValues = {
    name: support?.name || "",
    task: support?.task || "",
    attachment: support?.attachment || "",
    deadline: support?.deadline || "",
    assign_date: support?.assign_date || "",
    is_active: support?.is_active ? true : false,
    support_type_id: support?.support_type_id || "",
    sale_id: support?.sale_id || "",
    employee_id: support?.employee_id || "",
  };

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();
    formData.append("name", values.name);
    formData.append("task", values.task);
    formData.append("attachment", values.attachment);
    formData.append("deadline", values.deadline);
    formData.append("assign_date", values.assign_date);
    formData.append("is_active", values.is_active ? 1 : 0);
    formData.append("support_type_id", values.support_type_id);
    formData.append("employee_id", values.employee_id);

    try {
      const data = {
        end_point: `supports/${id}`,
        body: formData,
      };

      const response = await updateApi(data).unwrap();

      // navigate(`/support-list`);
      toast.success("Support Ticket Updated Successfully!");
      resetForm();
      navigate(-1);
    } catch (err) {
      toast.error("Failed to Update Support Ticket. Please try again.");
    }
  };

  if (isLoading) {
    return <p>Loading...</p>;
  }

  return (
    <div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values, setFieldValue }) => (
          <Form>
            {/* Form fields */}
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-bold text-lg">
                        Edit of{" "}
                        <span className="text-primary-500">{values.name}</span>
                      </span>
                    </div>
                    <div>
                      <Tooltip
                        content="Back to Support Ticket List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() =>
                            // navigate(`/support-ticket/${ticketId}`)
                            navigate(-1)
                          }
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-8 col-span-12">
                <div className="hidden md:block">
                  <div className="grid gap-4 ">
                    <Card>
                      <>
                        <span className="font-bold text-slate-600 my-4">
                          Task Title:
                        </span>
                        <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                          <span className="flex items-center text-slate-700">
                            {values.name || "Untitled Support Task Title"}
                          </span>
                        </div>
                      </>
                    </Card>
                    <div className="grid grid-cols-2 gap-3">
                      <Card>
                        <>
                          <span className="font-bold text-slate-600 my-4">
                            Support Type
                          </span>
                          <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                            <span className="flex items-center text-slate-700">
                              {allSupportTypeOptions.find(
                                (option) =>
                                  option.value === values.support_type_id
                              )?.label || "Untitled Support Type Name"}
                            </span>
                          </div>
                        </>
                      </Card>
                      <Card>
                        <>
                          <span className="font-bold text-slate-600 my-4">
                            Assign To
                          </span>
                          <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                            <span className="flex items-center text-slate-700">
                              <span className="bg-primary-500 px-2 rounded-lg text-slate-50 font-semibold">
                                {allUsersOptions.find(
                                  (option) =>
                                    option.value === values.employee_id
                                )?.label || "Not Assigned"}
                              </span>
                            </span>
                          </div>
                        </>
                      </Card>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <Card>
                        <>
                          <span className="font-bold text-slate-600 my-4">
                            Assign Date
                          </span>
                          <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                            <span className="flex items-center text-slate-700">
                              {values.assign_date
                                ? format(
                                    new Date(values.assign_date),
                                    "dd-MMM,yyyy"
                                  )
                                : "Date not available"}
                            </span>
                          </div>
                        </>
                      </Card>
                      <Card>
                        <>
                          <span className="font-bold text-slate-600 my-4">
                            Dateline
                          </span>
                          <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                            <span className="flex items-center text-slate-700">
                              {values.deadline
                                ? format(
                                    new Date(values.deadline),
                                    "dd-MMM,yyyy"
                                  )
                                : "Date not available"}
                            </span>
                          </div>
                        </>
                      </Card>
                    </div>

                    <Card>
                      <>
                        <span className="font-bold text-slate-600 my-4">
                          Task Details
                        </span>
                        <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                          <span className="flex items-center text-slate-700">
                            {values.task}
                          </span>
                        </div>
                      </>
                    </Card>

                    <Card>
                      <div className="flex justify-center items-center bg-slate-100 rounded-lg px-3 py-2">
                        <span className="mr-1 font-bold text-slate-600">
                          Status:
                        </span>
                        <Badge
                          className={
                            values.is_active
                              ? `text-success-500 font-bold text-base`
                              : `text-danger-500 font-bold text-base`
                          }
                        >
                          {values.is_active ? (
                            <>
                              Active
                              <Icon
                                icon="icon-park-solid:correct"
                                className="ml-2"
                              />
                            </>
                          ) : (
                            <>
                              <Icon icon="maki:cross" className="mr-2" />
                              Inactive
                            </>
                          )}
                        </Badge>
                      </div>
                    </Card>
                  </div>
                </div>
              </div>

              <div className="xl:col-span-4 col-span-12 space-y-4">
                <Card>
                  <div className="grid grid-cols-1 my-3 gap-3">
                    <InputField
                      label="Task Title"
                      name="name"
                      type="text"
                      placeholder="Enter Support Name"
                      required
                    />

                    <InputSelect
                      label="Support Type"
                      className="w-full"
                      name="support_type_id"
                      options={allSupportTypeOptions}
                      placeholder="Select Support Type"
                      value={values.support_type_id}
                      onChange={(value) =>
                        setFieldValue("support_type_id", value)
                      }
                      required
                    />

                    <InputSelect
                      label="Assign To"
                      name="employee_id"
                      options={allUsersOptions}
                      placeholder="Select Assign To"
                      value={values.employee_id}
                      onChange={(value) => setFieldValue("employee_id", value)}
                      required
                    />

                    <DateTimePicker
                      name="assign_date"
                      label="Assign Date"
                      inputType={"date"}
                      required
                    />
                    <DateTimePicker
                      name="deadline"
                      label="Deadline"
                      inputType={"date"}
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 my-3">
                    <TextAreaField
                      label="Task Details"
                      name="task"
                      type="text"
                      // required
                      placeholder="Enter Task Details"
                    />
                  </div>
                  <div className="grid grid-cols-1 my-3">
                    <InputFile
                      label="Attachment"
                      name="attachment"
                      type="file"
                      title="Upload Your Task Document"
                      accept="image/*"
                    />
                  </div>

                  <div className="my-3">
                    <Switch
                      label="Is Active"
                      activeClass="bg-success-500"
                      name="is_active"
                      value={values.is_active}
                      onChange={() =>
                        setFieldValue("is_active", !values.is_active)
                      }
                    />
                  </div>
                </Card>
              </div>
            </div>

            {/* Submit and Cancel Buttons */}
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-end items-center gap-4">
                    <Button
                      type="button"
                      className="btn text-center btn-danger"
                      onClick={() => navigate(`/support-ticket/${ticketId}`)}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="btn text-center btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Updating..." : "Update"}
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditSupport;
