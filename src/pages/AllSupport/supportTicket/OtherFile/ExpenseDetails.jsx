import React from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { format } from "date-fns";
import Icon from "@/components/ui/Icon";
import NoImage from "../../../../assets/CRM/NoImage.png";

const ExpenseDetails = ({
  showExpenseModal,
  setShowExpenseModal,
  expenseData,
}) => {
  return (
    <Modal
      activeModal={showExpenseModal}
      onClose={() => setShowExpenseModal(false)}
      title="Expense Details"
      className="max-w-7xl h-auto"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowExpenseModal(false)}
        />
      }
    >
      <div>
        {expenseData && expenseData.length > 0 ? (
          expenseData.map((expense) => (
            <div key={expense.id} className="my-4">
              <div className="grid grid-cols-1 gap-3 my-4 border-2 border-red-400 rounded-lg p-1 shadow-md">
                <div className="mx-2 flex justify-between">
                  <div>
                    <span className="flex gap-1 font-bold justify-center text-slate-600 ">
                      <span className="justify-center my-auto text-primary-500 font-bold">
                        <Icon icon="uiw:date" />
                      </span>
                      {format(new Date(expense.created_at), "d-MMM,yyyy")}
                    </span>
                  </div>
                  <div>
                    <span className="flex gap-1 font-bold justify-center text-slate-600 ">
                      <span className="justify-center my-auto text-primary-500 font-bold">
                        <Icon icon="lets-icons:time" />
                      </span>
                      {format(new Date(expense.created_at), "hh:mm a")}
                    </span>
                  </div>
                </div>
              </div>
              <Card className="my-2">
                <>
                  <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2 w-full h-96">
                    {expense.attachment ? (
                      expense.attachment.endsWith(".pdf") ? (
                        <iframe
                          src={`${import.meta.env.VITE_MEDIA_URL}/${
                            expense.attachment
                          }`}
                          className="w-full h-80 rounded-lg border"
                          title="PDF Attachment"
                        />
                      ) : (
                        <img
                          src={
                            expense.attachment
                              ? `${import.meta.env.VITE_MEDIA_URL}/${
                                  expense.attachment
                                }`
                              : NoImage
                          }
                          alt="Image Attachment"
                          className="w-full h-80 object-cover rounded-lg border"
                        />
                      )
                    ) : (
                      <span className="text-slate-500">
                        No attachment available
                      </span>
                    )}
                  </div>
                </>
              </Card>
              <Card>
                <>
                  <span className="font-bold text-slate-600 my-4">
                    Description:
                  </span>
                  <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                    <span className="flex items-center text-slate-700">
                      {expense.description}
                    </span>
                  </div>
                </>
              </Card>
              <div className="grid grid-cols-3 gap-3 my-4">
                <Card>
                  <>
                    <span className="font-bold text-slate-600 my-4">
                      Advance Amount:
                    </span>
                    <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                      <span className="flex items-center text-slate-700">
                        {expense.advance_amount}
                      </span>
                    </div>
                  </>
                </Card>
                <Card>
                  <>
                    <span className="font-bold text-slate-600 my-4">
                      Expense Amount:
                    </span>
                    <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                      <span className="flex items-center text-slate-700">
                        {expense.expense_amount}
                      </span>
                    </div>
                  </>
                </Card>
                <Card>
                  <>
                    <span className="font-bold text-slate-600 my-4">
                      Extra Expense:
                    </span>
                    <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                      <span className="flex items-center text-red-500 font-bold">
                        - {""}
                        {expense.extra_expense_amount || 0}
                      </span>
                    </div>
                  </>
                </Card>
              </div>
              <div className="grid grid-cols-3 gap-3 my-4">
                <Card>
                  <>
                    <span className="font-bold text-slate-600 my-4">
                      Status:
                    </span>
                    <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                      <span className="flex items-center text-slate-700 uppercase">
                        <span className="bg-success-500 px-1 rounded-lg">
                          {expense.status}
                        </span>
                      </span>
                    </div>
                  </>
                </Card>
                <Card>
                  <>
                    <span className="font-bold text-slate-600 my-4">
                      Support Name
                    </span>
                    <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                      <span className="flex items-center text-primary-500 font-bold">
                        {expense?.support?.name}
                      </span>
                    </div>
                  </>
                </Card>
                <Card>
                  <>
                    <span className="font-bold text-slate-600 my-4">
                      Refund Amount:
                    </span>
                    <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                      <span className="flex items-center font-bold text-success-500">
                        {expense.refund_amount || 0}
                      </span>
                    </div>
                  </>
                </Card>
              </div>
              {/* <hr className="my-4 border-t border-slate-500" /> */}
            </div>
          ))
        ) : (
          <p>No Expense Data Available.</p>
        )}
      </div>
    </Modal>
  );
};

export default ExpenseDetails;
