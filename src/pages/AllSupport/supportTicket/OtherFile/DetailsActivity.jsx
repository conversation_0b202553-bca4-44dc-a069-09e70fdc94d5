import React from "react";

const DetailsActivity = ({ supportHistory }) => {
  return (
    <ul className="relative ltr:pl-2 rtl:pr-2">
      {supportHistory.map((item, i) => (
        <li
          key={item.id}
          className={`
            ${
              // item.trigger_type === "complete"
              //   ? "before:opacity-100"
              //   : "before:opacity-50"
              // ---------------------------

              item.trigger_type === "complete"
                ? "before:opacity-100 before:bg-green-500"
                : item.trigger_type === "assign"
                ? "before:opacity-75 before:bg-blue-500"
                : item.trigger_type === "accept"
                ? "before:opacity-90 before:bg-yellow-500"
                : item.trigger_type === "re-assign"
                ? "before:opacity-85 before:bg-purple-500"
                : item.trigger_type === "forward"
                ? "before:opacity-80 before:bg-orange-500"
                : "before:opacity-50 before:bg-red-500"
            }
            ltr:border-l-2 rtl:border-r-2 border-slate-100 dark:border-slate-700 pb-4
            last:border-none ltr:pl-[22px] rtl:pr-[22px] relative before:absolute ltr:before:left-[-8px]
            rtl:before:-right-2 before:top-[0px] before:rounded-full before:w-4 before:h-4
            before:bg-slate-900 dark:before:bg-slate-600 before:leading-[2px]
            before:content-[url('@/assets/images/all-img/ck.svg')]
          `}
        >
          <div className="p-[10px] relative top-[-20px]">
            <h6 className="text-sm font-medium dark:text-slate-400-900 mb-1 text-slate-600">
              {item.trigger_type.charAt(0).toUpperCase() +
                item.trigger_type.slice(1)}
            </h6>
            <p className="text-xs capitalize dark:text-slate-400 my-1">
              {new Intl.DateTimeFormat("en-US", {
                day: "2-digit",
                month: "short",
                year: "numeric",
              }).format(new Date(item.created_at))}
            </p>
            <p className="text-xs capitalize dark:text-slate-400 my-1">
              <span className="text-primary-500">
                Assign: {item.employee?.name || "Unknown Employee"}
              </span>
            </p>
            <p className="text-xs capitalize dark:text-slate-400 my-1">
              {item.description}
            </p>
          </div>
        </li>
      ))}
    </ul>
  );
};

export default DetailsActivity;
