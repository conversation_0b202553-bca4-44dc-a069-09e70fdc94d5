import Icon from "@/components/ui/Icon";

const statusMapping = {
  pending: {
    color: "text-yellow-500",
    text: "Pending",
    icon: "mdi:clock-outline",
  },
  forwarded: {
    color: "text-blue-500",
    text: "Forwarded",
    icon: "line-md:loading-twotone-loop",
  },
  on_going: {
    color: "text-purple-500",
    text: "Ongoing",
    icon: "mdi:progress-clock",
  },
  completed: {
    color: "text-success-500",
    text: "Completed",
    icon: "mdi:check-bold",
  },
  cancelled: {
    color: "text-danger-500",
    text: "Cancelled",
    icon: "mdi:cancel",
  },
  default: {
    color: "text-slate-700",
    text: "N/A",
    icon: "mdi:help-circle-outline",
  },
};

export const getStatusColor = (status) =>
  statusMapping[status]?.color || statusMapping.default.color;

export const getStatusText = (status) =>
  statusMapping[status]?.text || statusMapping.default.text;

export const getStatusIcon = (status) => {
  const iconName = statusMapping[status]?.icon || statusMapping.default.icon;
  return <Icon icon={iconName} className="mr-2" />;
};
