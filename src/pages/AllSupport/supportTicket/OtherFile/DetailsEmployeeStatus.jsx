import Icon from "@/components/ui/Icon";

const employeeStatusMapping = {
  pending: {
    color: "text-yellow-500",
    text: "Pending",
    icon: "mdi:clock-outline",
  },
  forwarded: {
    color: "text-blue-500",
    text: "Forwarded",
    icon: "mdi:arrow-right",
  },
  accepted: {
    color: "text-green-500",
    text: "Accepted",
    icon: "mdi:check-circle-outline",
  },
  completed: {
    color: "text-success-500",
    text: "Completed",
    icon: "mdi:check-bold",
  },
  rejected: {
    color: "text-danger-500",
    text: "Rejected",
    icon: "mdi:close-circle-outline",
  },
  cancelled: { color: "text-gray-500", text: "Cancelled", icon: "mdi:cancel" },
  default: {
    color: "text-slate-700",
    text: "N/A",
    icon: "mdi:help-circle-outline",
  },
};

export const getEmployeeStatusColor = (status) =>
  employeeStatusMapping[status]?.color || employeeStatusMapping.default.color;

export const getEmployeeStatusText = (status) =>
  employeeStatusMapping[status]?.text || employeeStatusMapping.default.text;

export const getEmployeeStatusIcon = (status) => {
  const iconName =
    employeeStatusMapping[status]?.icon || employeeStatusMapping.default.icon;
  return <Icon icon={iconName} className="mr-2" />;
};
