import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/apihandler/commonSlice";
import React from "react";
import { toast } from "react-toastify";
import InputSelect from "@/components/ui/form/InputSelect";
import { Formik, Form, ErrorMessage } from "formik";
import * as Yup from "yup";

const ForwardSupport = ({
  showForwardModal,
  setShowForwardModal,
  forwardData,
}) => {
  // Fetch all Users using API query
  const { data: userData } = useGetApiQuery({ url: "user-list" });

  // Find the employee name by employee_id from forwardData
  const employeeName =
    userData?.find((user) => user.id === forwardData?.employee_id)?.name ||
    "N/A";

  // Filter out the current assigned employee from the dropdown options
  const allUsersOptions = userData
    ?.filter((item) => item.id !== forwardData?.employee_id)
    .map((item) => ({
      label: item.name,
      value: item.id,
    }));

  const [postApi, { isLoading }] = usePostApiMutation();

  // Validation
  const initialValues = {
    employee_id: "",
  };

  const validationSchema = Yup.object({
    employee_id: Yup.string().required(
      "Please select an employee to Re-Assign."
    ),
  });

  const onSubmit = async (values) => {
    try {
      const response = await postApi({
        end_point: `task-forward/${forwardData?.id}`,
        body: { employee_id: values.employee_id },
      });

      toast.success(`Support "${forwardData?.name}" forwarded successfully!`);
      setShowForwardModal(false);
    } catch (err) {
      toast.error(
        `Failed to forward "${forwardData?.name}". Please try again.`
      );
    }
  };

  return (
    <Modal
      activeModal={showForwardModal}
      onClose={() => setShowForwardModal(false)}
      title="New Re-Assign"
      className="max-w-xl h-[700px]"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => setShowForwardModal(false)}
        />
      }
    >
      <div className="border bg-slate-100 border-slate-400 rounded-lg my-5">
        <h6 className="text-base font-bold p-2">
          Support Type:{" "}
          <span className="bg-primary-200 p-1 rounded-lg text-primary-500">
            {forwardData?.support_type?.name}
          </span>
        </h6>
      </div>
      <div className="border bg-slate-100 border-slate-400 rounded-lg my-5">
        <h6 className="text-base font-bold p-2">
          Previous Assign to:{" "}
          <span className="bg-primary-200 p-1 rounded-lg text-primary-500">
            {employeeName}
          </span>
        </h6>
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({ setFieldValue, values }) => (
          <Form>
            <div>
              <InputSelect
                label="Re-Assign To"
                name="employee_id"
                options={allUsersOptions}
                placeholder="Select Assign To"
                onChange={(option) =>
                  setFieldValue("employee_id", option.value)
                }
                value={values.employee_id}
                required
              />
              <ErrorMessage
                name="employee_id"
                component="div"
                className="text-red-500 text-sm mt-1"
              />
            </div>

            <div className="ltr:text-right rtl:text-left mt-5 gap-4">
              <Button
                type="button"
                // className="btn text-center btn-primary mr-4"
                className="btn text-center btn-danger mr-4"
                onClick={() => setShowForwardModal(false)}
              >
                Cancel
              </Button>
              <Button
                isLoading={isLoading}
                type="submit"
                className="btn text-center btn-primary"
              >
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default ForwardSupport;
