import * as yup from "yup";

// Initial values for form fields
export const initialValues = {
    description: "",
    is_active: true,
    payment_method: "",
    payment_type: "",
    paid_amount: "",
};

// Validation schema for form fields
export const validationSchema = yup.object({
    payment_method: yup
        .string()
        .required("Payment method is required"),

    payment_type: yup
        .string()
        .required("Payment type is required"),

    paid_amount: yup
        .number()
        .required("Paid amount is required")
        .positive("Paid amount must be a positive number")
        .typeError("Paid amount must be a valid number"),
});
