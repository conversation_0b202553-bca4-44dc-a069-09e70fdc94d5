import React, { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import Tooltip from "@/components/ui/Tooltip";
import {
  useGetApiQuery,
  usePostApiMutation,
} from "@/store/api/apihandler/commonSlice";
import DetailsActivity from "./OtherFile/DetailsActivity";
import Button from "@/components/ui/Button";
import NoImage from "../../../assets/CRM/NoImage.png";

import {
  getEmployeeStatusColor,
  getEmployeeStatusText,
  getEmployeeStatusIcon,
} from "./OtherFile/DetailsEmployeeStatus";
import {
  getStatusColor,
  getStatusText,
  getStatusIcon,
} from "./OtherFile/DetailsStatus";
import AdjustBalance from "./adjustBalance";
import { toast } from "react-toastify";
import ConfirmationModal from "@/components/ui/form/ConfirmationModal";

const Details = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [postApi] = usePostApiMutation();

  const [modal, setModal] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [selectedId, setSelectedId] = useState(null);

  const handleConfirmModalOpen = (id) => {
    setSelectedId(id);
    setIsConfirmModalOpen(true);
  };
  const handleConfirmModalClose = () => {
    setSelectedId(null);
    setIsConfirmModalOpen(false);
  };

  // Fetch data from API
  const { data: supportData, isLoading } = useGetApiQuery({
    url: `supports/${id}`,
  });
  const handleModal = () => {
    setModal(true);
  };
  const handleCloseModal = () => {
    setModal(false);
  };

  const assignedUserNames = supportData?.employee;
  const ClientData = supportData?.sale?.client;
  const SaleData = supportData?.sale;
  const ExpenseData = supportData?.support_expenses;
  const HistoryData = supportData?.support_history || [];
  console.log(assignedUserNames);
  const getStatusStyles = (triggerType) => {
    switch (triggerType) {
      case "assign":
        return {
          bgColor: "bg-orange-100",
          dotColor: "bg-orange-500",
          textColor: "text-orange-500",
          cardBgColor: "bg-orange-50",
        };
      case "accept":
        return {
          bgColor: "bg-blue-100",
          dotColor: "bg-blue-600",
          textColor: "text-blue-600",
          cardBgColor: "bg-blue-50",
        };
      case "complete":
        return {
          bgColor: "bg-green-100",
          dotColor: "bg-green-500",
          textColor: "text-green-500",
          cardBgColor: "bg-green-50",
        };
      case "re-assign":
        return {
          bgColor: "bg-yellow-100",
          dotColor: "bg-yellow-500",
          textColor: "text-yellow-500",
          cardBgColor: "bg-yellow-50",
        };
      case "forward":
        return {
          bgColor: "bg-purple-100",
          dotColor: "bg-purple-500",
          textColor: "text-purple-500",
          cardBgColor: "bg-purple-50",
        };
      case "cancel":
        return {
          bgColor: "bg-red-100",
          dotColor: "bg-red-500",
          textColor: "text-red-500",
          cardBgColor: "bg-red-50",
        };
      default:
        return {
          bgColor: "bg-blue-100",
          dotColor: "bg-blue-600",
          textColor: "text-blue-600",
          cardBgColor: "bg-white border border-gray-200",
        };
    }
  };

  // console.log(supportData?.status);

  if (isLoading) return <p>Loading...</p>;
  // Function to format date to "dd-MM-yyyy - dayName"
  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const options = { weekday: "long" };
    const dayName = new Intl.DateTimeFormat("en-US", options).format(date);
    const formattedDate = `${String(date.getDate()).padStart(2, "0")}-${String(
      date.getMonth() + 1
    ).padStart(2, "0")}-${date.getFullYear()} - ${dayName}`;
    return formattedDate;
  };

  console.log(supportData);

  const handleCloseExpense = async (id) => {
    try {
      const response = await postApi({
        end_point: `expense-close/${id}`,
      }).unwrap();
      toast.success("Support submitted successfully!");
    } catch (err) {
      console.log(err);
    }
  };

  const restrictedStatuses = ["pending", "forwarded"];

  return (
    <>
      <div className="grid gap-4 grid-cols-12 my-5 ">
        <div className="xl:col-span-12 col-span-12 space-y-4 bg-[#E4EAF1]">
          <div className="xl:col-span-12 col-span-12 space-y-4 bg-[#E4EAF1]">
            <div className="flex justify-between items-center my-2">
              <div className="px-5">
                <span className="font-semibold text-2xl text-[#172554]">
                  Support Details
                </span>
              </div>
              <div className="flex gap-3 me-5">
                <div>
                  <Tooltip
                    content="Back to Support Ticket List"
                    placement="top"
                    arrow
                    animation="scale"
                  >
                    <div
                      className="m-1 border border-slate-500 p-3 shadow-md"
                      onClick={() => navigate(-1)}
                    >
                      <Icon
                        icon="ic:round-arrow-back"
                        className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                      />
                    </div>
                  </Tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid gap-4 grid-cols-1 md:grid-cols-3  my-8">
        <div className="col-span-2 space-y-4">
          <div className="items-center my-auto bg-white p-4 rounded-lg shadow-lg min-h-[122px]">
            <span className="text-[#333333] md:text-2xl sm:text-base font-semibold">
              Support Name:{" "}
            </span>
            <span className="text-[#333333] md:text-2xl sm:text-base font-semibold">
              {supportData?.name}
            </span>
            <div className="text-[#797979] md:text-base sm:text-base w-full ">
              {supportData?.task ? supportData?.task : "N/A"}
            </div>
          </div>
        </div>
        <div className="col-span-1">
          <div className="justify-end space-y-3 border border-slate-200 px-7 py-5 relative bg-white p-4 rounded-lg shadow-lg">
            <div className="absolute top-0 right-0">
              {(supportData?.status === "completed" ||
                supportData?.status === "cancelled") &&
                supportData?.expense_status === "closed" && (
                  // (supportData?.advance_payment > 0 || supportData?.total_expense > 0) &&
                  <Button
                    className="bg-primary-500 text-white font-semibold p-2 px-4 rounded-bl-lg rounded-tr-lg rounded-none"
                    onClick={() => handleModal()}
                  >
                    Adjust Expense
                  </Button>
                )}
            </div>
            <div className="text-[#1F1D1D] md:text-2xl xm:base font-semibold">
              Deadline:
            </div>
            <div className="flex space-x-2">
              <Icon
                icon="simple-line-icons:calender"
                className="w-6 h-6 text-[#FF4B4B] text-xl"
              />
              <div className="text-[#696969] text-base font-medium">
                {formatDate(supportData?.deadline)}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="grid md:grid-cols-6 lg:grid-cols-6 grid-cols-1 gap-3">
        <div className="col-span-2 bg-white p-4 rounded-lg shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="bg-gray-50 p-4 rounded-lg shadow-md">
              <div className="flex items-center gap-2">
                <Icon
                  icon="lets-icons:setting-line"
                  className="text-blue-600 text-lg"
                />
                <h4 className="font-semibold text-gray-800 text-sm">
                  Support Type
                </h4>
              </div>
              <div className="mt-3 bg-gray-100 rounded-md px-4 py-3">
                <span className="text-gray-700 text-sm">
                  {supportData?.support_type?.name}
                </span>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg shadow-md">
              <div className="flex items-center gap-2">
                <Icon
                  icon="mingcute:task-line"
                  className="text-blue-600 text-lg"
                />
                <h4 className="font-semibold text-gray-800 text-sm">
                  Task Status
                </h4>
              </div>
              <div className="mt-3 bg-gray-100 rounded-md px- py-3">
                <Badge
                  className={`font-bold text-sm ${getStatusColor(
                    supportData?.status
                  )}`}
                >
                  <div className="flex items-center gap-1">
                    {getStatusIcon(supportData?.status)}
                    {getStatusText(supportData?.status)}
                  </div>
                </Badge>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg shadow-md">
              <div className="flex items-center gap-2">
                <Icon
                  icon="iconamoon:profile-thin"
                  className="text-blue-600 text-lg"
                />
                <h4 className="font-semibold text-gray-800 text-sm">
                  Assigned Employee
                </h4>
              </div>
              <div className="mt-3 bg-gray-100 rounded-md px-4 py-3">
                <span className="flex flex-wrap gap-2 text-gray-700 text-sm items-center">
                  <img
                    src={
                      assignedUserNames?.image
                        ? `${import.meta.env.VITE_MEDIA_URL}/${
                            assignedUserNames?.image
                          }`
                        : NoImage
                    }
                    alt={assignedUserNames?.name || "Default User"}
                    className="w-8 h-8 rounded-full"
                  />
                  {assignedUserNames?.name}
                </span>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg shadow-md">
              <div className="flex items-center gap-2">
                <Icon
                  icon="mdi-light:clipboard-text"
                  className="text-blue-600 text-lg"
                />
                <h4 className="font-semibold text-gray-800 text-sm">
                  Customer Invoice
                </h4>
              </div>
              <div className="mt-3 text-gray-700 text-sm space-y-2">
                <p>Invoice No: {SaleData?.invoice_no}</p>
                <p>Paid Amount: {SaleData?.paid_amount}</p>
                <p>Payment Status: {SaleData?.payment_status}</p>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg shadow-md">
              <div className="flex items-center gap-2">
                <Icon
                  icon="ic:baseline-phone"
                  className="text-blue-600 text-lg"
                />
                <h4 className="font-semibold text-gray-800 text-sm">
                  Contact Info
                </h4>
              </div>
              <div className="mt-3 text-gray-700 text-sm">
                <p>{ClientData?.phone}</p>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg shadow-md">
              <div className="flex items-center gap-2">
                <Icon
                  icon="gridicons:location"
                  className="text-blue-600 text-lg"
                />
                <h4 className="font-semibold text-gray-800 text-sm">Address</h4>
              </div>
              <div className="mt-3 text-gray-700 text-sm space-y-2">
                <p>Permanent: {ClientData?.address}</p>
                <p>Present: {ClientData?.shipping_address}</p>
              </div>
            </div>
          </div>
          {supportData?.support_attachments?.length > 0 && (
            <div className="col-span-3 bg-white p-4 rounded-lg shadow-lg mt-3">
              <h6 className="my-3 text-lg font-semibold">
                Support Attachments
              </h6>
              <div className="grid grid-cols-3 gap-4">
                {supportData?.support_attachments?.map((item, index) => (
                  <div
                    key={index}
                    className="relative w-full aspect-square overflow-hidden rounded-lg border shadow-sm"
                  >
                    <img
                      src={`${import.meta.env.VITE_MEDIA_URL}/${
                        item?.attachment
                      }`}
                      alt={item.attachment}
                      className="absolute inset-0 w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="col-span-2 bg-white py-4 rounded-lg shadow-md">
          {supportData &&
            supportData.expense_status === "on_going" &&
            !restrictedStatuses.includes(supportData.status) && (
              <div className="flex justify-end mr-3 mb-3">
                <button
                  onClick={() => handleConfirmModalOpen(supportData?.id)}
                  className="bg-red-400 hover:bg-red-400 text-white p-2 rounded-md flex items-center gap-2 shadow-md transition duration-300"
                >
                  <div className="flex items-center gap-1">
                    <Icon icon="ep:finished" className="text-xl text-white" />
                    <span>Close Expense</span>
                  </div>
                </button>
              </div>
            )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-2">
            <div className="bg-[#F9F9F9] p-2 py-5 rounded-md shadow-md">
              <div className="flex gap-2">
                <div className="p-2 bg-[#E8F0FF] h-8 text-[#6082BC] font-bold">
                  <Icon icon="iconoir:wallet" />
                </div>
                <div>
                  <h6 className="text-[#6F7486] text-[10px] font-medium">
                    Advance
                  </h6>
                  <h6 className="text-[#6082BC] font-semibold mt-2">
                    {supportData?.advance_payment}
                  </h6>
                </div>
              </div>
            </div>
            <div className="bg-[#F9F9F9] p-2 py-5 rounded-md shadow-md">
              <div className="flex gap-2">
                <div className="p-2 bg-[#F0E4FC] h-8 text-[#956CBF] font-bold">
                  <Icon icon="arcticons:expense-manager" />
                </div>
                <div>
                  <h6 className="text-[#6F7486] text-[10px] font-medium">
                    Expense
                  </h6>
                  <h6 className="text-[#956CBF] font-semibold mt-2">
                    {supportData?.total_expense}
                  </h6>
                </div>
              </div>
            </div>
            <div className="bg-[#F9F9F9] p-2 py-5 rounded-md shadow-md">
              <div className="flex gap-2">
                <div className="p-2 bg-[#DEEFE4] h-8 text-[#59BA7B] font-bold">
                  <Icon icon="material-symbols-light:assignment-return-outline" />
                </div>
                <div>
                  <h6 className="text-[#6F7486] text-[10px] font-medium">
                    Need To Pay
                  </h6>
                  <h6 className="text-[#59BA7B] font-semibold mt-2">
                    {supportData?.total_expense -
                      supportData?.advance_payment >=
                    0
                      ? supportData?.total_expense -
                        supportData?.advance_payment
                      : 0}
                  </h6>
                </div>
              </div>
            </div>
            <div className="bg-[#F9F9F9] p-2 py-5 rounded-md shadow-md">
              <div className="flex gap-2">
                <div className="p-2 bg-[#FEEBEA] h-8 text-[#BB5753] font-bold">
                  <Icon icon="bi:collection" />
                </div>
                <div>
                  <h6 className="text-[#6F7486] text-[10px] font-medium">
                    Re-collect Amount
                  </h6>
                  <h6 className="text-[#BB5753] font-semibold mt-2">
                    {supportData?.advance_payment -
                      supportData?.total_expense >=
                    0
                      ? supportData?.advance_payment -
                        supportData?.total_expense
                      : 0}
                  </h6>
                </div>
              </div>
            </div>
          </div>
          {supportData?.due_payment_received > 0 && (
            <Badge
              className={`bg-green-300 text-black flex justify-center mt-5 p-3 mx-1`}
            >
              {supportData?.due_payment_received > 0 && (
                <span className="md:text-base sm:text-xs text-pretty">
                  Refund Paid ৳{supportData?.due_payment_received} Taka (Expense
                  Adjusted)
                </span>
              )}
            </Badge>
          )}
          {supportData?.recollect_received > 0 && (
            <Badge
              className={`bg-green-300 text-black flex justify-center mt-5 p-3 mx-3`}
            >
              {supportData?.recollect_received > 0 && (
                <span className="md:text-base sm:text-xs text-pretty">
                  {" "}
                  Received ৳{supportData?.recollect_received} Taka (Expense
                  Adjusted)
                </span>
              )}
            </Badge>
          )}
          <div className="mt-4">
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border border-gray-300 rounded-lg">
                <thead className="bg-[#E4EAF1]">
                  <tr>
                    <th className="px-4 py-2 text-left text-gray-900 font-semibold border-b text-sm">
                      Expense Head
                    </th>
                    <th className="px-4 py-2 text-right text-gray-900 font-semibold border-b text-sm">
                      Amount
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {ExpenseData?.map((expense, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-4 py-2  border-b">
                        <h6 className="text-[14px] font-semibold text-black">
                          {expense?.expense_head?.name}
                        </h6>
                        <p>{expense?.description}</p>
                      </td>
                      <td className="px-4 py-2 text-right text-gray-800 border-b font-semibold">
                        {expense.amount}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div className="col-span-2 bg-white p-2 rounded-lg shadow-md w-full max-w-md">
          <h2 className="text-xl font-semibold mb-4">Support Timeline</h2>

          <div className="relative">
            <div className="absolute left-[14px] top-6 bottom-0 w-0.5 bg-gray-200"></div>

            <div className="space-y-8">
              {HistoryData.map((item, index) => {
                const { bgColor, dotColor, textColor, cardBgColor } =
                  getStatusStyles(item.trigger_type);
                const { user } = item;
                return (
                  <div key={item.id} className="relative flex gap-4">
                    <div className={`flex-1 ${cardBgColor} rounded-lg p-2`}>
                      <div className="flex justify-between items-start mb-1">
                        <span
                          className={`font-medium text-[14px] ${textColor}`}
                        >
                          {item.trigger_type.charAt(0).toUpperCase() +
                            item.trigger_type.slice(1)}
                        </span>
                        <div className="flex items-center text-gray-500 text-sm">
                          <span className="flex gap-1 items-center text-[14px]">
                            <Icon icon="simple-line-icons:calender" />{" "}
                            {new Date(item.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <p className="font-normal text-[12px] p-0 mb-1 text-[#505359]">
                        {item.description}
                      </p>
                      <div className="flex gap-3 items-center">
                        <img
                          src={
                            user?.image
                              ? `${import.meta.env.VITE_MEDIA_URL}/${
                                  user?.image
                                }`
                              : NoImage
                          }
                          alt={user?.name || "Default User"}
                          className="w-6 h-6 rounded-full"
                        />
                        <p className="text-gray-600 text-[12px]">
                          {user?.name || "Unknown User"}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      <ConfirmationModal
        isOpen={isConfirmModalOpen}
        onClose={handleConfirmModalClose}
        onConfirm={() => handleCloseExpense(selectedId)}
        message="Are you sure you want to close this expense?"
        icon="mdi:alert-circle-outline"
      />

      {modal && (
        <AdjustBalance
          supportData={supportData}
          activeModal={modal}
          onClose={handleCloseModal}
        />
      )}
    </>
  );
};

export default Details;
