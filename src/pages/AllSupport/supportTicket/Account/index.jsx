import BasicTablePage from "@/components/partials/common-table/table-basic";
import Badge from "@/components/ui/Badge";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";
import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import DeleteSupportAccount from "./DeleteAccountSupport";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";

const index = () => {
  const { id, support_id } = useParams();
  const [apiParam, setApiParam] = useState(0);
  const [filter, setFilter] = useState("");
  const allSupport = useGetApiQuery({
    url: `expense-payments?support_id=${support_id}`,
    params: apiParam,
  });

  const navigate = useNavigate();

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteData, setDeleteData] = useState(null);

  const changePage = (value) => {
    setApiParam(value);
  };

  const columns = [
    { label: "Payment Type", field: "payment_type" },
    { label: "Payment Method", field: "payment_method" },
    { label: "Paid Amount", field: "paid_amount" },
    { label: "Action", field: "" },
  ];

  const actions = [
    {
      name: "View",
      icon: "lets-icons:view-alt",
      onClick: (val) => {
        {
          console.log(val, "hello world");
        }
        navigate(
          `/details-support-account/${allSupport?.data?.data[val]?.id}/${support_id}`,
          { state: { support: allSupport?.data?.data[val] } }
        );
      },
    },
    {
      name: "Edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        navigate(
          `/edit-support-account/${allSupport?.data?.data[val]?.id}/${support_id}`,
          { state: { support: allSupport?.data?.data[val] } }
        );
      },
    },

    {
      name: "Delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        setDeleteData(allSupport?.data.data[val]);
        setShowDeleteModal(true);
      },
    },
  ];

  const tableData = allSupport?.data?.data?.map((item, index) => {
    return {
      id: item.id,

      // payment_type: (
      //   <span
      //     className={`${
      //       item.payment_type.toLowerCase() === "prepaid"
      //         ? "bg-success-500"
      //         : "bg-red-500"
      //     } py-1 px-2 rounded-lg font-bold text-base uppercase text-slate-50`}
      //   >
      //     {item.payment_type.toLowerCase() === "prepaid" ? "Advance" : "Due"}
      //   </span>
      // ),

      payment_type: (
        <span
          className={`${
            item?.payment_type.toLowerCase() === "prepaid"
              ? "bg-success-300 text-success-700"
              : item?.payment_type.toLowerCase() === "recollect"
              ? "bg-purple-300 text-purple-700"
              : "bg-red-200 text-red-500"
          } py-1 px-2 rounded-lg font-bold text-base uppercase text-slate-50`}
        >
          {item?.payment_type.toLowerCase() === "prepaid"
            ? "Advance"
            : item?.payment_type.toLowerCase() === "recollect"
            ? "Recollect"
            : "Due"}
        </span>
      ),

      payment_method: (
        <div className="space-y-2">
          <span className="text-bold text-base text-slate-900">
            {item.payment_method}
          </span>
          <br />
          <span className="text-sm text-slate-500">
            <span className="text-danger-600 me-1 font-bold">Tran ID:</span>
            {item.transaction_id || "N/A"}
          </span>
        </div>
      ),

      paid_amount: item.paid_amount,
    };
  });

  return (
    <div>
      <BasicTablePage
        title="Account List"
        columns={columns}
        actions={actions}
        goto={
          <div className="flex items-center gap-2">
            <Icon
              icon="oui:ml-create-single-metric-job"
              className="text-white font-bold"
            />
            Advance pay
          </div>
        }
        gotoLink={`/create-support-account/${support_id}`}
        BackButton={
          <div className="flex justify-center items-center">
            <Tooltip
              content="Back to Support Ticket List"
              placement="top"
              arrow
              animation="Interactive"
            >
              <div
                className="border border-primary-300 hover:border-primary-500 p-2 text-primary-300 hover:text-primary-500 rounded-md"
                onClick={() => navigate(`/support-list`)}
              >
                <Icon icon="heroicons-outline:arrow-left" />
              </div>
            </Tooltip>
          </div>
        }
        changePage={changePage}
        filter={filter}
        setFilter={setApiParam}
        data={tableData}
        currentPage={allSupport?.data?.current_page}
        totalPages={Math.ceil(
          allSupport?.data?.total / allSupport?.data?.per_page
        )}
      />
      <DeleteSupportAccount
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </div>
  );
};

export default index;
