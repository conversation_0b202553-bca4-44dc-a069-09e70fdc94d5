import React from "react";
import Card from "@/components/ui/Card";
import {
  useGetApiWithIdQuery,
  useUpdateApiMutation,
} from "@/store/api/apihandler/commonSlice";
import { Form, Formik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";
import { validationSchema } from "./formSetting";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import InputSelect from "@/components/ui/form/InputSelect";

const edit = () => {
  const { id, accountId } = useParams();
  const navigate = useNavigate();

  // Fetch the support data using the ID
  const { data: support, isLoading } = useGetApiWithIdQuery([
    "expense-payments",
    id,
  ]);

  const [updateApi] = useUpdateApiMutation();

  // Payment Method Options
  const unitPayment = [
    { value: "Cash", label: "Cash" },
    { value: "BankTransfer", label: "Bank Transfer" },
    { value: "Cheque", label: "Cheque" },
    { value: "OnlinePayment", label: "Online Payment" },
    { value: "Bkash", label: "Bkash" },
    { value: "Ucash", label: "Ucash" },
    { value: "Nagad", label: "Nagad" },
    { value: "Rocket", label: "Rocket" },
    { value: "MobileBanking", label: "Mobile Banking" },
    { value: "AgentBanking", label: "Agent Banking" },
    { value: "Others", label: "Others" },
  ];

  // Initial values populated dynamically with the fetched data
  const initialValues = {
    description: support?.description || "",
    payment_type: support?.payment_type || "",
    payment_method: support?.payment_method || "",
    paid_amount: support?.paid_amount || "",
    transaction_id: support?.transaction_id || "",
    is_active: support?.is_active ? true : false,
  };

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        // Append the image field to formData
        if (values.image instanceof File) {
          formData.append(key, values.image);
        }
      } else if (key === "is_active") {
        // Convert boolean to integer (1 or 0)
        formData.append(key, values.is_active ? 1 : 0);
      } else {
        formData.append(key, values[key]);
      }
    });

    try {
      const data = {
        end_point: `expense-payments/${id}`,
        body: formData,
      };

      const response = await updateApi(data).unwrap();
      resetForm();
      toast.success("Advance Pay updated successfully Created!");
      navigate(`/support-account-list/${accountId}`);
    } catch (err) {
      toast.error("Failed to Update Advance Pay. Please try again.");
    }
  };

  // Handle loading and form display
  if (isLoading) {
    return <p>Loading...</p>;
  }

  return (
    <div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, values, setFieldValue }) => (
          <Form>
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-bold text-lg">
                        Edit{" "}
                        <span className="text-primary-500">
                          {values.payment_method}
                        </span>
                      </span>
                    </div>
                    <div>
                      <Tooltip
                        content="Back to Support Account List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() =>
                            navigate(`/support-account-list/${accountId}`)
                          }
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </Card>
              </div>
            </div>

            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-8 col-span-12 hidden md:block">
                <div className="grid gap-4">
                  <Card>
                    <>
                      <span className="font-bold text-slate-600 my-4">
                        Payment Method:
                      </span>
                      <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                        <span className="flex items-center text-slate-700">
                          {values.payment_method
                            ? unitPayment.find(
                                (type) => type.value === values.payment_method
                              )?.label
                            : "Untitled Payment Method"}
                        </span>
                      </div>
                    </>
                  </Card>
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <>
                        <span className="font-bold text-slate-600 my-4">
                          Paid Amount:
                        </span>
                        <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                          <span className="flex items-center text-slate-700">
                            {values.paid_amount || "Untitled Paid Amount"}
                          </span>
                        </div>
                      </>
                    </Card>
                    <Card>
                      <>
                        <span className="font-bold text-slate-600 my-4">
                          Transaction ID:
                        </span>
                        <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                          <span className="flex items-center text-slate-700">
                            {values.transaction_id || "N/A"}
                          </span>
                        </div>
                      </>
                    </Card>
                  </div>

                  <Card>
                    <>
                      <span className="font-bold text-slate-600 my-4">
                        Description:
                      </span>
                      <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                        <span className="flex items-center text-slate-700">
                          {values.description}
                        </span>
                      </div>
                    </>
                  </Card>
                </div>
              </div>

              <div className="xl:col-span-4 col-span-12 gap-5">
                <Card>
                  <div className="grid grid-cols-1">
                    <InputSelect
                      label="Payment Method"
                      id="payment_method"
                      name="payment_method"
                      value={values.payment_method}
                      onChange={(e) =>
                        setFieldValue("payment_method", e.target.value)
                      }
                      options={unitPayment}
                      placeholder="Select Payment Method"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-1 my-3">
                    <InputField
                      label="Paid Amount"
                      name="paid_amount"
                      type="text"
                      placeholder="Enter Paid Amount"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-1 my-3">
                    <InputField
                      label="Transaction ID"
                      name="transaction_id"
                      type="text"
                      placeholder="Enter Transaction ID"
                    />
                  </div>
                  <div className="grid grid-cols-1 my-3">
                    <TextAreaField
                      label="Description"
                      name="description"
                      type="text"
                      placeholder="Enter Description"
                    />
                  </div>
                </Card>
              </div>
            </div>

            {/* Submit and Cancel Buttons */}
            <div className="grid gap-4 grid-cols-12 mt-5">
              <div className="xl:col-span-12 col-span-12 space-y-4">
                <Card>
                  <div className="flex justify-end items-center gap-4">
                    <Button
                      type="button"
                      className="btn text-center btn-danger"
                      // onClick={() => navigate("/support-account-list")}
                      onClick={() =>
                        navigate(`/support-account-list/${accountId}`)
                      }
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="btn text-center btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Updating..." : "Update"}
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default edit;
