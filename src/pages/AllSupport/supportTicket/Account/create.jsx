import React from "react";
import Card from "@/components/ui/Card";
import { usePostApiMutation } from "@/store/api/apihandler/commonSlice";
import { Form, Formik, ErrorMessage } from "formik";
import { useParams } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import InputField from "@/components/ui/form/InputField";
import Text<PERSON>reaField from "@/components/ui/form/TextAreaField";
import InputSelect from "@/components/ui/form/InputSelect";
import Button from "@/components/ui/Button";
import Tooltip from "@/components/ui/Tooltip";
import Icon from "@/components/ui/Icon";
import { initialValues, validationSchema } from "./formSetting";
import { toast } from "react-toastify";

const create = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [postApi] = usePostApiMutation();

  // Payment Method Options
  const unitPayment = [
    { value: "Cash", label: "Cash" },
    { value: "BankTransfer", label: "Bank Transfer" },
    { value: "Cheque", label: "Cheque" },
    { value: "OnlinePayment", label: "Online Payment" },
    { value: "Bkash", label: "Bkash" },
    { value: "Ucash", label: "Ucash" },
    { value: "Nagad", label: "Nagad" },
    { value: "Rocket", label: "Rocket" },
    { value: "MobileBanking", label: "Mobile Banking" },
    { value: "AgentBanking", label: "Agent Banking" },
    { value: "Others", label: "Others" },
  ];

  // Form submission handler
  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();

    formData.append("description", values.description);
    formData.append("is_active", 1);
    formData.append("support_id", id);
    formData.append("payment_method", values.payment_method);
    formData.append("payment_type", "prepaid");
    formData.append("paid_amount", values.paid_amount);
    formData.append("transaction_id", values.transaction_id);

    try {
      const response = await postApi({
        end_point: "expense-payments",
        body: formData,
      }).unwrap();
      toast.success("Advance Pay Successfully Created!");
      navigate(`/support-account-list/${id}`);
      resetForm();
    } catch (err) {
      toast.error("Submission failed. Please try again.");
    }
  };

  const headerSlotContent = (
    <Tooltip
      content="Back to Account List"
      placement="top"
      arrow
      animation="Interactive"
    >
      <div
        className="m-1"
        onClick={() => navigate(`/support-account-list/${id}`)}
      >
        <Icon
          icon="ion:arrow-back"
          className="w-6 h-6 text-gray-500 cursor-pointer hover:text-primary-500 m-1 hover:border-primary-500"
        />
      </div>
    </Tooltip>
  );

  return (
    <>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ setFieldValue, values, isSubmitting }) => (
          <Form>
            <Card
              headerslot={headerSlotContent}
              title="Add New Advance Pay"
              className="w-full"
              titleClass="text-lg font-bold text-gray-800"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <InputSelect
                    label="Payment Method"
                    id="payment_method"
                    name="payment_method"
                    value={values.payment_method}
                    onChange={(e) =>
                      setFieldValue("payment_method", e.target.value)
                    }
                    options={unitPayment}
                    placeholder="Select Payment Method"
                    required
                  />
                  <ErrorMessage
                    name="payment_method"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>
                <InputField
                  label="Paid Amount"
                  name="paid_amount"
                  type="text"
                  placeholder="Enter Paid Amount"
                  required
                />
                <InputField
                  label="Transaction ID"
                  name="transaction_id"
                  type="text"
                  placeholder="Enter Transaction ID"
                />
              </div>
              <div className="my-4">
                <TextAreaField
                  label="Description"
                  name="description"
                  type="text"
                  placeholder="Enter Description"
                />
              </div>

              {/* Submit and Cancel Buttons */}
              <div className="flex justify-end mt-5 mb-2 gap-3">
                <Button
                  type="button"
                  className="btn text-center btn-danger"
                  onClick={() => navigate(`/support-account-list/${id}`)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="btn text-center btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </Card>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default create;
