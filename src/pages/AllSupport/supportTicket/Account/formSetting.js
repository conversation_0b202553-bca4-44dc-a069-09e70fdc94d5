import * as yup from "yup";

// Initial values for form fields
export const initialValues = {
    description: "",
    is_active: true,
    payment_method: "",
    paid_amount: "",
    transaction_id: "",
};

// Validation schema for form fields
export const validationSchema = yup.object({
    payment_method: yup
        .string()
        .required("Payment Method is Required"),
    paid_amount: yup
        .number()
        .required("Paid Amount is Required")
        .positive("Paid Amount must be a Positive Number")
        .typeError("Paid Amount must be a Valid Number"),
});
