import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import Badge from "@/components/ui/Badge";
import Tooltip from "@/components/ui/Tooltip";
import { useGetApiQuery } from "@/store/api/apihandler/commonSlice";

const details = () => {
  const { id, accountId } = useParams();
  const navigate = useNavigate();

  // Fetch data from API
  const { data: supportData, isLoading } = useGetApiQuery({
    url: `expense-payments/${id}`,
  });

  if (isLoading) return <p>Loading...</p>;

  return (
    <>
      <div className="grid gap-4 grid-cols-12 my-5">
        <div className="xl:col-span-12 col-span-12 space-y-4">
          <Card>
            <div className="xl:col-span-12 col-span-12 space-y-4">
              <Card>
                <div className="flex justify-between items-center">
                  <div>
                    <span className="font-bold text-lg">Account Details</span>
                  </div>
                  <div className="flex gap-3">
                    <div>
                      <Tooltip
                        content="Back to Account List"
                        placement="top"
                        arrow
                        animation="scale"
                      >
                        <div
                          className="m-1"
                          onClick={() =>
                            navigate(`/support-account-list/${accountId}`)
                          }
                        >
                          <Icon
                            icon="ic:round-arrow-back"
                            className="w-6 h-6 text-primary-400 cursor-pointer hover:text-primary-600 m-1"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </Card>
        </div>
      </div>
      <div className="grid grid-cols-12 gap-5 mb-5">
        <div className="col-span-12">
          <Card>
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2 mt-5">
              <Card className="shadow-base2">
                <span className="font-bold text-slate-600 my-4">
                  Payment Type :
                </span>

                <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                  <span
                    className={`${
                      supportData?.payment_type.toLowerCase() === "prepaid"
                        ? "bg-success-200 text-success-500"
                        : supportData?.payment_type.toLowerCase() ===
                          "recollect"
                        ? "bg-purple-300 text-purple-700"
                        : "bg-red-200 text-red-500"
                    } py-1 px-2 rounded-lg font-bold text-base uppercase text-slate-50`}
                  >
                    {supportData?.payment_type.toLowerCase() === "prepaid"
                      ? "Advance"
                      : supportData?.payment_type.toLowerCase() === "recollect"
                      ? "Recollect"
                      : "Due"}
                  </span>
                </div>
              </Card>
              <Card className="shadow-base2">
                <span className="font-bold text-slate-600 my-4">
                  Payment Method:
                </span>
                <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                  <span className="flex items-center text-slate-700">
                    {supportData?.payment_method}
                  </span>
                </div>
              </Card>
              <Card className="shadow-base2">
                <span className="font-bold text-slate-600 my-4">
                  Paid Amount:
                </span>
                <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                  <span className="flex items-center text-slate-700">
                    {supportData?.paid_amount}
                  </span>
                </div>
              </Card>
              <Card className="shadow-base2">
                <span className="font-bold text-slate-600 my-4">
                  Transaction ID:
                </span>
                <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                  <span className="flex items-center text-slate-700">
                    {supportData?.transaction_id || "N/A"}
                  </span>
                </div>
              </Card>
            </div>
            <div className="grid gap-4 grid-cols-1 mt-5">
              <Card className="shadow-base2">
                <span className="font-bold text-slate-600 my-4">
                  Description:
                </span>
                <div className="justify-start items-start bg-slate-100 rounded-lg px-3 py-2 my-2">
                  <span className="flex items-center text-slate-700 text-justify mx-3">
                    {supportData?.description}
                  </span>
                </div>
              </Card>
            </div>
          </Card>
        </div>
      </div>
    </>
  );
};

export default details;
