.dashcode-app {
  .react-calendar {
    @apply w-full bg-white border-none;
  }

  .react-calendar__navigation__prev2-button {
    @apply hidden;
  }
  .react-calendar__navigation__next2-button {
    @apply hidden;
  }
  .react-calendar__navigation__label {
    @apply text-slate-900  text-2xl;
  }
  .react-calendar__navigation button:enabled:hover,
  .react-calendar__navigation button:enabled:focus {
    @apply bg-transparent;
  }
  .react-calendar__navigation {
    @apply mb-2;
  }
  .react-calendar__navigation__arrow {
    @apply text-4xl;
  }
  .react-calendar__month-view__weekdays__weekday {
    @apply text-sm font-normal text-slate-600  capitalize p-0 mb-2;
    abbr {
      @apply no-underline;
    }
    .react-calendar__tile {
      @apply text-slate-600;
      &.react-calendar__month-view__days__day--weekend {
        @apply text-danger-500;
      }
    }
  }
  .react-calendar__tile {
    @apply text-sm font-normal  capitalize w-8 h-8 flex flex-col  items-center  justify-center  rounded;
  }
  &.dark {
    .react-calendar {
      @apply bg-slate-800;
    }
    .react-calendar__navigation__label {
      @apply text-slate-300;
    }
    .react-calendar__month-view__weekdays__weekday {
      @apply text-slate-300;
    }
    .react-calendar__tile {
      @apply text-slate-400;
      &.react-calendar__month-view__days__day--weekend {
        @apply text-danger-500;
      }
    }
    .react-calendar__tile:enabled:hover,
    .react-calendar__tile:enabled:focus {
      @apply bg-slate-700;
    }
  }
  .react-calendar__tile--active {
    @apply bg-slate-900 text-white;
  }
}
